'use client';

import { useState } from 'react';
import {
  MoreVertical,
  Edit3,
  Copy,
  Trash2,
  Info,
  Download,
  Share,
  FileText,
  FolderOpen,
  History,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import type { File } from '@/types';

interface FileContextMenuProps {
  file: File;
  isOpen: boolean;
  position: { x: number; y: number };
  onClose: () => void;
  onRename: (file: File) => void;
  onDelete: (file: File) => void;
  onDuplicate: (file: File) => void;
  onShowProperties: (file: File) => void;
  onExport?: (file: File) => void;
  onMoveToWorkspace?: (file: File) => void;
  className?: string;
}

export function FileContextMenu({
  file,
  isOpen,
  position,
  onClose,
  onRename,
  onDelete,
  onDuplicate,
  onShowProperties,
  onExport,
  onMoveToWorkspace,
  className,
}: FileContextMenuProps) {
  const [isVisible, setIsVisible] = useState(false);

  // Handle backdrop click to close menu
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleMenuAction = (action: () => void) => {
    action();
    onClose();
  };

  if (!isOpen) return null;

  const menuItems = [
    {
      icon: Edit3,
      label: 'Rename',
      action: () => handleMenuAction(() => onRename(file)),
      shortcut: 'F2',
    },
    {
      icon: Copy,
      label: 'Duplicate',
      action: () => handleMenuAction(() => onDuplicate(file)),
      shortcut: 'Ctrl+D',
    },
    {
      icon: Download,
      label: 'Export',
      action: () => handleMenuAction(() => onExport?.(file)),
      shortcut: 'Ctrl+E',
      disabled: !onExport,
    },
    { type: 'separator' },
    {
      icon: FolderOpen,
      label: 'Move to Workspace',
      action: () => handleMenuAction(() => onMoveToWorkspace?.(file)),
      disabled: !onMoveToWorkspace,
    },
    { type: 'separator' },
    {
      icon: Info,
      label: 'Properties',
      action: () => handleMenuAction(() => onShowProperties(file)),
      shortcut: 'Alt+Enter',
    },
    { type: 'separator' },
    {
      icon: Trash2,
      label: 'Delete',
      action: () => handleMenuAction(() => onDelete(file)),
      shortcut: 'Del',
      destructive: true,
    },
  ] as const;

  return (
    <div
      className="fixed inset-0 z-50"
      onClick={handleBackdropClick}
      onContextMenu={(e) => e.preventDefault()}
    >
      <div
        className={cn(
          'absolute min-w-[200px] bg-card border border-border rounded-lg shadow-lg py-2',
          'animate-in fade-in-0 zoom-in-95 duration-200',
          className
        )}
        style={{
          left: Math.min(position.x, window.innerWidth - 220),
          top: Math.min(position.y, window.innerHeight - 300),
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* File header */}
        <div className="px-3 py-2 border-b border-border">
          <div className="flex items-center gap-2">
            <FileText className="h-4 w-4 text-muted-foreground" />
            <span className="font-medium text-sm truncate">{file.name}</span>
          </div>
          <div className="text-xs text-muted-foreground mt-1">
            {file.language.toUpperCase()}
            {file.size && ` • ${Math.round(file.size / 1024)}KB`}
          </div>
        </div>

        {/* Menu items */}
        <div className="py-1">
          {menuItems.map((item, index) => {
            if (item.type === 'separator') {
              return <div key={index} className="my-1 h-px bg-border" />;
            }

            const Icon = item.icon;
            return (
              <button
                key={index}
                onClick={item.action}
                disabled={item.disabled}
                className={cn(
                  'flex items-center gap-3 px-3 py-2 text-sm w-full',
                  'hover:bg-accent hover:text-accent-foreground',
                  'disabled:opacity-50 disabled:cursor-not-allowed',
                  'focus:bg-accent focus:text-accent-foreground focus:outline-none',
                  item.destructive && 'text-destructive hover:text-destructive-foreground hover:bg-destructive/10'
                )}
              >
                <Icon className="h-4 w-4 flex-shrink-0" />
                <span className="flex-1 text-left">{item.label}</span>
                {item.shortcut && (
                  <span className="text-xs text-muted-foreground font-mono">
                    {item.shortcut}
                  </span>
                )}
              </button>
            );
          })}
        </div>
      </div>
    </div>
  );
}

// Hook for managing context menu state
export function useFileContextMenu() {
  const [contextMenu, setContextMenu] = useState<{
    file: File | null;
    position: { x: number; y: number };
    isOpen: boolean;
  }>({
    file: null,
    position: { x: 0, y: 0 },
    isOpen: false,
  });

  const openContextMenu = (file: File, event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();
    
    setContextMenu({
      file,
      position: { x: event.clientX, y: event.clientY },
      isOpen: true,
    });
  };

  const closeContextMenu = () => {
    setContextMenu(prev => ({ ...prev, isOpen: false }));
  };

  return {
    contextMenu,
    openContextMenu,
    closeContextMenu,
  };
}