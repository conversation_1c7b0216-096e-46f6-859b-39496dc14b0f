'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Info,
  Calendar,
  Clock,
  HardDrive,
  Play,
  Eye,
  FileText,
  X,
  ExternalLink,
  Code2,
  Timer,
  Hash,
} from 'lucide-react';
import { cn, formatFileSize, formatRelativeTime, formatExactTime } from '@/lib/utils';
import type { File, PracticeSession } from '@/types';

interface FilePropertiesPanelProps {
  file: File;
  sessions?: PracticeSession[];
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

export function FilePropertiesPanel({ 
  file, 
  sessions = [], 
  isOpen, 
  onClose,
  className 
}: FilePropertiesPanelProps) {
  const [activeTab, setActiveTab] = useState<'details' | 'history' | 'stats'>('details');

  if (!isOpen) return null;

  const fileSessions = sessions.filter(session => session.fileId === file.$id);
  const totalExecutions = fileSessions.length;
  const totalDuration = fileSessions.reduce((sum, session) => sum + session.duration, 0);
  const avgDuration = totalExecutions > 0 ? totalDuration / totalExecutions : 0;

  const getLanguageBadgeColor = (language: string) => {
    switch (language.toLowerCase()) {
      case 'javascript': return 'bg-yellow-500/10 text-yellow-600 border-yellow-500/20';
      case 'typescript': return 'bg-blue-500/10 text-blue-600 border-blue-500/20';
      case 'python': return 'bg-green-500/10 text-green-600 border-green-500/20';
      case 'java': return 'bg-orange-500/10 text-orange-600 border-orange-500/20';
      case 'cpp':
      case 'c': return 'bg-blue-700/10 text-blue-700 border-blue-700/20';
      default: return 'bg-muted text-muted-foreground border-border';
    }
  };

  return (
    <div className={cn(
      'fixed inset-y-0 right-0 z-50 w-96 bg-card border-l border-border shadow-lg transform transition-transform duration-300 ease-in-out',
      isOpen ? 'translate-x-0' : 'translate-x-full',
      className
    )}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border bg-muted/20">
        <div className="flex items-center gap-2">
          <Info className="h-4 w-4 text-primary" />
          <h2 className="font-semibold">File Properties</h2>
        </div>
        <Button
          variant="ghost"
          size="icon"
          onClick={onClose}
          className="h-8 w-8"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* File Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-start gap-3">
          <div className="p-2 rounded-lg bg-primary/10">
            <FileText className="h-6 w-6 text-primary" />
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-lg truncate" title={file.name}>
              {file.name}
            </h3>
            <div className="flex items-center gap-2 mt-1">
              <Badge 
                variant="outline" 
                className={cn('text-xs', getLanguageBadgeColor(file.language))}
              >
                {file.language.toUpperCase()}
              </Badge>
              {file.isModified && (
                <Badge variant="outline" className="text-xs bg-warning/10 text-warning border-warning/20">
                  Modified
                </Badge>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-border">
        {(['details', 'history', 'stats'] as const).map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={cn(
              'px-4 py-2 text-sm font-medium transition-colors border-b-2',
              activeTab === tab
                ? 'text-primary border-primary'
                : 'text-muted-foreground border-transparent hover:text-foreground hover:border-muted'
            )}
          >
            {tab === 'details' && 'Details'}
            {tab === 'history' && `History (${fileSessions.length})`}
            {tab === 'stats' && 'Statistics'}
          </button>
        ))}
      </div>

      {/* Content */}
      <ScrollArea className="flex-1 h-[calc(100vh-200px)]">
        <div className="p-4">
          {activeTab === 'details' && (
            <div className="space-y-4">
              {/* Basic Info */}
              <div className="space-y-3">
                <h4 className="font-medium flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  File Information
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Size:</span>
                    <span className="font-mono">{formatFileSize(file.size)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Lines:</span>
                    <span className="font-mono">{file.content.split('\n').length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Characters:</span>
                    <span className="font-mono">{file.content.length}</span>
                  </div>
                </div>
              </div>

              {/* Timestamps */}
              <div className="space-y-3">
                <h4 className="font-medium flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  Timestamps
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex flex-col gap-1">
                    <span className="text-muted-foreground">Created:</span>
                    <span className="font-mono text-xs">{formatExactTime(file.$createdAt)}</span>
                    <span className="text-xs text-muted-foreground">
                      ({formatRelativeTime(file.$createdAt)})
                    </span>
                  </div>
                  <div className="flex flex-col gap-1">
                    <span className="text-muted-foreground">Modified:</span>
                    <span className="font-mono text-xs">{formatExactTime(file.$updatedAt)}</span>
                    <span className="text-xs text-muted-foreground">
                      ({formatRelativeTime(file.$updatedAt)})
                    </span>
                  </div>
                  {file.lastExecuted && (
                    <div className="flex flex-col gap-1">
                      <span className="text-muted-foreground">Last executed:</span>
                      <span className="font-mono text-xs">{formatExactTime(file.lastExecuted)}</span>
                      <span className="text-xs text-muted-foreground">
                        ({formatRelativeTime(file.lastExecuted)})
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'history' && (
            <div className="space-y-4">
              <h4 className="font-medium flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Execution History
              </h4>
              {fileSessions.length > 0 ? (
                <div className="space-y-2">
                  {fileSessions
                    .sort((a, b) => new Date(b.sessionDate).getTime() - new Date(a.sessionDate).getTime())
                    .map((session) => (
                    <div
                      key={session.$id}
                      className="flex items-center justify-between p-3 rounded-lg bg-muted/30 border border-border/50"
                    >
                      <div className="flex items-center gap-2">
                        <Play className="h-3 w-3 text-success" />
                        <div>
                          <div className="text-sm font-medium">
                            {formatExactTime(session.sessionDate)}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {formatRelativeTime(session.sessionDate)}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-mono">{session.duration}s</div>
                        <div className="text-xs text-muted-foreground">Duration</div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Play className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No execution history yet</p>
                  <p className="text-xs">Run this file to see history</p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'stats' && (
            <div className="space-y-4">
              <h4 className="font-medium flex items-center gap-2">
                <Hash className="h-4 w-4" />
                Statistics
              </h4>
              <div className="grid grid-cols-2 gap-4">
                <div className="p-3 rounded-lg bg-success/10 border border-success/20">
                  <div className="text-2xl font-bold text-success">{totalExecutions}</div>
                  <div className="text-sm text-success/80">Total Runs</div>
                </div>
                <div className="p-3 rounded-lg bg-primary/10 border border-primary/20">
                  <div className="text-2xl font-bold text-primary">{Math.round(avgDuration)}s</div>
                  <div className="text-sm text-primary/80">Avg Duration</div>
                </div>
                <div className="p-3 rounded-lg bg-warning/10 border border-warning/20">
                  <div className="text-2xl font-bold text-warning">{Math.round(totalDuration / 60)}m</div>
                  <div className="text-sm text-warning/80">Total Time</div>
                </div>
                <div className="p-3 rounded-lg bg-muted/50 border border-border">
                  <div className="text-2xl font-bold text-foreground">
                    {file.content.split('\n').filter(line => line.trim()).length}
                  </div>
                  <div className="text-sm text-muted-foreground">Code Lines</div>
                </div>
              </div>

              {/* Language-specific stats */}
              <div className="space-y-2">
                <h5 className="font-medium text-sm">Code Analysis</h5>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Total lines:</span>
                    <span className="font-mono">{file.content.split('\n').length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Blank lines:</span>
                    <span className="font-mono">
                      {file.content.split('\n').filter(line => !line.trim()).length}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Code lines:</span>
                    <span className="font-mono">
                      {file.content.split('\n').filter(line => line.trim()).length}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
}