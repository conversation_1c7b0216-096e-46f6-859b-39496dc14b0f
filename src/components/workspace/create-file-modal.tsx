'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { X } from 'lucide-react';
import { getTemplateNames } from '@/lib/code-templates';
import type { File } from '@/types';

interface CreateFileModalProps {
  isOpen: boolean;
  onClose: () => void;
  onFileCreate?: (data: {
    name: string;
    language: string;
    template?: string;
  }) => void;
}

const languageOptions = [
  { value: 'javascript', label: 'JavaScript (.js)', ext: '.js' },
  { value: 'typescript', label: 'TypeScript (.ts)', ext: '.ts' },
  { value: 'python', label: 'Python (.py)', ext: '.py' },
  { value: 'java', label: 'Java (.java)', ext: '.java' },
  { value: 'cpp', label: 'C++ (.cpp)', ext: '.cpp' },
  { value: 'go', label: 'Go (.go)', ext: '.go' },
  { value: 'rust', label: 'Rust (.rs)', ext: '.rs' },
  { value: 'markdown', label: 'Markdown (.md)', ext: '.md' },
  { value: 'text', label: 'Text (.txt)', ext: '.txt' },
] as const;

export function CreateFileModal({
  isOpen,
  onClose,
  onFileCreate,
}: CreateFileModalProps) {
  const [fileName, setFileName] = useState('');
  const [selectedLanguage, setSelectedLanguage] =
    useState<string>('javascript');
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!fileName.trim()) {
      setError('File name is required');
      return;
    }

    // Check if filename has extension, if not add the appropriate one
    const selectedLangOption = languageOptions.find(
      (lang) => lang.value === selectedLanguage
    );
    let finalFileName = fileName.trim();

    if (selectedLangOption && !finalFileName.includes('.')) {
      finalFileName += selectedLangOption.ext;
    }

    setLoading(true);

    try {
      onFileCreate?.({
        name: finalFileName,
        language: selectedLanguage,
        template: selectedTemplate,
      });

      // Reset form
      setFileName('');
      setSelectedLanguage('javascript');
      onClose();
    } catch (err: any) {
      setError(err.message || 'Failed to create file');
    } finally {
      setLoading(false);
    }
  };

  const handleLanguageChange = (language: string) => {
    setSelectedLanguage(language);
    setSelectedTemplate(''); // Reset template when language changes

    // Auto-update file extension if user hasn't added one
    const currentName = fileName.trim();
    if (currentName && !currentName.includes('.')) {
      const langOption = languageOptions.find(
        (lang) => lang.value === language
      );
      if (langOption) {
        setFileName(currentName + langOption.ext);
      }
    }
  };

  const availableTemplates = getTemplateNames(selectedLanguage);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-card border border-border rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">Create New File</h2>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {error && (
          <div className="p-3 text-sm text-destructive bg-destructive/10 border border-destructive/20 rounded-md mb-4">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label
              htmlFor="fileName"
              className="block text-sm font-medium mb-1"
            >
              File Name
            </label>
            <Input
              id="fileName"
              value={fileName}
              onChange={(e) => setFileName(e.target.value)}
              placeholder="e.g., solution.py, algorithm.js"
              required
              disabled={loading}
              autoFocus
            />
            <p className="text-xs text-muted-foreground mt-1">
              Extension will be added automatically if not provided
            </p>
          </div>

          <div>
            <label
              htmlFor="language"
              className="block text-sm font-medium mb-1"
            >
              Programming Language
            </label>
            <select
              id="language"
              value={selectedLanguage}
              onChange={(e) => handleLanguageChange(e.target.value)}
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              disabled={loading}
            >
              {languageOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {availableTemplates.length > 0 && (
            <div>
              <label
                htmlFor="template"
                className="block text-sm font-medium mb-1"
              >
                Code Template (Optional)
              </label>
              <select
                id="template"
                value={selectedTemplate}
                onChange={(e) => setSelectedTemplate(e.target.value)}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                disabled={loading}
              >
                <option value="">Empty file</option>
                {availableTemplates.map((template) => (
                  <option key={template} value={template}>
                    {template}
                  </option>
                ))}
              </select>
              <p className="text-xs text-muted-foreground mt-1">
                Choose a template to start with example code
              </p>
            </div>
          )}

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading || !fileName.trim()}>
              {loading ? 'Creating...' : 'Create File'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
