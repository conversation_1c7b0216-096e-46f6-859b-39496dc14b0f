'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Code,
  Clock,
  Trophy,
  Target,
  Zap,
  ChevronRight,
  Play,
} from 'lucide-react';

interface Challenge {
  id: string;
  title: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  category: string;
  description: string;
  timeEstimate: string;
  starterCode: string;
  testCases: string[];
}

const CODING_CHALLENGES: Challenge[] = [
  {
    id: 'two-sum',
    title: 'Two Sum',
    difficulty: 'Easy',
    category: 'Array',
    description:
      'Given an array of integers nums and an integer target, return indices of the two numbers such that they add up to target.',
    timeEstimate: '15 min',
    starterCode: `function twoSum(nums, target) {
  // Your solution here

}

// Test cases
console.log('Test 1:', twoSum([2, 7, 11, 15], 9)); // Expected: [0, 1]
console.log('Test 2:', twoSum([3, 2, 4], 6)); // Expected: [1, 2]
console.log('Test 3:', twoSum([3, 3], 6)); // Expected: [0, 1]`,
    testCases: [
      '[2, 7, 11, 15], 9 → [0, 1]',
      '[3, 2, 4], 6 → [1, 2]',
      '[3, 3], 6 → [0, 1]',
    ],
  },
  {
    id: 'palindrome',
    title: 'Valid Palindrome',
    difficulty: 'Easy',
    category: 'String',
    description:
      'A phrase is a palindrome if, after converting all uppercase letters into lowercase letters and removing all non-alphanumeric characters, it reads the same forward and backward.',
    timeEstimate: '10 min',
    starterCode: `function isPalindrome(s) {
  // Your solution here

}

// Test cases
console.log('Test 1:', isPalindrome("A man, a plan, a canal: Panama")); // true
console.log('Test 2:', isPalindrome("race a car")); // false
console.log('Test 3:', isPalindrome(" ")); // true`,
    testCases: [
      '"A man, a plan, a canal: Panama" → true',
      '"race a car" → false',
      '" " → true',
    ],
  },
  {
    id: 'fizzbuzz',
    title: 'FizzBuzz',
    difficulty: 'Easy',
    category: 'Logic',
    description:
      'Write a program that outputs the string representation of numbers from 1 to n. But for multiples of three it should output "Fizz" instead of the number and for the multiples of five output "Buzz".',
    timeEstimate: '10 min',
    starterCode: `function fizzBuzz(n) {
  // Your solution here

}

// Test case
console.log('FizzBuzz(15):', fizzBuzz(15));
// Expected: ["1","2","Fizz","4","Buzz","Fizz","7","8","Fizz","Buzz","11","Fizz","13","14","FizzBuzz"]`,
    testCases: [
      'n=15 → ["1","2","Fizz","4","Buzz","Fizz","7","8","Fizz","Buzz","11","Fizz","13","14","FizzBuzz"]',
    ],
  },
  {
    id: 'reverse-string',
    title: 'Reverse String',
    difficulty: 'Easy',
    category: 'String',
    description:
      'Write a function that reverses a string. The input string is given as an array of characters s.',
    timeEstimate: '5 min',
    starterCode: `function reverseString(s) {
  // Your solution here - modify s in-place

}

// Test cases
let test1 = ["h","e","l","l","o"];
reverseString(test1);
console.log('Test 1:', test1); // ["o","l","l","e","h"]

let test2 = ["H","a","n","n","a","h"];
reverseString(test2);
console.log('Test 2:', test2); // ["h","a","n","n","a","H"]`,
    testCases: [
      '["h","e","l","l","o"] → ["o","l","l","e","h"]',
      '["H","a","n","n","a","h"] → ["h","a","n","n","a","H"]',
    ],
  },
  {
    id: 'binary-search',
    title: 'Binary Search',
    difficulty: 'Medium',
    category: 'Search',
    description:
      'Given an array of integers nums which is sorted in ascending order, and an integer target, write a function to search target in nums.',
    timeEstimate: '20 min',
    starterCode: `function search(nums, target) {
  // Your solution here

}

// Test cases
console.log('Test 1:', search([-1,0,3,5,9,12], 9)); // 4
console.log('Test 2:', search([-1,0,3,5,9,12], 2)); // -1
console.log('Test 3:', search([5], 5)); // 0`,
    testCases: [
      '[-1,0,3,5,9,12], 9 → 4',
      '[-1,0,3,5,9,12], 2 → -1',
      '[5], 5 → 0',
    ],
  },
  {
    id: 'merge-arrays',
    title: 'Merge Sorted Arrays',
    difficulty: 'Medium',
    category: 'Array',
    description:
      'You are given two integer arrays nums1 and nums2, sorted in non-decreasing order. Merge nums2 into nums1 as one sorted array.',
    timeEstimate: '25 min',
    starterCode: `function merge(nums1, m, nums2, n) {
  // nums1 has length m + n, where the first m elements are the actual values
  // and the last n elements are set to 0 and should be ignored
  // Your solution here

}

// Test case
let nums1 = [1,2,3,0,0,0];
let nums2 = [2,5,6];
merge(nums1, 3, nums2, 3);
console.log('Result:', nums1); // [1,2,2,3,5,6]`,
    testCases: ['nums1=[1,2,3,0,0,0], m=3, nums2=[2,5,6], n=3 → [1,2,2,3,5,6]'],
  },
];

interface CodingChallengesProps {
  onSelectChallenge: (code: string, title: string) => void;
}

export function CodingChallenges({ onSelectChallenge }: CodingChallengesProps) {
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('All');
  const [selectedCategory, setSelectedCategory] = useState<string>('All');

  const difficulties = ['All', 'Easy', 'Medium', 'Hard'];
  const categories = [
    'All',
    ...Array.from(new Set(CODING_CHALLENGES.map((c) => c.category))),
  ];

  const filteredChallenges = CODING_CHALLENGES.filter((challenge) => {
    const difficultyMatch =
      selectedDifficulty === 'All' ||
      challenge.difficulty === selectedDifficulty;
    const categoryMatch =
      selectedCategory === 'All' || challenge.category === selectedCategory;
    return difficultyMatch && categoryMatch;
  });

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Hard':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-6 border-b border-border">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
            <Trophy className="h-5 w-5 text-primary" />
          </div>
          <div>
            <h2 className="text-xl font-bold">Coding Challenges</h2>
            <p className="text-sm text-muted-foreground">
              Practice with popular interview questions
            </p>
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap gap-3">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Difficulty:</span>
            {difficulties.map((diff) => (
              <Button
                key={diff}
                size="sm"
                variant={selectedDifficulty === diff ? 'default' : 'outline'}
                onClick={() => setSelectedDifficulty(diff)}
                className="h-7"
              >
                {diff}
              </Button>
            ))}
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Category:</span>
            {categories.map((cat) => (
              <Button
                key={cat}
                size="sm"
                variant={selectedCategory === cat ? 'default' : 'outline'}
                onClick={() => setSelectedCategory(cat)}
                className="h-7"
              >
                {cat}
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* Challenges List */}
      <ScrollArea className="flex-1">
        <div className="p-6 space-y-4">
          {filteredChallenges.map((challenge) => (
            <Card
              key={challenge.id}
              className="hover:shadow-md transition-shadow"
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <CardTitle className="text-lg">
                        {challenge.title}
                      </CardTitle>
                      <Badge
                        className={getDifficultyColor(challenge.difficulty)}
                      >
                        {challenge.difficulty}
                      </Badge>
                      <Badge variant="outline">{challenge.category}</Badge>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {challenge.timeEstimate}
                      </div>
                      <div className="flex items-center gap-1">
                        <Target className="h-3 w-3" />
                        {challenge.testCases.length} test cases
                      </div>
                    </div>
                  </div>
                  <Button
                    onClick={() =>
                      onSelectChallenge(challenge.starterCode, challenge.title)
                    }
                    className="flex items-center gap-2"
                  >
                    <Play className="h-3 w-3" />
                    Start Challenge
                    <ChevronRight className="h-3 w-3" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-3">
                  {challenge.description}
                </p>
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Example Test Cases:</h4>
                  <div className="space-y-1">
                    {challenge.testCases.slice(0, 2).map((testCase, index) => (
                      <div
                        key={index}
                        className="text-xs font-mono bg-muted/50 px-2 py-1 rounded"
                      >
                        {testCase}
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
}
