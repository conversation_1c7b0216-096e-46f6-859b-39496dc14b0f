'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Trash2,
  X,
  AlertTriangle,
  FileText,
  HardDriveIcon,
  Loader2,
} from 'lucide-react';
import { cn, formatFileSize, formatRelativeTime } from '@/lib/utils';
import type { File } from '@/types';

interface FileDeleteModalProps {
  file: File | null;
  isOpen: boolean;
  onClose: () => void;
  onDelete: (file: File) => Promise<void>;
  className?: string;
}

export function FileDeleteModal({
  file,
  isOpen,
  onClose,
  onDelete,
  className,
}: FileDeleteModalProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!file || isDeleting) return;

    setIsDeleting(true);
    setError('');
    
    try {
      await onDelete(file);
      onClose();
    } catch (err: any) {
      setError(err.message || 'Failed to delete file');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleCancel = () => {
    if (!isDeleting) {
      setError('');
      onClose();
    }
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget && !isDeleting) {
      onClose();
    }
  };

  if (!isOpen || !file) return null;

  return (
    <div
      className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4"
      onClick={handleBackdropClick}
    >
      <div
        className={cn(
          'bg-card border border-border rounded-lg shadow-lg w-full max-w-md',
          'animate-in fade-in-0 zoom-in-95 duration-200',
          className
        )}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-border">
          <div className="flex items-center gap-2">
            <div className="flex items-center justify-center w-8 h-8 bg-destructive/10 rounded-full">
              <Trash2 className="h-4 w-4 text-destructive" />
            </div>
            <h2 className="font-semibold">Delete File</h2>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleCancel}
            disabled={isDeleting}
            className="h-8 w-8"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit} className="p-4 space-y-4">
          {/* Warning Message */}
          <div className="flex items-start gap-3 p-4 bg-destructive/5 border border-destructive/20 rounded-lg">
            <AlertTriangle className="h-5 w-5 text-destructive flex-shrink-0 mt-0.5" />
            <div className="space-y-1">
              <p className="font-medium text-destructive">
                This action cannot be undone
              </p>
              <p className="text-sm text-muted-foreground">
                This file will be permanently deleted from your workspace. All code and history will be lost.
              </p>
            </div>
          </div>

          {/* File Details */}
          <div className="space-y-3 p-4 bg-muted/30 rounded-lg border border-border/50">
            <div className="flex items-center gap-3">
              <div className="flex items-center justify-center w-10 h-10 bg-background rounded-lg border border-border/50">
                <FileText className="h-5 w-5 text-muted-foreground" />
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="font-medium truncate">{file.name}</h3>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="outline" className="text-xs">
                    {file.language.toUpperCase()}
                  </Badge>
                  {file.size && (
                    <span className="text-xs text-muted-foreground">
                      {formatFileSize(file.size)}
                    </span>
                  )}
                </div>
              </div>
            </div>

            {/* File Stats */}
            <div className="grid grid-cols-2 gap-3 pt-3 border-t border-border/50">
              <div className="space-y-1">
                <div className="text-xs text-muted-foreground">Created</div>
                <div className="text-sm font-medium">
                  {formatRelativeTime(file.$createdAt)}
                </div>
              </div>
              <div className="space-y-1">
                <div className="text-xs text-muted-foreground">Modified</div>
                <div className="text-sm font-medium">
                  {file.$updatedAt ? formatRelativeTime(file.$updatedAt) : 'Never'}
                </div>
              </div>
              {file.lastExecuted && (
                <>
                  <div className="space-y-1">
                    <div className="text-xs text-muted-foreground">Last Run</div>
                    <div className="text-sm font-medium">
                      {formatRelativeTime(file.lastExecuted)}
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-xs text-muted-foreground">Executions</div>
                    <div className="text-sm font-medium">
                      {file.executionCount || 0}x
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Confirmation Input */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-muted-foreground">
              To confirm deletion, type the file name below:
            </label>
            <div className="p-3 bg-muted/50 border border-border rounded-md">
              <code className="text-sm font-mono">{file.name}</code>
            </div>
          </div>

          {/* Error message */}
          {error && (
            <div className="flex items-center gap-2 text-sm text-destructive bg-destructive/10 p-3 rounded-lg border border-destructive/20">
              <AlertTriangle className="h-4 w-4 flex-shrink-0" />
              <span>{error}</span>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="destructive"
              disabled={isDeleting}
              className="min-w-[100px]"
            >
              {isDeleting ? (
                <div className="flex items-center gap-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Deleting...
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Trash2 className="w-4 h-4" />
                  Delete File
                </div>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}