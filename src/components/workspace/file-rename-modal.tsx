'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Edit3,
  X,
  AlertCircle,
  FileText,
  CheckCircle,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import type { File } from '@/types';

interface FileRenameModalProps {
  file: File | null;
  existingFileNames: string[];
  isOpen: boolean;
  onClose: () => void;
  onRename: (file: File, newName: string) => Promise<void>;
  className?: string;
}

export function FileRenameModal({
  file,
  existingFileNames,
  isOpen,
  onClose,
  onRename,
  className,
}: FileRenameModalProps) {
  const [newName, setNewName] = useState('');
  const [error, setError] = useState('');
  const [isRenaming, setIsRenaming] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isOpen && file) {
      setNewName(file.name);
      setError('');
      // Focus and select filename without extension
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.focus();
          const lastDot = file.name.lastIndexOf('.');
          if (lastDot > 0) {
            inputRef.current.setSelectionRange(0, lastDot);
          } else {
            inputRef.current.select();
          }
        }
      }, 100);
    }
  }, [isOpen, file]);

  const validateFileName = (name: string): string | null => {
    if (!name.trim()) {
      return 'File name cannot be empty';
    }

    if (name.length > 100) {
      return 'File name is too long (max 100 characters)';
    }

    // Check for invalid characters
    const invalidChars = /[<>:"/\\|?*\x00-\x1f]/;
    if (invalidChars.test(name)) {
      return 'File name contains invalid characters';
    }

    // Check if name already exists (case-insensitive)
    const normalizedNew = name.toLowerCase().trim();
    const normalizedExisting = existingFileNames
      .filter(existingName => existingName !== file?.name) // Exclude current file
      .map(name => name.toLowerCase());
    
    if (normalizedExisting.includes(normalizedNew)) {
      return 'A file with this name already exists';
    }

    return null;
  };

  const getFileExtension = (filename: string): string => {
    const lastDot = filename.lastIndexOf('.');
    return lastDot > 0 ? filename.substring(lastDot) : '';
  };

  const getLanguageFromExtension = (extension: string): File['language'] => {
    const extMap: Record<string, File['language']> = {
      '.js': 'javascript',
      '.ts': 'typescript',
      '.py': 'python',
      '.java': 'java',
      '.cpp': 'cpp',
      '.c': 'c',
      '.go': 'go',
      '.rs': 'rust',
      '.html': 'text', // We don't have HTML in the union, using text
      '.css': 'text',
      '.json': 'text',
      '.md': 'markdown',
      '.txt': 'text',
    };
    return extMap[extension.toLowerCase()] || 'text';
  };

  const handleInputChange = (value: string) => {
    setNewName(value);
    const validationError = validateFileName(value);
    setError(validationError || '');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!file || !newName.trim() || error || isRenaming) return;

    const validationError = validateFileName(newName);
    if (validationError) {
      setError(validationError);
      return;
    }

    setIsRenaming(true);
    try {
      await onRename(file, newName.trim());
      onClose();
    } catch (err: any) {
      setError(err.message || 'Failed to rename file');
    } finally {
      setIsRenaming(false);
    }
  };

  const handleCancel = () => {
    if (!isRenaming) {
      onClose();
    }
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget && !isRenaming) {
      onClose();
    }
  };

  if (!isOpen || !file) return null;

  const originalExtension = getFileExtension(file.name);
  const newExtension = getFileExtension(newName);
  const isExtensionChanged = originalExtension !== newExtension;
  const newLanguage = getLanguageFromExtension(newExtension);

  return (
    <div
      className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4"
      onClick={handleBackdropClick}
    >
      <div
        className={cn(
          'bg-card border border-border rounded-lg shadow-lg w-full max-w-md',
          'animate-in fade-in-0 zoom-in-95 duration-200',
          className
        )}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-border">
          <div className="flex items-center gap-2">
            <Edit3 className="h-4 w-4 text-primary" />
            <h2 className="font-semibold">Rename File</h2>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleCancel}
            disabled={isRenaming}
            className="h-8 w-8"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit} className="p-4 space-y-4">
          {/* Original file info */}
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <FileText className="h-4 w-4" />
            <span>Current: </span>
            <span className="font-medium">{file.name}</span>
            <Badge variant="outline" className="text-xs">
              {file.language.toUpperCase()}
            </Badge>
          </div>

          {/* Input field */}
          <div className="space-y-2">
            <label htmlFor="fileName" className="text-sm font-medium">
              New name:
            </label>
            <Input
              ref={inputRef}
              id="fileName"
              value={newName}
              onChange={(e) => handleInputChange(e.target.value)}
              placeholder="Enter new file name"
              className={cn(
                error && 'border-destructive focus-visible:ring-destructive',
                !error && newName !== file.name && 'border-success focus-visible:ring-success'
              )}
              disabled={isRenaming}
            />
          </div>

          {/* Extension change warning */}
          {isExtensionChanged && (
            <div className="flex items-start gap-2 p-3 bg-warning/10 border border-warning/20 rounded-md">
              <AlertCircle className="h-4 w-4 text-warning flex-shrink-0 mt-0.5" />
              <div className="text-sm">
                <p className="font-medium text-warning">Extension changed</p>
                <p className="text-muted-foreground">
                  Language will change from{' '}
                  <span className="font-medium">{file.language}</span> to{' '}
                  <span className="font-medium">{newLanguage}</span>
                </p>
              </div>
            </div>
          )}

          {/* Error message */}
          {error && (
            <div className="flex items-center gap-2 text-sm text-destructive">
              <AlertCircle className="h-4 w-4" />
              <span>{error}</span>
            </div>
          )}

          {/* Success preview */}
          {!error && newName !== file.name && (
            <div className="flex items-center gap-2 text-sm text-success">
              <CheckCircle className="h-4 w-4" />
              <span>Ready to rename</span>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isRenaming}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={!!error || newName === file.name || isRenaming}
              className="min-w-[80px]"
            >
              {isRenaming ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  Renaming...
                </div>
              ) : (
                'Rename'
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}