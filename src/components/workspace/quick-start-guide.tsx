'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { X, Play, Code, FileText, Zap } from 'lucide-react';

interface QuickStartGuideProps {
  onClose: () => void;
  onCreateFile: () => void;
}

export function QuickStartGuide({
  onClose,
  onCreateFile,
}: QuickStartGuideProps) {
  const [currentStep, setCurrentStep] = useState(0);

  const steps = [
    {
      title: 'Welcome to Codeable!',
      icon: <Zap className="h-8 w-8 text-primary" />,
      content: (
        <div className="space-y-4">
          <p>
            Codeable is your AI-powered coding practice platform. You can write,
            run, and test JavaScript and TypeScript code directly in your
            browser!
          </p>
          <div className="bg-muted p-4 rounded-lg">
            <h4 className="font-medium mb-2">✨ What's New:</h4>
            <ul className="text-sm space-y-1">
              <li>• Real JavaScript/TypeScript execution</li>
              <li>• Code templates and examples</li>
              <li>• Auto-save functionality</li>
              <li>• Professional Monaco editor</li>
            </ul>
          </div>
        </div>
      ),
    },
    {
      title: 'Create Your First File',
      icon: <FileText className="h-8 w-8 text-primary" />,
      content: (
        <div className="space-y-4">
          <p>
            Start by creating a new file. Choose from JavaScript or TypeScript,
            and pick a template to get started quickly.
          </p>
          <div className="bg-muted p-4 rounded-lg">
            <h4 className="font-medium mb-2">📝 Available Templates:</h4>
            <ul className="text-sm space-y-1">
              <li>• Hello World - Basic introduction</li>
              <li>• Array Operations - Working with arrays</li>
              <li>• Object Manipulation - Object methods</li>
              <li>• Async Example - Promises and async/await</li>
            </ul>
          </div>
          <Button onClick={onCreateFile} className="w-full">
            <FileText className="h-4 w-4 mr-2" />
            Create Your First File
          </Button>
        </div>
      ),
    },
    {
      title: 'Run Your Code',
      icon: <Play className="h-8 w-8 text-primary" />,
      content: (
        <div className="space-y-4">
          <p>
            Once you've written some code, click the "Run" button or press
            Ctrl+Enter to execute it. Your output will appear in the console
            below.
          </p>
          <div className="bg-muted p-4 rounded-lg">
            <h4 className="font-medium mb-2">🚀 Try this example:</h4>
            <pre className="text-sm bg-background p-2 rounded border">
              {`console.log('Hello, Codeable!');

const numbers = [1, 2, 3, 4, 5];
const doubled = numbers.map(n => n * 2);
console.log('Doubled:', doubled);`}
            </pre>
          </div>
        </div>
      ),
    },
    {
      title: "You're Ready!",
      icon: <Code className="h-8 w-8 text-primary" />,
      content: (
        <div className="space-y-4">
          <p>
            You're all set to start coding! Your work is automatically saved,
            and you can create multiple files to organize your practice
            sessions.
          </p>
          <div className="bg-muted p-4 rounded-lg">
            <h4 className="font-medium mb-2">💡 Pro Tips:</h4>
            <ul className="text-sm space-y-1">
              <li>• Use Ctrl+S to force save</li>
              <li>• Files are organized in the sidebar</li>
              <li>• Try different templates for learning</li>
              <li>• Code runs safely in your browser</li>
            </ul>
          </div>
        </div>
      ),
    },
  ];

  const currentStepData = steps[currentStep];

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-card border border-border rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[80vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            {currentStepData.icon}
            <h2 className="text-xl font-semibold">{currentStepData.title}</h2>
          </div>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        <div className="mb-6">{currentStepData.content}</div>

        {/* Progress indicator */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex space-x-2">
            {steps.map((_, index) => (
              <div
                key={index}
                className={`w-2 h-2 rounded-full ${
                  index === currentStep
                    ? 'bg-primary'
                    : index < currentStep
                    ? 'bg-primary/50'
                    : 'bg-muted'
                }`}
              />
            ))}
          </div>
          <span className="text-sm text-muted-foreground">
            {currentStep + 1} of {steps.length}
          </span>
        </div>

        {/* Navigation */}
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
            disabled={currentStep === 0}
          >
            Previous
          </Button>

          {currentStep < steps.length - 1 ? (
            <Button onClick={() => setCurrentStep(currentStep + 1)}>
              Next
            </Button>
          ) : (
            <Button onClick={onClose}>Get Started!</Button>
          )}
        </div>
      </div>
    </div>
  );
}
