'use client';

import { useState } from 'react';
import { 
  <PERSON>Tex<PERSON>, 
  MoreHorizontal, 
  Edit2, 
  Trash2, 
  Copy,
  Download
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import type { File } from '@/types';

interface FileTreeProps {
  files: File[];
  activeFileId?: string;
  onFileSelect: (file: File) => void;
  onFileRename?: (file: File, newName: string) => void;
  onFileDelete?: (file: File) => void;
  onFileDuplicate?: (file: File) => void;
  className?: string;
}

interface FileItemProps {
  file: File;
  isActive: boolean;
  onSelect: () => void;
  onRename?: (newName: string) => void;
  onDelete?: () => void;
  onDuplicate?: () => void;
}

function FileItem({ 
  file, 
  isActive, 
  onSelect, 
  onRename, 
  onDelete, 
  onDuplicate 
}: FileItemProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editName, setEditName] = useState(file.name);
  const [showMenu, setShowMenu] = useState(false);

  const handleRename = () => {
    if (editName.trim() && editName !== file.name) {
      onRename?.(editName.trim());
    }
    setIsEditing(false);
    setEditName(file.name);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleRename();
    } else if (e.key === 'Escape') {
      setIsEditing(false);
      setEditName(file.name);
    }
  };

  const getLanguageIcon = (language: string) => {
    // You can expand this with more specific icons
    return <FileText className="h-4 w-4" />;
  };

  const getLanguageColor = (language: string) => {
    const colors: Record<string, string> = {
      javascript: 'text-yellow-500',
      typescript: 'text-blue-500',
      python: 'text-green-500',
      java: 'text-orange-500',
      cpp: 'text-purple-500',
      go: 'text-cyan-500',
      rust: 'text-orange-600',
      markdown: 'text-gray-500',
      text: 'text-gray-400',
    };
    return colors[language] || 'text-gray-500';
  };

  return (
    <div
      className={cn(
        'group flex items-center justify-between px-2 py-1.5 rounded-md cursor-pointer hover:bg-accent',
        isActive && 'bg-accent text-accent-foreground'
      )}
      onClick={!isEditing ? onSelect : undefined}
    >
      <div className="flex items-center space-x-2 flex-1 min-w-0">
        <div className={cn('shrink-0', getLanguageColor(file.language))}>
          {getLanguageIcon(file.language)}
        </div>
        
        {isEditing ? (
          <input
            type="text"
            value={editName}
            onChange={(e) => setEditName(e.target.value)}
            onBlur={handleRename}
            onKeyDown={handleKeyDown}
            className="flex-1 bg-background border border-border rounded px-1 py-0.5 text-sm focus:outline-none focus:ring-1 focus:ring-ring"
            autoFocus
          />
        ) : (
          <span className="text-sm truncate">{file.name}</span>
        )}
      </div>

      {!isEditing && (
        <div className="relative">
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100"
            onClick={(e) => {
              e.stopPropagation();
              setShowMenu(!showMenu);
            }}
          >
            <MoreHorizontal className="h-3 w-3" />
          </Button>

          {showMenu && (
            <div className="absolute right-0 top-6 w-48 bg-card border border-border rounded-md shadow-md z-50">
              <div className="p-1">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsEditing(true);
                    setShowMenu(false);
                  }}
                  className="flex w-full items-center px-2 py-1 text-sm hover:bg-accent rounded-sm"
                >
                  <Edit2 className="h-3 w-3 mr-2" />
                  Rename
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onDuplicate?.();
                    setShowMenu(false);
                  }}
                  className="flex w-full items-center px-2 py-1 text-sm hover:bg-accent rounded-sm"
                >
                  <Copy className="h-3 w-3 mr-2" />
                  Duplicate
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    // TODO: Implement download
                    setShowMenu(false);
                  }}
                  className="flex w-full items-center px-2 py-1 text-sm hover:bg-accent rounded-sm"
                >
                  <Download className="h-3 w-3 mr-2" />
                  Download
                </button>
                <hr className="my-1" />
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    if (confirm('Are you sure you want to delete this file?')) {
                      onDelete?.();
                    }
                    setShowMenu(false);
                  }}
                  className="flex w-full items-center px-2 py-1 text-sm text-destructive hover:bg-accent rounded-sm"
                >
                  <Trash2 className="h-3 w-3 mr-2" />
                  Delete
                </button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export function FileTree({
  files,
  activeFileId,
  onFileSelect,
  onFileRename,
  onFileDelete,
  onFileDuplicate,
  className,
}: FileTreeProps) {
  const [openMenuId, setOpenMenuId] = useState<string | null>(null);

  // Close menu when clicking outside
  useState(() => {
    const handleClickOutside = () => setOpenMenuId(null);
    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  });

  const sortedFiles = [...files].sort((a, b) => 
    a.name.localeCompare(b.name, undefined, { sensitivity: 'base' })
  );

  if (files.length === 0) {
    return (
      <div className={cn('p-4 text-center', className)}>
        <FileText className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
        <p className="text-sm text-muted-foreground">No files yet</p>
        <p className="text-xs text-muted-foreground mt-1">
          Create your first file to get started
        </p>
      </div>
    );
  }

  return (
    <div className={cn('space-y-1', className)}>
      {sortedFiles.map((file) => (
        <FileItem
          key={file.$id}
          file={file}
          isActive={file.$id === activeFileId}
          onSelect={() => onFileSelect(file)}
          onRename={(newName) => onFileRename?.(file, newName)}
          onDelete={() => onFileDelete?.(file)}
          onDuplicate={() => onFileDuplicate?.(file)}
        />
      ))}
    </div>
  );
}