import { Code } from 'lucide-react';
import { cn } from '@/lib/utils';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
  className?: string;
  onClick?: () => void;
}

export function Logo({ size = 'md', showText = true, className, onClick }: LogoProps) {
  const sizeClasses = {
    sm: {
      container: 'w-8 h-8',
      icon: 'h-4 w-4',
      text: 'text-lg',
    },
    md: {
      container: 'w-10 h-10',
      icon: 'h-6 w-6',
      text: 'text-xl',
    },
    lg: {
      container: 'w-12 h-12',
      icon: 'h-7 w-7',
      text: 'text-2xl',
    },
  };

  const currentSize = sizeClasses[size];
  const Component = onClick ? 'button' : 'div';

  return (
    <Component 
      className={cn(
        'flex items-center space-x-3',
        onClick && 'cursor-pointer hover:opacity-80 transition-opacity focus:outline-none focus:ring-2 focus:ring-primary/20 rounded-lg',
        className
      )}
      onClick={onClick}
      title={onClick ? 'Go to Dashboard' : undefined}
    >
      <div
        className={cn(
          'bg-gradient-to-br from-primary via-purple-600 to-pink-500 rounded-xl flex items-center justify-center shadow-lg shadow-primary/25',
          currentSize.container
        )}
      >
        <Code className={cn('text-white', currentSize.icon)} />
      </div>
      {showText && (
        <span
          className={cn(
            'font-bold bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent',
            currentSize.text
          )}
        >
          Codeable
        </span>
      )}
    </Component>
  );
}
