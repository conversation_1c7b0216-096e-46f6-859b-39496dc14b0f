'use client';

import { useState, useRef, useCallback, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface ResizablePanelProps {
  children: React.ReactNode;
  defaultSize?: number;
  minSize?: number;
  maxSize?: number;
  direction?: 'horizontal' | 'vertical';
  className?: string;
  onResize?: (size: number) => void;
}

export function ResizablePanel({
  children,
  defaultSize = 300,
  minSize = 200,
  maxSize = 600,
  direction = 'horizontal',
  className,
  onResize,
}: ResizablePanelProps) {
  const [size, setSize] = useState(defaultSize);
  const [isResizing, setIsResizing] = useState(false);
  const panelRef = useRef<HTMLDivElement>(null);
  const startPos = useRef(0);
  const startSize = useRef(0);

  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      setIsResizing(true);
      startPos.current = direction === 'horizontal' ? e.clientX : e.clientY;
      startSize.current = size;

      // Prevent text selection during resize
      document.body.style.userSelect = 'none';
      document.body.style.cursor =
        direction === 'horizontal' ? 'col-resize' : 'row-resize';
    },
    [direction, size]
  );

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isResizing) return;

      const currentPos = direction === 'horizontal' ? e.clientX : e.clientY;
      const delta = currentPos - startPos.current;

      // For horizontal resizing, we want to make the panel smaller when dragging left
      const adjustedDelta = direction === 'horizontal' ? -delta : delta;

      const newSize = Math.max(
        minSize,
        Math.min(maxSize, startSize.current + adjustedDelta)
      );

      setSize(newSize);
      onResize?.(newSize);
    },
    [isResizing, direction, minSize, maxSize, onResize]
  );

  const handleMouseUp = useCallback(() => {
    setIsResizing(false);
    document.body.style.userSelect = '';
    document.body.style.cursor = '';
  }, []);

  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isResizing, handleMouseMove, handleMouseUp]);

  const sizeStyle =
    direction === 'horizontal'
      ? { width: `${size}px` }
      : { height: `${size}px` };

  return (
    <div
      ref={panelRef}
      className={cn('relative flex-shrink-0', className)}
      style={sizeStyle}
    >
      {/* Resize handle */}
      <div
        className={cn(
          'absolute bg-border hover:bg-primary/50 transition-colors z-10 group',
          direction === 'horizontal'
            ? 'left-0 top-0 w-1 h-full cursor-col-resize hover:w-2'
            : 'top-0 left-0 w-full h-1 cursor-row-resize hover:h-2',
          isResizing && 'bg-primary w-2'
        )}
        onMouseDown={handleMouseDown}
      >
        {/* Visual indicator */}
        <div
          className={cn(
            'absolute opacity-0 group-hover:opacity-100 transition-opacity',
            direction === 'horizontal'
              ? 'left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-1 h-8 bg-primary/30 rounded-full'
              : 'left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-8 h-1 bg-primary/30 rounded-full'
          )}
        />
      </div>

      {/* Panel content */}
      <div className="h-full w-full">{children}</div>
    </div>
  );
}
