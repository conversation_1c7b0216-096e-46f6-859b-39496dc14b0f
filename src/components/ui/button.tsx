import * as React from "react";
import { cn } from "@/lib/utils";

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link" | "success" | "warning";
  size?: "default" | "sm" | "lg" | "icon";
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = "default", size = "default", ...props }, ref) => {
    return (
      <button
        className={cn(
          "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium",
          "transition-all duration-150 ease-in-out ripple-effect focus-glow",
          "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
          "focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-1",
          "disabled:pointer-events-none disabled:opacity-50 disabled:cursor-not-allowed",
          "active:scale-95 active:transition-transform active:duration-75",
          {
            // Variants with enhanced hover effects
            "bg-primary text-primary-foreground hover:bg-primary-hover hover:-translate-y-0.5 hover:shadow-lg active:translate-y-0": variant === "default",
            "bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:-translate-y-0.5 hover:shadow-lg active:translate-y-0": variant === "destructive",
            "border border-input bg-background hover:bg-accent-hover hover:text-accent-foreground hover:border-primary/50": variant === "outline",
            "bg-secondary text-secondary-foreground hover:bg-secondary-hover hover:-translate-y-0.5": variant === "secondary",
            "hover:bg-accent-hover hover:text-accent-foreground rounded-md": variant === "ghost",
            "text-primary underline-offset-4 hover:underline hover:text-primary-hover": variant === "link",
            "bg-success text-success-foreground hover:bg-success/90 hover:-translate-y-0.5 hover:shadow-lg active:translate-y-0": variant === "success",
            "bg-warning text-warning-foreground hover:bg-warning/90 hover:-translate-y-0.5 hover:shadow-lg active:translate-y-0": variant === "warning",
          },
          {
            // Sizes with 8px grid system
            "h-10 px-4 py-2": size === "default",
            "h-8 px-3 text-xs": size === "sm",
            "h-12 px-6 text-base": size === "lg",
            "h-10 w-10 p-0": size === "icon",
          },
          className
        )}
        ref={ref}
        {...props}
      />
    );
  }
);

Button.displayName = "Button";

export { Button };