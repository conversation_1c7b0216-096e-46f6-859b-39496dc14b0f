'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import {
  FolderPlus,
  Search,
  Settings,
  FileText,
  Clock,
  Menu,
  X,
  File,
  Plus,
  FileCode,
  FileType,
  Code,
  Cog,
  Globe,
  Palette,
  FileJson,
  BookOpen,
  MoreHorizontal,
} from 'lucide-react';
import { FileContextMenu, useFileContextMenu } from '@/components/workspace/file-context-menu';
import { FileRenameModal } from '@/components/workspace/file-rename-modal';
import { FileDeleteModal } from '@/components/workspace/file-delete-modal';
import type { File } from '@/types';

const getFileIcon = (language: string, fileName: string) => {
  const ext = fileName.split('.').pop()?.toLowerCase();

  switch (language.toLowerCase()) {
    case 'javascript':
      return FileCode;
    case 'typescript':
      return FileType;
    case 'python':
      return Code;
    case 'java':
      return FileCode;
    case 'cpp':
    case 'c':
      return Cog;
    case 'html':
      return Globe;
    case 'css':
      return Palette;
    case 'json':
      return FileJson;
    case 'markdown':
    case 'md':
      return BookOpen;
    case 'yaml':
    case 'yml':
      return Settings;
    default:
      switch (ext) {
        case 'js':
          return FileCode;
        case 'ts':
          return FileType;
        case 'py':
          return Code;
        case 'java':
          return FileCode;
        case 'cpp':
        case 'c':
          return Cog;
        case 'html':
          return Globe;
        case 'css':
          return Palette;
        case 'json':
          return FileJson;
        case 'md':
          return BookOpen;
        case 'yml':
        case 'yaml':
          return Settings;
        default:
          return FileText;
      }
  }
};
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { cn, formatFileSize, formatRelativeTime, formatExactTime } from '@/lib/utils';
import { useWorkspace } from '@/contexts/workspace-context';
import { Logo } from '@/components/ui/logo';

interface SidebarProps {
  workspaces?: any[];
  onCreateWorkspace?: () => void;
  onCreateFile?: () => void;
  onSearchChange?: (query: string) => void;
  onRenameFile?: (file: File, newName: string) => Promise<void>;
  onDeleteFile?: (file: File) => Promise<void>;
  onDuplicateFile?: (file: File) => void;
  onShowFileProperties?: (file: File) => void;
}

export function Sidebar({
  workspaces = [],
  onCreateWorkspace,
  onCreateFile,
  onSearchChange,
  onRenameFile,
  onDeleteFile,
  onDuplicateFile,
  onShowFileProperties,
}: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [renameModalFile, setRenameModalFile] = useState<File | null>(null);
  const [deleteModalFile, setDeleteModalFile] = useState<File | null>(null);
  
  const pathname = usePathname();
  const router = useRouter();
  const { currentWorkspace, currentFiles, activeFile, setActiveFile } =
    useWorkspace();
  
  const { contextMenu, openContextMenu, closeContextMenu } = useFileContextMenu();

  const handleSearch = (value: string) => {
    setSearchQuery(value);
    onSearchChange?.(value);
  };

  // Context menu handlers
  const handleRenameFile = (file: File) => {
    setRenameModalFile(file);
  };

  const handleDeleteFile = (file: File) => {
    setDeleteModalFile(file);
  };

  const handleDuplicateFile = (file: File) => {
    onDuplicateFile?.(file);
  };

  const handleShowProperties = (file: File) => {
    onShowFileProperties?.(file);
  };

  // Modal handlers
  const handleRenameConfirm = async (file: File, newName: string) => {
    if (onRenameFile) {
      await onRenameFile(file, newName);
    }
    setRenameModalFile(null);
  };

  const handleDeleteConfirm = async (file: File) => {
    if (onDeleteFile) {
      await onDeleteFile(file);
    }
    setDeleteModalFile(null);
  };

  const navigation = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: FileText,
    },
    {
      name: 'Recent Files',
      href: '/dashboard/recent',
      icon: Clock,
    },
    {
      name: 'Settings',
      href: '/dashboard/settings',
      icon: Settings,
    },
  ];

  return (
    <div
      className={cn(
        'sidebar flex flex-col h-full bg-sidebar-background border-r border-sidebar-border transition-all duration-300 shadow-sm',
        // Desktop sizing
        'hidden md:flex',
        isCollapsed ? 'md:w-16' : 'md:w-72',
        // Mobile: full width overlay when not collapsed
        'md:relative fixed inset-y-0 left-0 z-50',
        isCollapsed ? 'md:static' : 'w-80 md:w-72'
      )}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-sidebar-border bg-muted/20">
        {!isCollapsed ? (
          <Logo size="md" onClick={() => router.push('/dashboard')} />
        ) : (
          <Logo size="sm" showText={false} onClick={() => router.push('/dashboard')} />
        )}
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="h-8 w-8 hover:bg-accent-hover transition-all duration-150"
        >
          {isCollapsed ? (
            <Menu className="h-4 w-4" />
          ) : (
            <X className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* Search */}
      {!isCollapsed && (
        <div className="p-4 border-b border-sidebar-border">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search workspaces and files..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-9 bg-background/50 border-input focus:border-input-focus focus:ring-1 focus:ring-primary/20 transition-all duration-150"
            />
          </div>
        </div>
      )}

      {/* New Workspace Button */}
      <div className="p-4">
        <Button
          onClick={onCreateWorkspace}
          className="w-full justify-start gap-2"
          variant="success"
          size="default"
        >
          <FolderPlus className="h-4 w-4" />
          {!isCollapsed && 'New Workspace'}
        </Button>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {!isCollapsed && (
          <>
            {currentWorkspace ? (
              /* Files in Current Workspace */
              <div className="px-4 py-3">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-sm font-semibold text-foreground">
                    {currentWorkspace.name}
                  </h3>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={onCreateFile}
                    className="h-6 w-6 p-0 hover:bg-primary/10 hover:text-primary"
                    title="Create new file"
                  >
                    <Plus className="h-3 w-3" />
                  </Button>
                </div>
                <div className="space-y-1">
                  {currentFiles.map((file) => (
                    <div className="relative group">
                      <button
                        key={file.$id}
                        onClick={() => setActiveFile(file)}
                        onContextMenu={(e) => openContextMenu(file, e)}
                        className={cn(
                          'sidebar-item w-full flex items-center justify-between px-3 py-2.5 text-sm rounded-md transition-all text-left',
                          'focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-1',
                          activeFile?.$id === file.$id
                            ? 'bg-primary/15 text-primary border border-primary/30 font-medium'
                            : 'text-muted-foreground hover:text-foreground hover:bg-accent-hover'
                        )}
                        data-active={activeFile?.$id === file.$id}
                        aria-pressed={activeFile?.$id === file.$id}
                        aria-label={`Open file ${file.name} (${file.language})`}
                        title={`${file.name} (${file.language}) • Right-click or use options button for more actions`}
                      >
                        <div className="flex items-center min-w-0 flex-1 mr-2">
                          {(() => {
                            const FileIcon = getFileIcon(file.language, file.name);
                            const getLanguageColor = (lang: string) => {
                              switch (lang.toLowerCase()) {
                                case 'javascript': return 'text-yellow-600 dark:text-yellow-400';
                                case 'typescript': return 'text-blue-600 dark:text-blue-400';
                                case 'python': return 'text-success';
                                case 'java': return 'text-orange-600 dark:text-orange-400';
                                case 'cpp':
                                case 'c': return 'text-blue-700 dark:text-blue-300';
                                case 'html': return 'text-orange-500 dark:text-orange-400';
                                case 'css': return 'text-blue-500 dark:text-blue-400';
                                case 'json': return 'text-muted-foreground';
                                default: return 'text-muted-foreground';
                              }
                            };
                            
                            return (
                              <FileIcon
                                className={cn(
                                  'h-4 w-4 mr-3 flex-shrink-0',
                                  getLanguageColor(file.language)
                                )}
                              />
                            );
                          })()}
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <span className="truncate font-medium">{file.name}</span>
                              {file.isModified && (
                                <div 
                                  className="w-1.5 h-1.5 rounded-full bg-warning flex-shrink-0" 
                                  title="Unsaved changes"
                                />
                              )}
                            </div>
                            <div className="flex items-center gap-2 text-xs text-muted-foreground mt-0.5">
                              <span>Updated {formatRelativeTime(file.$updatedAt)}</span>
                              <span>•</span>
                              <span className="capitalize">{file.language}</span>
                              {file.size && (
                                <>
                                  <span>•</span>
                                  <span>{formatFileSize(file.size)}</span>
                                </>
                              )}
                              {file.lastExecuted && (
                                <>
                                  <span>•</span>
                                  <span title={`Last run: ${formatExactTime(file.lastExecuted)}`}>
                                    Executed {formatRelativeTime(file.lastExecuted)}
                                  </span>
                                </>
                              )}
                              {file.executionCount && file.executionCount > 0 && (
                                <>
                                  <span>•</span>
                                  <span className="text-success" title={`Executed ${file.executionCount} times`}>
                                    {file.executionCount}x runs
                                  </span>
                                </>
                              )}
                            </div>
                          </div>
                        </div>
                        
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-7 w-7 flex-shrink-0 opacity-60 group-hover:opacity-100 hover:bg-accent/80 transition-all duration-150"
                          onClick={(e) => {
                            e.stopPropagation();
                            openContextMenu(file, e);
                          }}
                          title="File options"
                        >
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </button>
                    </div>
                  ))}
                  {currentFiles.length === 0 && (
                    <div className="text-center py-6">
                      <File className="h-8 w-8 mx-auto mb-2 text-muted-foreground/50" />
                      <p className="text-xs text-muted-foreground">
                        No files yet
                      </p>
                      <p className="text-xs text-muted-foreground/75">
                        Create your first one!
                      </p>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              /* Workspaces List */
              <div className="px-4 py-2">
                <h3 className="text-sm font-medium text-muted-foreground mb-2">
                  Workspaces
                </h3>
                <div className="space-y-1">
                  {workspaces.map((workspace) => (
                    <Link
                      key={workspace.$id}
                      href={`/dashboard/workspace/${workspace.$id}`}
                      className={cn(
                        'flex items-center px-3 py-2 text-sm rounded-md hover:bg-accent hover:text-accent-foreground transition-colors',
                        pathname === `/dashboard/workspace/${workspace.$id}`
                          ? 'bg-accent text-accent-foreground'
                          : 'text-muted-foreground'
                      )}
                    >
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-primary rounded-full" />
                        <span className="truncate">{workspace.name}</span>
                      </div>
                    </Link>
                  ))}
                  {workspaces.length === 0 && (
                    <p className="text-xs text-muted-foreground px-3 py-2">
                      No workspaces yet. Create your first one!
                    </p>
                  )}
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* Navigation */}
      <div className="border-t border-border">
        {navigation.map((item) => (
          <Link
            key={item.name}
            href={item.href}
            className={cn(
              'flex items-center px-4 py-3 text-sm hover:bg-accent hover:text-accent-foreground transition-colors',
              pathname === item.href
                ? 'bg-accent text-accent-foreground'
                : 'text-muted-foreground'
            )}
          >
            <item.icon className="h-4 w-4" />
            {!isCollapsed && <span className="ml-3">{item.name}</span>}
          </Link>
        ))}
      </div>

      {/* Context Menu */}
      {contextMenu.file && (
        <FileContextMenu
          file={contextMenu.file}
          isOpen={contextMenu.isOpen}
          position={contextMenu.position}
          onClose={closeContextMenu}
          onRename={handleRenameFile}
          onDelete={handleDeleteFile}
          onDuplicate={handleDuplicateFile}
          onShowProperties={handleShowProperties}
        />
      )}

      {/* Rename Modal */}
      <FileRenameModal
        file={renameModalFile}
        existingFileNames={currentFiles.map(f => f.name)}
        isOpen={!!renameModalFile}
        onClose={() => setRenameModalFile(null)}
        onRename={handleRenameConfirm}
      />

      {/* Delete Modal */}
      <FileDeleteModal
        file={deleteModalFile}
        isOpen={!!deleteModalFile}
        onClose={() => setDeleteModalFile(null)}
        onDelete={handleDeleteConfirm}
      />
    </div>
  );
}
