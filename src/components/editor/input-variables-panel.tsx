'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Settings, Play, RotateCcw } from 'lucide-react';
import { cn } from '@/lib/utils';

interface InputVariablesPanelProps {
  language: string;
  onVariablesChange?: (variablesText: string) => void;
  onRunWithVariables?: () => void;
  className?: string;
}

const getDefaultVariables = (language: string) => {
  if (language === 'python') {
    return `nums = [2, 7, 11, 15]
target = 9`;
  } else {
    // JavaScript/TypeScript
    return `const nums = [2, 7, 11, 15];
const target = 9;`;
  }
};

export function InputVariablesPanel({
  language,
  onVariablesChange,
  onRunWithVariables,
  className,
}: InputVariablesPanelProps) {
  const [variablesText, setVariablesText] = useState(
    getDefaultVariables(language)
  );

  useEffect(() => {
    setVariablesText(getDefaultVariables(language));
  }, [language]);

  useEffect(() => {
    onVariablesChange?.(variablesText);
  }, [variablesText, onVariablesChange]);

  const resetToDefaults = () => {
    setVariablesText(getDefaultVariables(language));
  };

  const getVariableCount = () => {
    return variablesText
      .split('\n')
      .filter(
        (line) =>
          line.trim() &&
          !line.trim().startsWith('#') &&
          !line.trim().startsWith('//')
      ).length;
  };

  return (
    <Card className={cn('flex flex-col h-full', className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CardTitle className="text-sm font-semibold">STDIN</CardTitle>
          </div>
          <div className="flex items-center gap-1">
            <Button
              size="sm"
              variant="ghost"
              onClick={resetToDefaults}
              className="h-6 w-6 p-0"
              title="Reset to defaults"
            >
              <RotateCcw className="h-3 w-3" />
            </Button>
          </div>
        </div>
        <p className="text-xs text-muted-foreground">
          Input for the program (Optional)
        </p>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col min-h-0 pt-0">
        {/* Variables Input */}
        <div className="flex-1 mb-4">
          <Textarea
            value={variablesText}
            onChange={(e) => setVariablesText(e.target.value)}
            placeholder={
              language === 'python'
                ? `# Define your variables here
nums = [2, 7, 11, 15]
target = 9
name = "example"`
                : `// Define your variables here
const nums = [2, 7, 11, 15];
const target = 9;
const name = "example";`
            }
            className="h-full resize-none font-mono text-sm"
          />
        </div>

        {/* Actions */}
        <div className="space-y-2">
          {onRunWithVariables && (
            <Button
              size="sm"
              onClick={onRunWithVariables}
              className="w-full h-8 text-sm bg-green-600 hover:bg-green-700"
            >
              <Play className="h-3 w-3 mr-2" />
              Run with Variables
            </Button>
          )}
        </div>

        {/* Help Text - With max height to prevent overflow */}
        <div
          className="mt-4 pt-3 border-t border-border overflow-hidden"
          style={{ maxHeight: '80px' }}
        >
          <div className="text-xs text-muted-foreground space-y-1">
            <p className="font-medium">Tips:</p>
            <ul className="space-y-1 ml-2">
              <li>
                • Define variables using{' '}
                {language === 'python' ? 'Python' : 'JavaScript'} syntax
              </li>
              <li>• Variables will be available in your code</li>
              <li>• Use comments to organize your variables</li>
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
