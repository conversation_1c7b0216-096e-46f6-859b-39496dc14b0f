'use client';

import { useEffect, useRef } from 'react';
import Editor from '@monaco-editor/react';
import type { editor } from 'monaco-editor';
import { cn } from '@/lib/utils';
import { useTheme } from '@/contexts/theme-context';

interface CodeEditorProps {
  value: string;
  language: string;
  onChange?: (value: string) => void;
  onSave?: () => void;
  readOnly?: boolean;
  className?: string;
  onKeyDown?: (e: KeyboardEvent) => void;
}

export function CodeEditor({
  value,
  language,
  onChange,
  onSave,
  readOnly = false,
  className,
  onKeyDown,
}: CodeEditorProps) {
  const editorRef = useRef<editor.IStandaloneCodeEditor | null>(null);
  const { editorTheme: theme } = useTheme();

  const handleEditorDidMount = (editor: editor.IStandaloneCodeEditor) => {
    editorRef.current = editor;

    // Add keyboard shortcuts
    editor.addCommand(
      // Ctrl+S / Cmd+S
      2048 + 49, // Monaco.KeyMod.CtrlCmd | Monaco.KeyCode.KeyS
      () => {
        onSave?.();
      }
    );

    // Add Ctrl+Enter for code execution
    if (onKeyDown) {
      editor.addCommand(
        2048 + 3, // Monaco.KeyMod.CtrlCmd | Monaco.KeyCode.Enter
        () => {
          const event = new KeyboardEvent('keydown', {
            key: 'Enter',
            ctrlKey: true,
            metaKey: false,
          });
          onKeyDown(event);
        }
      );
    }

    // Focus the editor
    editor.focus();
  };

  const handleChange = (value: string | undefined) => {
    if (value !== undefined) {
      onChange?.(value);
    }
  };

  const editorOptions: editor.IStandaloneEditorConstructionOptions = {
    fontSize: 14,
    lineHeight: 22,
    fontFamily:
      'var(--font-geist-mono), "SF Mono", Monaco, Consolas, monospace',
    minimap: {
      enabled: true,
      side: 'right',
      showSlider: 'mouseover',
      renderCharacters: false,
      maxColumn: 120,
    },
    scrollBeyondLastLine: false,
    automaticLayout: true,
    wordWrap: 'on',
    renderWhitespace: 'selection',
    folding: true,
    lineNumbers: 'on',
    glyphMargin: true,
    readOnly,
    contextmenu: true,
    mouseWheelZoom: true,
    smoothScrolling: true,
    cursorBlinking: 'smooth',
    cursorSmoothCaretAnimation: 'on',
    renderLineHighlight: 'all', // Enhanced line highlighting
    selectOnLineNumbers: true,
    roundedSelection: true,
    scrollbar: {
      vertical: 'auto',
      horizontal: 'auto',
      useShadows: false,
      verticalHasArrows: false,
      horizontalHasArrows: false,
      verticalScrollbarSize: 10,
      horizontalScrollbarSize: 10,
    },
    overviewRulerBorder: false,
    hideCursorInOverviewRuler: true,
    bracketPairColorization: {
      enabled: true,
    },
    guides: {
      bracketPairs: true,
      indentation: true,
    },
    suggest: {
      showKeywords: true,
      showSnippets: true,
      showFunctions: true,
      showConstructors: true,
      showFields: true,
      showVariables: true,
      showClasses: true,
      showModules: true,
      showProperties: true,
      showEvents: true,
      showOperators: true,
      showUnits: true,
      showValues: true,
      showConstants: true,
      showEnums: true,
      showEnumMembers: true,
      showKeywords: true,
      showText: true,
      showColors: true,
      showFiles: true,
      showReferences: true,
      showFolders: true,
      showTypeParameters: true,
      showUsers: true,
      showIssues: true,
    },
  };

  return (
    <div className={cn('flex flex-col h-full min-h-0', className)}>
      <div className="flex-1 min-h-0">
        <Editor
          height="100%"
          language={language}
          value={value}
          theme={theme}
          onChange={handleChange}
          onMount={handleEditorDidMount}
          options={editorOptions}
          loading={
            <div className="flex items-center justify-center h-full min-h-[200px]">
              <div className="text-muted-foreground">Loading editor...</div>
            </div>
          }
        />
      </div>
    </div>
  );
}
