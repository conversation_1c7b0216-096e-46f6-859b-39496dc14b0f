'use client';

import { Check, Save, AlertCircle, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SaveIndicatorProps {
  status: 'saved' | 'saving' | 'error' | 'unsaved';
  message?: string;
  className?: string;
}

export function SaveIndicator({ status, message, className }: SaveIndicatorProps) {
  const getStatusConfig = () => {
    switch (status) {
      case 'saved':
        return {
          icon: <Check className="h-3.5 w-3.5" />,
          text: message || 'Saved',
          styles: 'status-saved text-status-saved bg-success/10 border-success/20'
        };
      case 'saving':
        return {
          icon: <Loader2 className="h-3.5 w-3.5 animate-spin" />,
          text: message || 'Saving...',
          styles: 'text-primary bg-primary/10 border-primary/20'
        };
      case 'error':
        return {
          icon: <AlertCircle className="h-3.5 w-3.5" />,
          text: message || 'Save failed',
          styles: 'status-error text-status-error bg-error/10 border-error/20'
        };
      case 'unsaved':
        return {
          icon: <Save className="h-3.5 w-3.5" />,
          text: message || 'Modified',
          styles: 'status-modified text-status-modified bg-warning/10 border-warning/20'
        };
      default:
        return {
          icon: null,
          text: '',
          styles: 'text-muted-foreground bg-muted/50 border-border'
        };
    }
  };

  const statusConfig = getStatusConfig();

  if (!statusConfig.icon && !statusConfig.text) return null;

  return (
    <div className={cn(
      'status-indicator inline-flex items-center gap-1.5 px-2 py-1 text-xs font-medium border rounded-md transition-all duration-200 animate-fade-in',
      statusConfig.styles,
      className
    )}>
      {statusConfig.icon}
      <span>{statusConfig.text}</span>
    </div>
  );
}