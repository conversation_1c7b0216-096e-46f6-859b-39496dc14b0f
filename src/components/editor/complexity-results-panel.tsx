'use client';

import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Clock,
  BarChart3,
  TrendingUp,
  Info,
  AlertTriangle,
  AlertCircle,
  CheckCircle,
  ChevronDown,
  ChevronUp,
  Target,
  Lightbulb,
} from 'lucide-react';
import {
  ComplexityResult,
  ComplexityClass,
  OptimizationSuggestion,
  PerformanceMeasurement,
} from '@/lib/complexity-analysis/types';

interface ComplexityResultsPanelProps {
  result: ComplexityResult | null;
  isAnalyzing?: boolean;
  className?: string;
}

const EfficiencyIcon = ({ efficiency }: { efficiency: string }) => {
  switch (efficiency) {
    case 'excellent':
      return <CheckCircle className="h-4 w-4 text-green-400" />;
    case 'good':
      return <CheckCircle className="h-4 w-4 text-blue-400" />;
    case 'fair':
      return <AlertTriangle className="h-4 w-4 text-yellow-400" />;
    case 'poor':
      return <AlertCircle className="h-4 w-4 text-orange-400" />;
    case 'terrible':
      return <AlertCircle className="h-4 w-4 text-red-400" />;
    default:
      return <Info className="h-4 w-4 text-gray-400" />;
  }
};

const SeverityIcon = ({ severity }: { severity: string }) => {
  switch (severity) {
    case 'info':
      return <Info className="h-4 w-4 text-blue-400" />;
    case 'warning':
      return <AlertTriangle className="h-4 w-4 text-yellow-400" />;
    case 'critical':
      return <AlertCircle className="h-4 w-4 text-red-400" />;
    default:
      return <Info className="h-4 w-4 text-gray-400" />;
  }
};

const ComplexityClassCard = ({
  complexity,
  confidence,
  type,
}: {
  complexity: ComplexityClass | null;
  confidence: number;
  type: 'time' | 'space';
}) => {
  if (!complexity) {
    return (
      <Card className="border-dashed border-2 border-muted-foreground/20">
        <CardContent className="flex items-center justify-center h-32 text-muted-foreground">
          <div className="text-center">
            <BarChart3 className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No {type} complexity detected</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Improved color scheme with better contrast and modern colors

  const bgClass =
    type === 'time'
      ? 'bg-gradient-to-br from-emerald-950/30 to-teal-950/20 border-emerald-500/30'
      : 'bg-gradient-to-br from-violet-950/30 to-indigo-950/20 border-violet-500/30';

  const textClass = type === 'time' ? 'text-emerald-200' : 'text-violet-200';

  const iconClass = type === 'time' ? 'text-emerald-400' : 'text-violet-400';

  return (
    <Card className={`${bgClass} transition-all hover:shadow-lg`}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-sm">
          {type === 'time' ? (
            <Clock className={`h-4 w-4 ${iconClass}`} />
          ) : (
            <BarChart3 className={`h-4 w-4 ${iconClass}`} />
          )}
          <span className={`${iconClass} font-medium`}>
            {type === 'time' ? 'Time Complexity' : 'Space Complexity'}
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Big O Notation */}
        <div className="text-center">
          <div className={`text-2xl font-mono font-bold ${textClass}`}>
            {complexity.notation}
          </div>
          <div className={`text-xs mt-1 ${iconClass}/80`}>
            {complexity.description}
          </div>
        </div>

        {/* Efficiency Rating */}
        <div className="flex items-center justify-between">
          <span className="text-xs text-muted-foreground">Efficiency:</span>
          <div className="flex items-center gap-1">
            <EfficiencyIcon efficiency={complexity.efficiency} />
            <Badge
              variant={
                complexity.efficiency === 'excellent' ||
                complexity.efficiency === 'good'
                  ? 'default'
                  : complexity.efficiency === 'fair'
                  ? 'secondary'
                  : 'destructive'
              }
              className="text-xs capitalize"
            >
              {complexity.efficiency}
            </Badge>
          </div>
        </div>

        {/* Confidence Score */}
        <div className="flex items-center justify-between">
          <span className="text-xs text-muted-foreground">Confidence:</span>
          <div className="flex items-center gap-2">
            <div className="w-16 h-2 bg-muted rounded-full overflow-hidden">
              <div
                className={`h-full transition-all duration-500 ${
                  confidence >= 0.8
                    ? 'bg-green-400'
                    : confidence >= 0.6
                    ? 'bg-yellow-400'
                    : 'bg-red-400'
                }`}
                style={{ width: `${confidence * 100}%` }}
              />
            </div>
            <span className="text-xs font-mono">
              {Math.round(confidence * 100)}%
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const PerformanceChart = ({
  measurements,
}: {
  measurements: PerformanceMeasurement[];
}) => {
  if (!measurements || measurements.length === 0) {
    return (
      <div className="h-48 bg-muted/20 rounded-lg flex items-center justify-center border-2 border-dashed border-muted-foreground/20">
        <div className="text-center text-muted-foreground">
          <TrendingUp className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p className="text-sm">No performance data available</p>
        </div>
      </div>
    );
  }

  // Find max values for scaling
  const maxTime = Math.max(...measurements.map((m) => m.executionTime));
  const maxMemory = Math.max(...measurements.map((m) => m.memoryUsage));
  const maxSize = Math.max(...measurements.map((m) => m.inputSize));

  // Chart dimensions - responsive
  const chartWidth = 400;
  const chartHeight = 180;
  const padding = 40;
  const plotWidth = chartWidth - 2 * padding;
  const plotHeight = chartHeight - 2 * padding;

  // Safety check for valid data
  if (maxTime === 0 || maxSize === 0) {
    return (
      <div className="h-48 bg-muted/20 rounded-lg flex items-center justify-center border-2 border-dashed border-muted-foreground/20">
        <div className="text-center text-muted-foreground">
          <TrendingUp className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p className="text-sm">Invalid performance data</p>
        </div>
      </div>
    );
  }

  // Scale functions
  const scaleX = (inputSize: number) =>
    (inputSize / maxSize) * plotWidth + padding;
  const scaleTimeY = (time: number) =>
    chartHeight - padding - (time / maxTime) * plotHeight;
  const scaleMemoryY = (memory: number) =>
    chartHeight - padding - (memory / maxMemory) * plotHeight;

  // Generate path strings for lines
  const timePathData = measurements
    .map(
      (m, i) =>
        `${i === 0 ? 'M' : 'L'} ${scaleX(m.inputSize)} ${scaleTimeY(
          m.executionTime
        )}`
    )
    .join(' ');

  const memoryPathData = measurements
    .map(
      (m, i) =>
        `${i === 0 ? 'M' : 'L'} ${scaleX(m.inputSize)} ${scaleMemoryY(
          m.memoryUsage
        )}`
    )
    .join(' ');

  return (
    <div className="space-y-4">
      {/* Time Chart */}
      <div className="space-y-2">
        <h4 className="text-sm font-medium text-emerald-300 flex items-center gap-2">
          <Clock className="h-4 w-4 text-emerald-400" />
          Execution Time vs Input Size
        </h4>
        <div className="bg-slate-950/50 rounded-lg p-4">
          <svg
            width={chartWidth}
            height={chartHeight}
            className="w-full h-auto"
          >
            {/* Grid lines */}
            <defs>
              <pattern
                id="grid"
                width="40"
                height="20"
                patternUnits="userSpaceOnUse"
              >
                <path
                  d="M 40 0 L 0 0 0 20"
                  fill="none"
                  stroke="rgb(71 85 105)"
                  strokeWidth="0.5"
                  opacity="0.3"
                />
              </pattern>
            </defs>
            <rect width={chartWidth} height={chartHeight} fill="url(#grid)" />

            {/* Axes */}
            <line
              x1={padding}
              y1={padding}
              x2={padding}
              y2={chartHeight - padding}
              stroke="rgb(148 163 184)"
              strokeWidth="1"
            />
            <line
              x1={padding}
              y1={chartHeight - padding}
              x2={chartWidth - padding}
              y2={chartHeight - padding}
              stroke="rgb(148 163 184)"
              strokeWidth="1"
            />

            {/* Time line */}
            <path
              d={timePathData}
              fill="none"
              stroke="rgb(59 130 246)"
              strokeWidth="2"
            />

            {/* Data points */}
            {measurements.map((m, i) => (
              <circle
                key={i}
                cx={scaleX(m.inputSize)}
                cy={scaleTimeY(m.executionTime)}
                r="3"
                fill="rgb(59 130 246)"
                className="hover:r-4 transition-all cursor-pointer"
              >
                <title>{`Size: ${m.inputSize}, Time: ${m.executionTime.toFixed(
                  2
                )}ms`}</title>
              </circle>
            ))}

            {/* Labels */}
            <text
              x={padding}
              y={15}
              fill="rgb(148 163 184)"
              fontSize="10"
              textAnchor="start"
            >
              {maxTime.toFixed(1)}ms
            </text>
            <text
              x={padding}
              y={chartHeight - 25}
              fill="rgb(148 163 184)"
              fontSize="10"
              textAnchor="start"
            >
              0ms
            </text>
            <text
              x={chartWidth - padding}
              y={chartHeight - 10}
              fill="rgb(148 163 184)"
              fontSize="10"
              textAnchor="end"
            >
              {maxSize}
            </text>
            <text
              x={padding + 5}
              y={chartHeight - 10}
              fill="rgb(148 163 184)"
              fontSize="10"
              textAnchor="start"
            >
              0
            </text>
          </svg>
        </div>
      </div>

      {/* Memory Chart */}
      <div className="space-y-2">
        <h4 className="text-sm font-medium text-violet-300 flex items-center gap-2">
          <BarChart3 className="h-4 w-4 text-violet-400" />
          Memory Usage vs Input Size
        </h4>
        <div className="bg-slate-950/50 rounded-lg p-4">
          <svg
            width={chartWidth}
            height={chartHeight}
            className="w-full h-auto"
          >
            {/* Grid */}
            <rect width={chartWidth} height={chartHeight} fill="url(#grid)" />

            {/* Axes */}
            <line
              x1={padding}
              y1={padding}
              x2={padding}
              y2={chartHeight - padding}
              stroke="rgb(148 163 184)"
              strokeWidth="1"
            />
            <line
              x1={padding}
              y1={chartHeight - padding}
              x2={chartWidth - padding}
              y2={chartHeight - padding}
              stroke="rgb(148 163 184)"
              strokeWidth="1"
            />

            {/* Memory line */}
            <path
              d={memoryPathData}
              fill="none"
              stroke="rgb(147 51 234)"
              strokeWidth="2"
            />

            {/* Data points */}
            {measurements.map((m, i) => (
              <circle
                key={i}
                cx={scaleX(m.inputSize)}
                cy={scaleMemoryY(m.memoryUsage)}
                r="3"
                fill="rgb(147 51 234)"
                className="hover:r-4 transition-all cursor-pointer"
              >
                <title>{`Size: ${m.inputSize}, Memory: ${(
                  m.memoryUsage / 1024
                ).toFixed(1)}KB`}</title>
              </circle>
            ))}

            {/* Labels */}
            <text
              x={padding}
              y={15}
              fill="rgb(148 163 184)"
              fontSize="10"
              textAnchor="start"
            >
              {(maxMemory / 1024).toFixed(1)}KB
            </text>
            <text
              x={padding}
              y={chartHeight - 25}
              fill="rgb(148 163 184)"
              fontSize="10"
              textAnchor="start"
            >
              0KB
            </text>
            <text
              x={chartWidth - padding}
              y={chartHeight - 10}
              fill="rgb(148 163 184)"
              fontSize="10"
              textAnchor="end"
            >
              {maxSize}
            </text>
            <text
              x={padding + 5}
              y={chartHeight - 10}
              fill="rgb(148 163 184)"
              fontSize="10"
              textAnchor="start"
            >
              0
            </text>
          </svg>
        </div>
      </div>

      {/* Legend */}
      <div className="flex items-center justify-center gap-6 text-xs">
        <div className="flex items-center gap-2">
          <div className="w-3 h-0.5 bg-blue-400"></div>
          <span className="text-blue-300">Execution Time</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-0.5 bg-purple-400"></div>
          <span className="text-purple-300">Memory Usage</span>
        </div>
      </div>
    </div>
  );
};

const SuggestionCard = ({
  suggestion,
}: {
  suggestion: OptimizationSuggestion;
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <Card className="border-l-4 border-l-yellow-400">
      <CardContent className="pt-4">
        <div className="flex items-start gap-3">
          <SeverityIcon severity={suggestion.severity} />
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium text-sm text-yellow-300">
                {suggestion.title}
              </h4>
              <Badge variant="outline" className="text-xs">
                {suggestion.type}
              </Badge>
            </div>

            <p className="text-sm text-yellow-200/80 mb-2">
              {suggestion.description}
            </p>

            {suggestion.expectedImprovement && (
              <div className="text-xs text-yellow-200/60 mb-2">
                <strong>Expected improvement:</strong>{' '}
                {suggestion.expectedImprovement}
              </div>
            )}

            {suggestion.codeExample && (
              <div className="mt-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="h-6 px-2 text-xs text-yellow-300 hover:text-yellow-200"
                >
                  {isExpanded ? (
                    <>
                      <ChevronUp className="h-3 w-3 mr-1" />
                      Hide Example
                    </>
                  ) : (
                    <>
                      <ChevronDown className="h-3 w-3 mr-1" />
                      Show Example
                    </>
                  )}
                </Button>

                {isExpanded && (
                  <div className="mt-2 bg-slate-950/50 rounded border p-3">
                    <pre className="text-xs text-green-400 whitespace-pre-wrap break-words">
                      {suggestion.codeExample}
                    </pre>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export function ComplexityResultsPanel({
  result,
  isAnalyzing = false,
  className = '',
}: ComplexityResultsPanelProps) {
  const [activeTab, setActiveTab] = useState<
    'overview' | 'performance' | 'suggestions'
  >('overview');

  if (isAnalyzing) {
    return (
      <div className={`p-4 ${className}`}>
        <Card>
          <CardContent className="flex items-center justify-center h-40">
            <div className="text-center">
              <TrendingUp className="h-8 w-8 mx-auto mb-3 text-primary animate-pulse" />
              <h3 className="font-medium mb-2">Analyzing Complexity...</h3>
              <p className="text-sm text-muted-foreground">
                Running performance measurements across different input sizes
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!result) {
    return (
      <div className={`p-4 ${className}`}>
        <Card className="border-dashed border-2 border-muted-foreground/20">
          <CardContent className="flex items-center justify-center h-40">
            <div className="text-center text-muted-foreground">
              <BarChart3 className="h-8 w-8 mx-auto mb-3 opacity-50" />
              <h3 className="font-medium mb-2">No Analysis Available</h3>
              <p className="text-sm">
                Enable complexity analysis and run your code to see performance
                insights
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!result.executionSuccess) {
    return (
      <div className={`p-4 ${className}`}>
        <Card className="border-red-500/20 bg-red-950/20">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-400">
              <AlertCircle className="h-5 w-5" />
              Analysis Failed
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-red-300">
              {result.errorMessage ||
                'Complexity analysis could not be completed.'}
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={`p-4 space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5 text-primary" />
          <h2 className="font-semibold">Complexity Analysis</h2>
        </div>
        <Badge variant="outline" className="text-xs">
          {new Date(result.timestamp).toLocaleTimeString()}
        </Badge>
      </div>

      {/* Tab Navigation */}
      <div className="flex gap-1 p-1 bg-muted rounded-lg">
        {[
          { id: 'overview' as const, label: 'Overview', icon: Target },
          { id: 'performance' as const, label: 'Performance', icon: BarChart3 },
          { id: 'suggestions' as const, label: 'Suggestions', icon: Lightbulb },
        ].map((tab) => (
          <Button
            key={tab.id}
            variant={activeTab === tab.id ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab(tab.id)}
            className="flex-1 flex items-center gap-2"
          >
            <tab.icon className="h-4 w-4" />
            <span className="hidden sm:inline">{tab.label}</span>
          </Button>
        ))}
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-4">
          {/* Complexity Classes */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <ComplexityClassCard
              complexity={result.timeComplexity}
              confidence={result.confidence.time}
              type="time"
            />
            <ComplexityClassCard
              complexity={result.spaceComplexity}
              confidence={result.confidence.space}
              type="space"
            />
          </div>

          {/* Analysis Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Analysis Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Language:</span>
                  <Badge variant="secondary" className="text-xs">
                    {result.language}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Test Cases:</span>
                  <span className="font-mono">
                    {result.measurements.length}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">
                    Time Confidence:
                  </span>
                  <span className="font-mono">
                    {Math.round(result.confidence.time * 100)}%
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">
                    Space Confidence:
                  </span>
                  <span className="font-mono">
                    {Math.round(result.confidence.space * 100)}%
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === 'performance' && (
        <div className="space-y-4">
          {/* Performance Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                Performance Measurements
              </CardTitle>
              <CardDescription>
                Execution time and memory usage across different input sizes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <PerformanceChart measurements={result.measurements} />
            </CardContent>
          </Card>

          {/* Raw Measurements Table */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Raw Measurements</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-2">Input Size</th>
                      <th className="text-left p-2">Execution Time</th>
                      <th className="text-left p-2">Memory Usage</th>
                      <th className="text-left p-2">Peak Memory</th>
                    </tr>
                  </thead>
                  <tbody>
                    {result.measurements
                      .slice(0, 10)
                      .map((measurement, index) => (
                        <tr key={index} className="border-b">
                          <td className="p-2 font-mono">
                            {measurement.inputSize}
                          </td>
                          <td className="p-2 font-mono">
                            {measurement.executionTime.toFixed(2)}ms
                          </td>
                          <td className="p-2 font-mono">
                            {(measurement.memoryUsage / 1024).toFixed(1)}KB
                          </td>
                          <td className="p-2 font-mono">
                            {(measurement.peakMemory / 1024).toFixed(1)}KB
                          </td>
                        </tr>
                      ))}
                  </tbody>
                </table>
                {result.measurements.length > 10 && (
                  <p className="text-xs text-muted-foreground mt-2 text-center">
                    Showing first 10 of {result.measurements.length}{' '}
                    measurements
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === 'suggestions' && (
        <div className="space-y-4">
          {result.suggestions.length > 0 ? (
            <>
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Lightbulb className="h-4 w-4" />
                    Optimization Opportunities
                  </CardTitle>
                  <CardDescription>
                    Suggestions to improve your algorithm&apos;s performance
                  </CardDescription>
                </CardHeader>
              </Card>

              <div className="space-y-3">
                {result.suggestions.map((suggestion, index) => (
                  <SuggestionCard key={index} suggestion={suggestion} />
                ))}
              </div>
            </>
          ) : (
            <Card className="border-dashed border-2 border-muted-foreground/20">
              <CardContent className="flex items-center justify-center h-32">
                <div className="text-center text-muted-foreground">
                  <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-400 opacity-50" />
                  <h3 className="font-medium mb-1">No Suggestions</h3>
                  <p className="text-sm">
                    Your algorithm appears to be well-optimized for the given
                    analysis
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  );
}
