'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  X,
  FileText,
  Code,
  Database,
  Image,
  Settings,
  FileCode,
  FileType,
  Braces,
  Globe,
  Palette,
  FileJson,
  BookOpen,
  Cog,
} from 'lucide-react';
import { cn, formatRelativeTime, formatExactTime } from '@/lib/utils';
import type { File } from '@/types';

interface FileTabsProps {
  files: File[];
  activeFile: File | null;
  onFileSelect: (file: File) => void;
  onFileClose?: (file: File) => void;
  maxTabs?: number;
}

const getFileIcon = (language: string, fileName: string) => {
  const ext = fileName.split('.').pop()?.toLowerCase();

  switch (language.toLowerCase()) {
    case 'javascript':
      return FileCode; // JavaScript icon
    case 'typescript':
      return FileType; // TypeScript icon
    case 'python':
      return Code; // Python icon
    case 'java':
      return FileCode; // Java icon
    case 'cpp':
    case 'c':
      return Cog; // C/C++ icon
    case 'html':
      return Globe; // HTML icon
    case 'css':
      return Palette; // CSS icon
    case 'json':
      return FileJson; // JSON icon
    case 'markdown':
    case 'md':
      return BookOpen; // Markdown icon
    case 'yaml':
    case 'yml':
      return Settings; // YAML icon
    default:
      // Fallback to file extension
      switch (ext) {
        case 'js':
          return FileCode;
        case 'ts':
          return FileType;
        case 'py':
          return Code;
        case 'java':
          return FileCode;
        case 'cpp':
        case 'c':
          return Cog;
        case 'html':
          return Globe;
        case 'css':
          return Palette;
        case 'json':
          return FileJson;
        case 'md':
          return BookOpen;
        case 'yml':
        case 'yaml':
          return Settings;
        default:
          return FileText;
      }
  }
};

const getLanguageColor = (language: string) => {
  switch (language.toLowerCase()) {
    case 'javascript':
      return 'text-yellow-600 dark:text-yellow-400';
    case 'typescript':
      return 'text-blue-600 dark:text-blue-400';
    case 'python':
      return 'text-success dark:text-success';
    case 'java':
      return 'text-orange-600 dark:text-orange-400';
    case 'cpp':
    case 'c':
      return 'text-blue-700 dark:text-blue-300';
    case 'html':
      return 'text-orange-500 dark:text-orange-400';
    case 'css':
      return 'text-blue-500 dark:text-blue-400';
    case 'json':
      return 'text-muted-foreground';
    case 'markdown':
    case 'md':
      return 'text-gray-700 dark:text-gray-300';
    default:
      return 'text-muted-foreground';
  }
};

export function FileTabs({
  files,
  activeFile,
  onFileSelect,
  onFileClose,
  maxTabs = 8,
}: FileTabsProps) {
  const [showAllTabs, setShowAllTabs] = useState(false);

  // Show recently accessed files first, then alphabetical
  const sortedFiles = [...files].sort((a, b) => {
    // Active file first
    if (a.$id === activeFile?.$id) return -1;
    if (b.$id === activeFile?.$id) return 1;

    // Then by last modified (most recent first)
    return new Date(b.$updatedAt).getTime() - new Date(a.$updatedAt).getTime();
  });

  const visibleFiles = showAllTabs
    ? sortedFiles
    : sortedFiles.slice(0, maxTabs);
  const hiddenCount = sortedFiles.length - visibleFiles.length;

  if (files.length === 0) {
    return null;
  }

  return (
    <div className="border-b border-border bg-muted/20">
      <div className="flex items-center">
        <ScrollArea className="flex-1">
          <div className="flex items-center" role="tablist" aria-label="Open files">
            {visibleFiles.map((file) => {
              const FileIcon = getFileIcon(file.language, file.name);
              const isActive = activeFile?.$id === file.$id;

              return (
                <div
                  key={file.$id}
                  className={cn(
                    'file-tab flex items-center gap-2 px-3 py-2.5 border-r border-border cursor-pointer group relative min-w-0',
                    'transition-all duration-150 ease-in-out',
                    'focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-1',
                    isActive
                      ? 'bg-editor-background text-foreground border-b-2 border-b-primary shadow-sm'
                      : 'hover:bg-accent-hover text-muted-foreground hover:text-foreground'
                  )}
                  role="tab"
                  tabIndex={0}
                  aria-selected={isActive}
                  aria-label={`Open file ${file.name} (${file.language}) - Modified ${formatRelativeTime(file.$updatedAt)}${file.lastExecuted ? `, Last run ${formatRelativeTime(file.lastExecuted)}` : ''}`}
                  onClick={() => onFileSelect(file)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      onFileSelect(file);
                    }
                  }}
                  title={`${file.name} (${file.language})
Modified: ${formatExactTime(file.$updatedAt)}${file.lastExecuted ? `
Last executed: ${formatExactTime(file.lastExecuted)}` : ''}${file.executionCount ? `
Executions: ${file.executionCount}x` : ''}`}
                >
                  <FileIcon
                    className={cn(
                      'h-4 w-4 flex-shrink-0',
                      getLanguageColor(file.language)
                    )}
                  />
                  
                  <div className="flex items-center gap-1.5 min-w-0">
                    <span
                      className={cn(
                        'text-sm font-medium truncate max-w-[120px]',
                        isActive ? 'font-semibold' : 'font-normal'
                      )}
                    >
                      {file.name}
                    </span>
                    
                    {/* Status indicators */}
                    <div className="flex items-center gap-1">
                      {/* Modified indicator */}
                      {file.isModified && (
                        <div 
                          className="w-1.5 h-1.5 rounded-full bg-warning flex-shrink-0 animate-pulse" 
                          title="Unsaved changes"
                        />
                      )}
                      
                      {/* Recently executed indicator */}
                      {file.lastExecuted && (
                        <div
                          className={cn(
                            "w-1.5 h-1.5 rounded-full flex-shrink-0",
                            // Show green dot if executed recently (within 5 minutes)
                            new Date().getTime() - new Date(file.lastExecuted).getTime() < 5 * 60 * 1000
                              ? "bg-success animate-pulse"
                              : "bg-muted-foreground"
                          )}
                          title={`Last executed: ${formatExactTime(file.lastExecuted)}`}
                        />
                      )}
                    </div>
                  </div>

                  {onFileClose && (
                    <Button
                      size="sm"
                      variant="ghost"
                      className={cn(
                        'h-5 w-5 p-0 ml-1 flex-shrink-0',
                        'opacity-0 group-hover:opacity-100',
                        'hover:bg-destructive/20 hover:text-destructive',
                        'transition-all duration-150',
                        'rounded-sm'
                      )}
                      onClick={(e) => {
                        e.stopPropagation();
                        onFileClose(file);
                      }}
                      title="Close file"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              );
            })}

            {hiddenCount > 0 && (
              <Button
                size="sm"
                variant="ghost"
                className="px-3 py-2 text-xs text-muted-foreground hover:text-foreground"
                onClick={() => setShowAllTabs(!showAllTabs)}
              >
                {showAllTabs ? 'Show Less' : `+${hiddenCount} more`}
              </Button>
            )}
          </div>
        </ScrollArea>

        {/* Tab actions */}
        <div className="flex items-center px-2 border-l border-border">
          <span className="text-xs text-muted-foreground">
            {files.length} file{files.length !== 1 ? 's' : ''}
          </span>
        </div>
      </div>
    </div>
  );
}
