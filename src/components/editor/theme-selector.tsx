'use client';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { Pa<PERSON>, <PERSON>, <PERSON>, <PERSON>, Monitor } from 'lucide-react';
import { useTheme } from '@/contexts/theme-context';

interface Theme {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
}

const themes: Theme[] = [
  {
    id: 'light',
    name: 'Light',
    description: 'Clean light theme',
    icon: Sun,
  },
  {
    id: 'dark',
    name: 'Dark',
    description: 'Easy on the eyes',
    icon: Moon,
  },
  {
    id: 'system',
    name: 'System',
    description: 'Follow system preference',
    icon: Monitor,
  },
];

interface ThemeSelectorProps {
  currentTheme?: string;
  onThemeChange?: (theme: string) => void;
}

export function ThemeSelector({
  currentTheme = 'system',
  onThemeChange,
}: ThemeSelectorProps) {
  // Import the theme context
  const { theme: selectedTheme, setTheme } = useTheme();

  const handleThemeChange = (themeId: string) => {
    setTheme(themeId as 'light' | 'dark' | 'system');
    onThemeChange?.(themeId);
  };

  const currentThemeData =
    themes.find((t) => t.id === selectedTheme) || themes[2];

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="flex items-center gap-2 text-muted-foreground hover:text-foreground"
        >
          <Palette className="h-3 w-3" />
          <span className="text-xs">Theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <div className="px-2 py-1.5">
          <p className="text-xs font-medium text-muted-foreground">
            Choose theme
          </p>
        </div>
        <DropdownMenuSeparator />
        {themes.map((theme) => {
          const Icon = theme.icon;
          const isSelected = theme.id === selectedTheme;

          return (
            <DropdownMenuItem
              key={theme.id}
              onClick={() => handleThemeChange(theme.id)}
              className="flex items-center gap-3 cursor-pointer"
            >
              <Icon className="h-4 w-4" />
              <div className="flex-1">
                <div className="font-medium">{theme.name}</div>
                <div className="text-xs text-muted-foreground">
                  {theme.description}
                </div>
              </div>
              {isSelected && <Check className="h-3 w-3 text-primary" />}
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
