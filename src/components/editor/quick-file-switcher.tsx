'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Search,
  File,
  Clock,
  FileCode,
  FileType,
  Code,
  Cog,
  Globe,
  Palette,
  FileJson,
  BookOpen,
  Settings,
  FileText,
} from 'lucide-react';

const getFileIcon = (language: string, fileName: string) => {
  const ext = fileName.split('.').pop()?.toLowerCase();

  switch (language.toLowerCase()) {
    case 'javascript':
      return FileCode;
    case 'typescript':
      return FileType;
    case 'python':
      return Code;
    case 'java':
      return FileCode;
    case 'cpp':
    case 'c':
      return Cog;
    case 'html':
      return Globe;
    case 'css':
      return Palette;
    case 'json':
      return FileJson;
    case 'markdown':
    case 'md':
      return BookOpen;
    case 'yaml':
    case 'yml':
      return Settings;
    default:
      switch (ext) {
        case 'js':
          return FileCode;
        case 'ts':
          return FileType;
        case 'py':
          return Code;
        case 'java':
          return FileCode;
        case 'cpp':
        case 'c':
          return Cog;
        case 'html':
          return Globe;
        case 'css':
          return Palette;
        case 'json':
          return FileJson;
        case 'md':
          return BookOpen;
        case 'yml':
        case 'yaml':
          return Settings;
        default:
          return FileText;
      }
  }
};
import { cn } from '@/lib/utils';
import type { File as FileType } from '@/types';

interface QuickFileSwitcherProps {
  files: FileType[];
  activeFile: FileType | null;
  onFileSelect: (file: FileType) => void;
  onClose: () => void;
  isOpen: boolean;
}

export function QuickFileSwitcher({
  files,
  activeFile,
  onFileSelect,
  onClose,
  isOpen,
}: QuickFileSwitcherProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);
  const inputRef = useRef<HTMLInputElement>(null);

  // Filter files based on search query
  const filteredFiles = files.filter((file) =>
    file.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Sort by relevance: exact matches first, then partial matches, then by recent
  const sortedFiles = filteredFiles.sort((a, b) => {
    const aName = a.name.toLowerCase();
    const bName = b.name.toLowerCase();
    const query = searchQuery.toLowerCase();

    // Exact matches first
    if (aName === query && bName !== query) return -1;
    if (bName === query && aName !== query) return 1;

    // Starts with query
    if (aName.startsWith(query) && !bName.startsWith(query)) return -1;
    if (bName.startsWith(query) && !aName.startsWith(query)) return 1;

    // Then by last modified
    return new Date(b.$updatedAt).getTime() - new Date(a.$updatedAt).getTime();
  });

  // Reset selection when search changes
  useEffect(() => {
    setSelectedIndex(0);
  }, [searchQuery]);

  // Focus input when opened
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex((prev) =>
            prev < sortedFiles.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex((prev) =>
            prev > 0 ? prev - 1 : sortedFiles.length - 1
          );
          break;
        case 'Enter':
          e.preventDefault();
          if (sortedFiles[selectedIndex]) {
            onFileSelect(sortedFiles[selectedIndex]);
            onClose();
          }
          break;
        case 'Escape':
          e.preventDefault();
          onClose();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, sortedFiles, selectedIndex, onFileSelect, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-start justify-center pt-20 z-50">
      <div className="bg-card border border-border rounded-lg shadow-lg w-full max-w-md mx-4">
        {/* Header */}
        <div className="p-4 border-b border-border">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              ref={inputRef}
              placeholder="Search files..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9"
            />
          </div>
        </div>

        {/* File List */}
        <ScrollArea className="max-h-80">
          <div className="p-2">
            {sortedFiles.length > 0 ? (
              sortedFiles.map((file, index) => (
                <button
                  key={file.$id}
                  onClick={() => {
                    onFileSelect(file);
                    onClose();
                  }}
                  className={cn(
                    'w-full flex items-center gap-3 px-3 py-2 rounded-md text-left transition-colors',
                    index === selectedIndex
                      ? 'bg-accent text-accent-foreground'
                      : 'hover:bg-accent/50',
                    activeFile?.$id === file.$id && 'text-primary'
                  )}
                >
                  {(() => {
                    const FileIcon = getFileIcon(file.language, file.name);
                    return (
                      <FileIcon
                        className="h-4 w-4 flex-shrink-0"
                        title={file.language}
                      />
                    );
                  })()}
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">{file.name}</div>
                    <div className="text-xs text-muted-foreground">
                      {file.language}
                    </div>
                  </div>
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <Clock className="h-3 w-3" />
                    {new Date(file.$updatedAt).toLocaleDateString()}
                  </div>
                </button>
              ))
            ) : (
              <div className="text-center py-6 text-muted-foreground">
                <File className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No files found</p>
                {searchQuery && (
                  <p className="text-xs mt-1">Try a different search term</p>
                )}
              </div>
            )}
          </div>
        </ScrollArea>

        {/* Footer */}
        <div className="p-3 border-t border-border bg-muted/30">
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-4">
              <span>↑↓ Navigate</span>
              <span>↵ Select</span>
              <span>Esc Close</span>
            </div>
            <span>{sortedFiles.length} files</span>
          </div>
        </div>
      </div>
    </div>
  );
}
