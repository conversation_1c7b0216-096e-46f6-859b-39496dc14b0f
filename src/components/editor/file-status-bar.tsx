'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Info,
  FileText,
  Clock,
  Play,
  Circle,
  CheckCircle,
  AlertCircle,
} from 'lucide-react';
import { cn, formatFileSize, formatRelativeTime } from '@/lib/utils';
import type { File } from '@/types';

interface FileStatusBarProps {
  file?: File;
  cursorPosition?: { line: number; column: number };
  onShowProperties?: () => void;
  className?: string;
}

export function FileStatusBar({ 
  file, 
  cursorPosition,
  onShowProperties,
  className 
}: FileStatusBarProps) {
  if (!file) return null;

  const getStatusIcon = () => {
    if (file.isModified) {
      return <Circle className="h-3 w-3 text-warning fill-warning" />;
    }
    return <CheckCircle className="h-3 w-3 text-success" />;
  };

  const getStatusText = () => {
    if (file.isModified) return 'Modified';
    return 'Saved';
  };

  const getLanguageDisplayName = (language: string) => {
    const displayNames: Record<string, string> = {
      'javascript': 'JavaScript',
      'typescript': 'TypeScript',
      'python': 'Python',
      'java': 'Java',
      'cpp': 'C++',
      'c': 'C',
      'go': 'Go',
      'rust': 'Rust',
      'html': 'HTML',
      'css': 'CSS',
      'json': 'JSON',
      'markdown': 'Markdown',
      'text': 'Plain Text',
    };
    return displayNames[language.toLowerCase()] || language;
  };

  const lineCount = file.content.split('\n').length;
  const charCount = file.content.length;

  return (
    <div className={cn(
      'flex items-center justify-between px-4 py-2 bg-muted/30 border-t border-border text-xs',
      'text-muted-foreground',
      className
    )}>
      {/* Left section - File info */}
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <FileText className="h-3 w-3" />
          <span className="font-medium text-foreground truncate max-w-[200px]" title={file.name}>
            {file.name}
          </span>
        </div>

        <div className="flex items-center gap-1">
          {getStatusIcon()}
          <span>{getStatusText()}</span>
        </div>

        {file.size && (
          <div className="flex items-center gap-1">
            <span>{formatFileSize(file.size)}</span>
          </div>
        )}

        <div className="flex items-center gap-1">
          <span>{lineCount} lines</span>
          <span>•</span>
          <span>{charCount} chars</span>
        </div>
      </div>

      {/* Center section - Language and cursor position */}
      <div className="flex items-center gap-4">
        {cursorPosition && (
          <div className="flex items-center gap-1 font-mono">
            <span>Ln {cursorPosition.line}</span>
            <span>•</span>
            <span>Col {cursorPosition.column}</span>
          </div>
        )}
      </div>

      {/* Right section - Language and actions */}
      <div className="flex items-center gap-3">
        {file.lastExecuted && (
          <div className="flex items-center gap-1" title={`Last executed: ${formatRelativeTime(file.lastExecuted)}`}>
            <Play className="h-3 w-3 text-success" />
            <span>Ran {formatRelativeTime(file.lastExecuted)}</span>
            {file.executionCount && file.executionCount > 1 && (
              <Badge variant="outline" className="text-xs h-5 px-1">
                {file.executionCount}x
              </Badge>
            )}
          </div>
        )}

        <Badge 
          variant="outline" 
          className={cn(
            'text-xs h-6 px-2',
            file.language === 'python' && 'bg-green-500/10 text-green-600 border-green-500/20',
            file.language === 'javascript' && 'bg-yellow-500/10 text-yellow-600 border-yellow-500/20',
            file.language === 'typescript' && 'bg-blue-500/10 text-blue-600 border-blue-500/20',
          )}
        >
          {getLanguageDisplayName(file.language)}
        </Badge>

        {onShowProperties && (
          <Button
            variant="ghost"
            size="icon"
            onClick={onShowProperties}
            className="h-6 w-6 hover:bg-accent"
            title="Show file properties"
          >
            <Info className="h-3 w-3" />
          </Button>
        )}
      </div>
    </div>
  );
}