@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Enhanced VS Code-inspired light theme */
  --background: 0 0% 99%;
  --foreground: 0 0% 7%;
  --card: 0 0% 100%;
  --card-foreground: 0 0% 7%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 7%;

  /* Primary colors with better contrast */
  --primary: 142 76% 36%; /* Professional green for actions */
  --primary-foreground: 0 0% 100%;
  --primary-hover: 142 76% 32%;

  /* Secondary and accent colors */
  --secondary: 210 20% 96%;
  --secondary-foreground: 0 0% 15%;
  --secondary-hover: 210 20% 92%;

  --accent: 210 40% 96%;
  --accent-foreground: 0 0% 15%;
  --accent-hover: 210 40% 92%;

  /* Muted colors with better contrast */
  --muted: 210 20% 97%;
  --muted-foreground: 0 0% 35%;
  --muted-hover: 210 20% 94%;

  /* Semantic colors */
  --success: 142 76% 36%;
  --success-foreground: 0 0% 100%;
  --warning: 38 92% 50%;
  --warning-foreground: 0 0% 7%;
  --error: 0 84% 60%;
  --error-foreground: 0 0% 100%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 0 0% 100%;

  /* Border and input */
  --border: 214 13% 91%;
  --input: 214 13% 91%;
  --input-focus: 142 76% 36%;
  --ring: 142 76% 36%;

  /* Sidebar and editor specific */
  --sidebar-background: 0 0% 98%;
  --sidebar-border: 214 13% 91%;
  --editor-background: 0 0% 100%;
  --editor-gutter: 210 20% 96%;
  --console-background: 0 0% 5%;
  --console-foreground: 142 76% 70%;

  /* Status indicators */
  --status-saved: 142 76% 36%;
  --status-modified: 38 92% 50%;
  --status-error: 0 84% 60%;

  --radius: 0.375rem; /* Slightly smaller radius for modern look */
}

  .dark {
    /* Enhanced VS Code-inspired dark theme */
    --background: 220 13% 9%;
    --foreground: 220 14% 93%;
    --card: 220 13% 9%;
    --card-foreground: 220 14% 93%;
    --popover: 220 13% 9%;
    --popover-foreground: 220 14% 93%;

    /* Primary colors - bright green for better visibility in dark mode */
    --primary: 142 76% 46%;
    --primary-foreground: 0 0% 7%;
    --primary-hover: 142 76% 42%;

    /* Secondary and accent colors */
    --secondary: 217 10% 15%;
    --secondary-foreground: 220 14% 93%;
    --secondary-hover: 217 10% 18%;

    --accent: 217 10% 15%;
    --accent-foreground: 220 14% 93%;
    --accent-hover: 217 10% 18%;

    /* Muted colors with better contrast */
    --muted: 217 10% 13%;
    --muted-foreground: 220 9% 55%;
    --muted-hover: 217 10% 16%;

    /* Semantic colors */
    --success: 142 76% 46%;
    --success-foreground: 0 0% 7%;
    --warning: 45 93% 58%;
    --warning-foreground: 0 0% 7%;
    --error: 0 84% 60%;
    --error-foreground: 0 0% 100%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    /* Border and input */
    --border: 217 10% 18%;
    --input: 217 10% 18%;
    --input-focus: 142 76% 46%;
    --ring: 142 76% 46%;

    /* Sidebar and editor specific */
    --sidebar-background: 220 13% 7%;
    --sidebar-border: 217 10% 15%;
    --editor-background: 220 13% 9%;
    --editor-gutter: 217 10% 13%;
    --console-background: 0 0% 3%;
    --console-foreground: 142 76% 65%;

    /* Status indicators */
    --status-saved: 142 76% 46%;
    --status-modified: 45 93% 58%;
    --status-error: 0 84% 60%;
  }

  @layer base {
    * {
      @apply border-border;
    }

    body {
      @apply bg-background text-foreground;
      font-feature-settings: "rlig" 1, "calt" 1;
    }

    html,
    body,
    #__next {
      height: 100%;
    }

    /* Typography improvements */
    h1, h2, h3, h4, h5, h6 {
      font-weight: 600;
      line-height: 1.3;
      letter-spacing: -0.025em;
    }

    code {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 0.9em;
      line-height: 1.6;
    }

    /* Standardized spacing system (8px grid) */
    .space-y-2 > * + * { margin-top: 0.5rem; } /* 8px */
    .space-y-3 > * + * { margin-top: 0.75rem; } /* 12px */
    .space-y-4 > * + * { margin-top: 1rem; } /* 16px */
    .space-y-6 > * + * { margin-top: 1.5rem; } /* 24px */
    .space-y-8 > * + * { margin-top: 2rem; } /* 32px */

    /* Enhanced button hover states */
    .button-hover-lift {
      transition: transform 0.15s ease, box-shadow 0.15s ease;
    }
    .button-hover-lift:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px hsl(var(--primary) / 0.25);
    }
  }

  /* Monaco Editor specific fixes */
  .monaco-editor {
    min-height: 200px;
  }

  /* Tabs content height fix */
  [data-state="active"][data-orientation="horizontal"] {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;
  }

  /* ScrollArea fixes for proper scrolling */
  [data-radix-scroll-area-viewport] {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--border)) transparent;
  }

  [data-radix-scroll-area-viewport]::-webkit-scrollbar {
    width: 8px;
  }

  [data-radix-scroll-area-viewport]::-webkit-scrollbar-track {
    background: transparent;
  }

  [data-radix-scroll-area-viewport]::-webkit-scrollbar-thumb {
    background-color: hsl(var(--border));
    border-radius: 4px;
  }

  [data-radix-scroll-area-viewport]::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--muted-foreground));
  }

  /* Ensure pre elements don't break scrolling */
  pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }

  /* Custom scrollbar for output areas */
  .output-scroll {
    scrollbar-width: auto;
    scrollbar-color: hsl(var(--border)) transparent;
    overflow-y: auto !important;
}

.output-scroll::-webkit-scrollbar {
  width: 14px;
  height: 14px;
}

.output-scroll::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 6px;
}
.output-scroll::-webkit-scrollbar-thumb {
  background-color: hsl(var(--border));
  border-radius: 6px;
  border: 2px solid hsl(var(--muted));
  min-height: 40px;
}

.output-scroll::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--muted-foreground));
}

/* Enhanced console styling with responsive adjustments */
.output-scroll pre {
  max-width: 100%;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  line-height: 1.5;
  padding: 1rem;
  margin: 0;
  background: hsl(var(--console-background));
  color: hsl(var(--console-foreground));
  border-radius: 0.375rem;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .output-scroll pre {
    padding: 0.75rem;
    font-size: 0.875rem;
    line-height: 1.4;
  }

  /* Adjust button sizes for touch */
  .run-button {
    min-height: 44px;
    padding: 0.75rem 1rem;
  }

  /* File tabs mobile optimization */
  .file-tab {
    min-height: 44px;
    padding: 0.5rem 0.75rem;
  }

  /* Sidebar items touch-friendly */
  .sidebar-item {
    min-height: 44px;
    padding: 0.75rem;
  }
}
/* Status indicators styling */
.status-indicator {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.125rem 0.5rem;
  border-radius: 0.25rem;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  transition: all 0.15s ease;
}

.status-saved {
  background: hsl(var(--success) / 0.1);
  color: hsl(var(--success));
  border: 1px solid hsl(var(--success) / 0.2);
}

.status-modified {
  background: hsl(var(--warning) / 0.1);
  color: hsl(var(--warning));
  border: 1px solid hsl(var(--warning) / 0.2);
}

.status-error {
  background: hsl(var(--error) / 0.1);
  color: hsl(var(--error));
  border: 1px solid hsl(var(--error) / 0.2);
}

/* Enhanced run button styling */
.run-button {
  background: hsl(var(--success));
  color: hsl(var(--success-foreground));
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.15s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.run-button:hover {
  background: hsl(var(--success) / 0.9);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px hsl(var(--success) / 0.25);
}

.run-button:active {
  transform: translateY(0);
}

/* File tab enhancements */
.file-tab {
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem 0.375rem 0 0;
  font-size: 0.875rem;
  transition: all 0.15s ease;
  border-bottom: 2px solid transparent;
}

.file-tab:hover {
  background: hsl(var(--accent-hover));
}

.file-tab[data-state="active"] {
  background: hsl(var(--editor-background));
  border-bottom-color: hsl(var(--primary));
}

/* Sidebar styling improvements */
.sidebar {
  background: hsl(var(--sidebar-background));
  border-right: 1px solid hsl(var(--sidebar-border));
}

.sidebar-item {
  padding: 0.375rem 0.75rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  transition: all 0.15s ease;
  cursor: pointer;
}

.sidebar-item:hover {
  background: hsl(var(--accent-hover));
}

.sidebar-item[data-active="true"] {
  background: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

/* Enhanced micro-interactions */
@layer utilities {

  /* Smooth scrolling for the entire page */
  html {
    scroll-behavior: smooth;
  }

  /* Loading shimmer effect */
  .loading-shimmer {
    background: linear-gradient(90deg,
        hsl(var(--muted)) 0%,
        hsl(var(--muted-hover)) 50%,
        hsl(var(--muted)) 100%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }

    100% {
      background-position: 200% 0;
    }
  }

  /* Pulse animation for status indicators */
  .status-pulse {
    animation: statusPulse 2s infinite;
  }

  @keyframes statusPulse {

    0%,
    100% {
      opacity: 1;
    }

    50% {
      opacity: 0.7;
    }
  }

  /* Smooth hover lift effect */
  .hover-lift {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px hsl(var(--primary) / 0.15);
  }

  /* Ripple effect for buttons */
  .ripple-effect {
    position: relative;
    overflow: hidden;
  }

  .ripple-effect::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: currentColor;
    opacity: 0.1;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
  }

  .ripple-effect:active::before {
    width: 300px;
    height: 300px;
  }

  /* Stagger animations */
  .stagger-children>* {
    animation: slideInUp 0.4s ease forwards;
    opacity: 0;
    transform: translateY(20px);
  }

  .stagger-children>*:nth-child(1) {
    animation-delay: 0.1s;
  }

  .stagger-children>*:nth-child(2) {
    animation-delay: 0.2s;
  }

  .stagger-children>*:nth-child(3) {
    animation-delay: 0.3s;
  }

  .stagger-children>*:nth-child(4) {
    animation-delay: 0.4s;
  }

  .stagger-children>*:nth-child(5) {
    animation-delay: 0.5s;
  }

  @keyframes slideInUp {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Focus glow effect */
  .focus-glow:focus-visible {
    box-shadow: 0 0 0 2px hsl(var(--background)),
      0 0 0 4px hsl(var(--primary) / 0.3),
      0 0 20px hsl(var(--primary) / 0.2);
  }

  /* Success animation */
  .success-bounce {
    animation: successBounce 0.6s ease;
  }

  @keyframes successBounce {

    0%,
    20%,
    53%,
    80%,
    100% {
      animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
      transform: translate3d(0, 0, 0);
    }

    40%,
    43% {
      animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
      transform: translate3d(0, -10px, 0);
    }

    70% {
      animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
      transform: translate3d(0, -5px, 0);
    }

    90% {
      transform: translate3d(0, -2px, 0);
    }
  }

  /* Typing indicator */
  .typing-dots::after {
    content: '';
    display: inline-block;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: currentColor;
    animation: typingDots 1.5s infinite;
    margin-left: 2px;
  }

  @keyframes typingDots {

    0%,
    20% {
      color: transparent;
      box-shadow:
        6px 0 0 transparent,
        12px 0 0 transparent;
    }

    40% {
      color: currentColor;
      box-shadow:
        6px 0 0 transparent,
        12px 0 0 transparent;
    }

    60% {
      box-shadow:
        6px 0 0 currentColor,
        12px 0 0 transparent;
    }

    80%,
    100% {
      box-shadow:
        6px 0 0 currentColor,
        12px 0 0 currentColor;
    }
  }
}

/* Complexity Analysis Modal - Zoom Level Compatible */
.modal-overlay {
  /* Use viewport units for zoom compatibility */
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 50;

  /* Background */
  background-color: rgba(0, 0, 0, 0.5);

  /* Flexbox centering - works at all zoom levels */
  display: flex;
  align-items: center;
  justify-content: center;

  /* Padding using viewport units */
  padding: 2vw;

  /* Prevent scrolling */
  overflow: hidden;
}

.modal-content {
  /* Use viewport-relative sizing */
  width: auto;
  height: auto;
  max-width: 88vw;
  /* Slightly less than 90vw for safety margin */
  max-height: 88vh;
  /* Slightly less than 90vh for safety margin */

  /* Minimum size constraints */
  min-width: min(320px, 80vw);
  min-height: min(200px, 40vh);

  /* Styling */
  background: hsl(var(--background));
  border: 1px solid hsl(var(--border));
  border-radius: 0.5rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Layout */
  display: flex;
  flex-direction: column;
  overflow: hidden;

  /* Smooth transitions */
  transition: all 0.3s ease-out;
}

/* Responsive breakpoints using viewport units */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 1vw;
  }

  .modal-content {
    max-width: 95vw;
    max-height: 95vh;
    border-radius: 0.375rem;
  }
}

@media (max-width: 480px) {
  .modal-overlay {
    padding: 0;
  }

  .modal-content {
    max-width: 100vw;
    max-height: 100vh;
    border-radius: 0;
    min-width: 100vw;
  }
}

/* High DPI / Zoom level adjustments */
@media (-webkit-min-device-pixel-ratio: 1.5),
(min-resolution: 144dpi) {
  .modal-content {
    /* Slightly reduce max size on high DPI displays */
    max-width: 85vw;
    max-height: 85vh;
  }
}

/* Ensure modal works with browser zoom */
@supports (zoom: 1) {
  .modal-overlay {
    /* Force recalculation of viewport units */
    width: calc(100vw);
    height: calc(100vh);
  }

  .modal-content {
    max-width: calc(88vw);
    max-height: calc(88vh);
  }
}
