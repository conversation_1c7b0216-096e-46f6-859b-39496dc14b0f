import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>eist, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";
import { AuthProvider } from '@/contexts/auth-context';
import { ThemeProvider } from '@/contexts/theme-context';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'Codeable - AI-Powered Coding Practice Platform',
  description:
    'Practice coding problems and prepare for interviews with our professional coding environment.',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="h-full">
      <head>
        {/* Theme initialization to prevent flash */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                function applyTheme(theme) {
                  const root = document.documentElement;
                  if (theme === 'dark') {
                    root.classList.add('dark');
                    root.style.colorScheme = 'dark';
                  } else if (theme === 'light') {
                    root.classList.remove('dark');
                    root.style.colorScheme = 'light';
                  } else {
                    // System theme
                    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
                    if (prefersDark) {
                      root.classList.add('dark');
                      root.style.colorScheme = 'dark';
                    } else {
                      root.classList.remove('dark');
                      root.style.colorScheme = 'light';
                    }
                  }
                }

                // Apply saved theme immediately
                const savedTheme = localStorage.getItem('theme') || 'system';
                applyTheme(savedTheme);
              })();
            `,
          }}
        />

        {/* Smart Pyodide Loading - Only load when needed */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.pyodideLoading = false;
              window.pyodideLoadRequested = false;

              // Smart loader - only loads when Python is needed
              window.loadPyodideOnDemand = async function() {
                if (window.pyodide) return window.pyodide;
                if (window.pyodideLoading) return null;
                if (window.pyodideLoadRequested) return null;

                window.pyodideLoadRequested = true;
                window.pyodideLoading = true;

                try {
                  console.log('🐍 Loading Pyodide on demand...');

                  // Load Pyodide script if not already loaded
                  if (typeof loadPyodide === 'undefined') {
                    const script = document.createElement('script');
                    script.src = 'https://cdn.jsdelivr.net/pyodide/v0.24.1/full/pyodide.js';
                    document.head.appendChild(script);

                    await new Promise((resolve, reject) => {
                      script.onload = resolve;
                      script.onerror = reject;
                    });
                  }

                  // Initialize Pyodide
                  window.pyodide = await loadPyodide({
                    indexURL: "https://cdn.jsdelivr.net/pyodide/v0.24.1/full/"
                  });

                  console.log('✅ Pyodide loaded successfully!');
                  window.pyodideLoading = false;
                  return window.pyodide;
                } catch (error) {
                  console.error('❌ Failed to load Pyodide:', error);
                  window.pyodideLoading = false;
                  window.pyodideLoadRequested = false;
                  throw error;
                }
              };
            `,
          }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased h-full`}
      >
        <AuthProvider>
          <ThemeProvider>{children}</ThemeProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
