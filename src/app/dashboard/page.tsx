'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { WorkspaceService } from '@/lib/services/workspace';
import { CreateWorkspaceModal } from '@/components/workspace/create-workspace-modal';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { FolderPlus, Search, Calendar, Code } from 'lucide-react';
import type { Workspace } from '@/types';

export default function DashboardPage() {
  const { user } = useAuth();
  const [workspaces, setWorkspaces] = useState<Workspace[]>([]);
  const [filteredWorkspaces, setFilteredWorkspaces] = useState<Workspace[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);

  useEffect(() => {
    if (user) {
      loadWorkspaces();
    }
  }, [user]);

  useEffect(() => {
    if (searchQuery) {
      const filtered = workspaces.filter(
        (workspace) =>
          workspace.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          workspace.description
            ?.toLowerCase()
            .includes(searchQuery.toLowerCase())
      );
      setFilteredWorkspaces(filtered);
    } else {
      setFilteredWorkspaces(workspaces);
    }
  }, [workspaces, searchQuery]);

  const loadWorkspaces = async () => {
    try {
      if (!user) return;
      const userWorkspaces = await WorkspaceService.getWorkspaces(user.$id);
      setWorkspaces(userWorkspaces);
    } catch (error) {
      console.error('Failed to load workspaces:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleWorkspaceCreated = () => {
    loadWorkspaces();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-muted-foreground">Loading your workspaces...</div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-foreground mb-2">
          Welcome back, {user?.name}!
        </h1>
        <p className="text-muted-foreground">
          Continue practicing or create a new workspace to get started.
        </p>
      </div>

      {/* Search and Create */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search workspaces..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>
        <Button onClick={() => setShowCreateModal(true)} className="shrink-0">
          <FolderPlus className="h-4 w-4 mr-2" />
          New Workspace
        </Button>
      </div>

      {/* Workspaces Grid */}
      {filteredWorkspaces.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredWorkspaces.map((workspace) => (
            <div
              key={workspace.$id}
              className="p-6 bg-card border border-border rounded-lg hover:shadow-md transition-shadow cursor-pointer"
              onClick={() =>
                (window.location.href = `/dashboard/workspace/${workspace.$id}`)
              }
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-primary rounded-full" />
                  <h3 className="font-semibold text-foreground">
                    {workspace.name}
                  </h3>
                </div>
                <span className="text-xs bg-secondary px-2 py-1 rounded text-secondary-foreground">
                  {workspace.category}
                </span>
              </div>

              {workspace.description && (
                <p className="text-sm text-muted-foreground mb-4 line-clamp-2">
                  {workspace.description}
                </p>
              )}

              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <div className="flex items-center space-x-1">
                  <Code className="h-3 w-3" />
                  <span>{workspace.fileCount || 0} files</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Calendar className="h-3 w-3" />
                  <span>
                    {new Date(workspace.$updatedAt).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
            <FolderPlus className="h-8 w-8 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-medium text-foreground mb-2">
            {searchQuery ? 'No workspaces found' : 'No workspaces yet'}
          </h3>
          <p className="text-muted-foreground mb-6">
            {searchQuery
              ? 'Try adjusting your search query'
              : 'Create your first workspace to start practicing coding problems'}
          </p>
          {!searchQuery && (
            <Button onClick={() => setShowCreateModal(true)}>
              <FolderPlus className="h-4 w-4 mr-2" />
              Create Workspace
            </Button>
          )}
        </div>
      )}

      {/* Create Workspace Modal */}
      <CreateWorkspaceModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onWorkspaceCreated={handleWorkspaceCreated}
      />
    </div>
  );
}
