'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { useAuth } from '@/contexts/auth-context';
import { useWorkspace } from '@/contexts/workspace-context';
import { FileService } from '@/lib/services/file';
import { EnhancedCodeEditor } from '@/components/editor/enhanced-code-editor';
import { SaveIndicator } from '@/components/editor/save-indicator';
import { CreateFileModal } from '@/components/workspace/create-file-modal';
import { FileTabs } from '@/components/editor/file-tabs';
import { QuickFileSwitcher } from '@/components/editor/quick-file-switcher';
import { ThemeSelector } from '@/components/editor/theme-selector';
import { Button } from '@/components/ui/button';
import { useAutoSave } from '@/hooks/use-auto-save';
import { Plus, FolderOpen, Save, Code, FileText, Search } from 'lucide-react';
import { cn } from '@/lib/utils';
import { getTemplate, getDefaultTemplate } from '@/lib/code-templates';
import { CodingChallenges } from '@/components/workspace/coding-challenges';
import type { File, CreateFileData } from '@/types';

export default function WorkspacePage() {
  const { workspaceId } = useParams();
  const { user } = useAuth();
  const {
    currentWorkspace,
    currentFiles,
    activeFile,
    setActiveFile,
    refreshFiles,
    setOnCreateFileRequest,
  } = useWorkspace();
  const [fileContent, setFileContent] = useState('');
  const [error, setError] = useState('');
  const [showCreateFileModal, setShowCreateFileModal] = useState(false);
  const [showChallenges, setShowChallenges] = useState(false);
  const [showQuickSwitcher, setShowQuickSwitcher] = useState(false);

  const { saveStatus, forceSave, hasUnsavedChanges } = useAutoSave(
    activeFile?.$id || null,
    fileContent,
    { enabled: !!activeFile }
  );

  // Update file content when active file changes
  useEffect(() => {
    if (activeFile) {
      setFileContent(activeFile.content);
    } else {
      setFileContent('');
    }
  }, [activeFile]);

  // Register file creation callback
  useEffect(() => {
    setOnCreateFileRequest(() => () => setShowCreateFileModal(true));
    return () => setOnCreateFileRequest(null);
  }, [setOnCreateFileRequest]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl/Cmd + P for quick file switcher
      if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
        e.preventDefault();
        setShowQuickSwitcher(true);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Handle navigation away - ensure auto-save completes
  useEffect(() => {
    const handleBeforeUnload = async (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        // Force save before navigation
        await forceSave();
        e.preventDefault();
        e.returnValue = '';
      }
    };

    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden' && hasUnsavedChanges) {
        // Force save when tab becomes hidden
        forceSave();
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [hasUnsavedChanges, forceSave]);

  const handleFileCreate = async (data: {
    name: string;
    language: string;
    template?: string;
  }) => {
    try {
      if (!user || typeof workspaceId !== 'string') return;

      const createData: CreateFileData = {
        name: data.name,
        language: data.language,
        workspaceId: workspaceId,
        content: getFileContent(data.language, data.template),
      };

      const newFile = await FileService.createFile(createData, user.$id);
      await refreshFiles();
      setActiveFile(newFile);
      setFileContent(newFile.content);
    } catch (err: any) {
      console.error('Failed to create file:', err);
      setError('Failed to create file');
    }
  };

  const handleFileRename = async (file: File, newName: string) => {
    try {
      const updatedFile = await FileService.updateFileMetadata(file.$id, {
        name: newName,
      });
      await refreshFiles();
      if (activeFile?.$id === file.$id) {
        setActiveFile(updatedFile);
      }
    } catch (err: any) {
      console.error('Failed to rename file:', err);
      setError('Failed to rename file');
    }
  };

  const handleFileDelete = async (file: File) => {
    try {
      await FileService.deleteFile(file.$id);
      await refreshFiles();
      if (activeFile?.$id === file.$id) {
        setActiveFile(null);
        setFileContent('');
      }
    } catch (err: any) {
      console.error('Failed to delete file:', err);
      setError('Failed to delete file');
    }
  };

  const handleFileDuplicate = async (file: File) => {
    try {
      if (!user || typeof workspaceId !== 'string') return;

      const duplicateName = `${file.name.replace(/\.[^/.]+$/, '')}_copy${
        file.name.match(/\.[^/.]+$/)?.[0] || ''
      }`;

      const createData: CreateFileData = {
        name: duplicateName,
        language: file.language,
        workspaceId: workspaceId,
        content: file.content,
      };

      const newFile = await FileService.createFile(createData, user.$id);
      await refreshFiles();
    } catch (err: any) {
      console.error('Failed to duplicate file:', err);
      setError('Failed to duplicate file');
    }
  };

  const getFileContent = (language: string, template?: string): string => {
    if (template) {
      return getTemplate(language, template);
    }
    return getDefaultTemplate(language);
  };

  const handleChallengeSelect = async (code: string, title: string) => {
    try {
      if (!user || typeof workspaceId !== 'string') return;

      // Create a new file with the challenge code
      const fileName = title.toLowerCase().replace(/\s+/g, '-') + '.js';
      const createData: CreateFileData = {
        name: fileName,
        language: 'javascript',
        workspaceId: workspaceId,
        content: code,
      };

      const newFile = await FileService.createFile(createData, user.$id);
      await refreshFiles();
      setActiveFile(newFile);
      setFileContent(newFile.content);
      setShowChallenges(false); // Hide challenges panel
    } catch (err: any) {
      console.error('Failed to create challenge file:', err);
      setError('Failed to create challenge file');
    }
  };

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-destructive">{error}</div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full min-h-0">
      {/* Workspace Header - Enhanced */}
      <div className="p-4 border-b border-border flex-shrink-0 bg-card shadow-sm">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 min-w-0 flex-1">
            <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
              <FolderOpen className="h-5 w-5 text-primary" />
            </div>
            <div className="min-w-0 flex-1">
              <h2 className="font-semibold text-lg truncate">
                {currentWorkspace?.name}
              </h2>
              {currentWorkspace?.description && (
                <p className="text-sm text-muted-foreground truncate">
                  {currentWorkspace.description}
                </p>
              )}
            </div>
          </div>
          <div className="flex items-center gap-2 flex-shrink-0">
            <Button
              onClick={() => setShowQuickSwitcher(true)}
              size="sm"
              variant="ghost"
              className="text-muted-foreground hidden sm:flex"
            >
              <Search className="h-4 w-4 mr-2" />
              <span className="hidden md:inline">Quick Open</span>
              <kbd className="ml-2 px-1.5 py-0.5 text-xs bg-muted rounded hidden lg:inline">
                ⌘P
              </kbd>
            </Button>
            <Button
              onClick={() => setShowCreateFileModal(true)}
              size="sm"
              variant="outline"
            >
              <Plus className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">New File</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Editor Area */}
      <div className="flex-1 flex flex-col min-h-0">
        {/* File Tabs - Always show if there are files */}
        {currentFiles.length > 0 && (
          <FileTabs
            files={currentFiles}
            activeFile={activeFile}
            onFileSelect={setActiveFile}
            onFileClose={handleFileDelete}
          />
        )}

        {activeFile ? (
          <>
            {/* Editor Header - Simplified since tabs show file info */}
            <div className="h-10 border-b border-border bg-muted/20 flex items-center justify-between px-4 flex-shrink-0">
              <div className="flex items-center space-x-3">
                <span className="text-xs bg-secondary px-2 py-1 rounded text-secondary-foreground">
                  {activeFile.language}
                </span>
                {(activeFile.language === 'javascript' ||
                  activeFile.language === 'typescript' ||
                  activeFile.language === 'python') && (
                  <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                    ▶ Executable
                  </span>
                )}
              </div>

              <div className="flex items-center space-x-3">
                <SaveIndicator status={saveStatus} />
                <ThemeSelector />
                <Button
                  size="sm"
                  variant="outline"
                  onClick={forceSave}
                  disabled={saveStatus === 'saving' || !hasUnsavedChanges}
                  className="h-7"
                >
                  <Save className="h-3 w-3 mr-1" />
                  Save
                </Button>
              </div>
            </div>

            {/* Enhanced Code Editor with Split Layout */}
            <div className="flex-1 min-h-0 flex flex-col">
              <EnhancedCodeEditor
                value={fileContent}
                language={activeFile.language}
                onChange={setFileContent}
                onSave={forceSave}
              />
            </div>
          </>
        ) : (
          <div className="flex-1 flex min-h-0">
            {/* Welcome/Challenges Panel */}
            <div className="flex-1 flex flex-col min-h-0">
              {currentFiles.length === 0 ? (
                /* No files - show welcome */
                <div className="flex-1 flex items-center justify-center">
                  <div className="text-center max-w-md">
                    <FolderOpen className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                    <h3 className="text-xl font-bold mb-2">
                      Welcome to {currentWorkspace?.name}
                    </h3>
                    <p className="text-muted-foreground mb-6">
                      Start by creating your first file or try one of our coding
                      challenges
                    </p>
                    <div className="flex flex-col sm:flex-row gap-3 justify-center">
                      <Button onClick={() => setShowCreateFileModal(true)}>
                        <Plus className="h-4 w-4 mr-2" />
                        Create File
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => setShowChallenges(!showChallenges)}
                      >
                        <Code className="h-4 w-4 mr-2" />
                        Browse Challenges
                      </Button>
                    </div>
                  </div>
                </div>
              ) : (
                /* Has files but none selected - show file list or challenges */
                <div className="flex-1 flex items-center justify-center">
                  <div className="text-center max-w-md">
                    <FileText className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                    <h3 className="text-lg font-medium mb-2">
                      Select a file to start coding
                    </h3>
                    <p className="text-muted-foreground mb-6">
                      Choose a file from the sidebar or create a new one
                    </p>
                    <div className="flex flex-col sm:flex-row gap-3 justify-center">
                      <Button onClick={() => setShowCreateFileModal(true)}>
                        <Plus className="h-4 w-4 mr-2" />
                        New File
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => setShowChallenges(!showChallenges)}
                      >
                        <Code className="h-4 w-4 mr-2" />
                        Practice Challenges
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Challenges Panel */}
            {showChallenges && (
              <div className="w-2/5 min-w-[400px] border-l border-border">
                <CodingChallenges onSelectChallenge={handleChallengeSelect} />
              </div>
            )}
          </div>
        )}
      </div>

      {/* Create File Modal */}
      <CreateFileModal
        isOpen={showCreateFileModal}
        onClose={() => setShowCreateFileModal(false)}
        onFileCreate={handleFileCreate}
      />

      {/* Quick File Switcher */}
      <QuickFileSwitcher
        files={currentFiles}
        activeFile={activeFile}
        onFileSelect={setActiveFile}
        onClose={() => setShowQuickSwitcher(false)}
        isOpen={showQuickSwitcher}
      />
    </div>
  );
}
