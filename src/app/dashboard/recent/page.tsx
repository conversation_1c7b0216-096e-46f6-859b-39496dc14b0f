'use client';

import { useState, useEffect } from 'react';
import { Clock, FileText, Search, FileCode, FileType, Code } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { formatRelativeTime, formatFileSize, cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';

const getFileIcon = (language: string) => {
  switch (language.toLowerCase()) {
    case 'javascript':
      return FileCode;
    case 'typescript':
      return FileType;
    case 'python':
      return Code;
    case 'java':
      return FileCode;
    default:
      return FileText;
  }
};

const getLanguageColor = (lang: string) => {
  switch (lang.toLowerCase()) {
    case 'javascript': return 'text-yellow-600 dark:text-yellow-400';
    case 'typescript': return 'text-blue-600 dark:text-blue-400';
    case 'python': return 'text-green-600 dark:text-green-400';
    case 'java': return 'text-orange-600 dark:text-orange-400';
    default: return 'text-muted-foreground';
  }
};

interface RecentFile {
  $id: string;
  name: string;
  language: string;
  $updatedAt: string;
  workspaceName: string;
  workspaceId: string;
  size?: number;
  lastExecuted?: string;
}

export default function RecentFilesPage() {
  const [recentFiles, setRecentFiles] = useState<RecentFile[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    // TODO: Replace with actual API call to get recent files
    const mockRecentFiles: RecentFile[] = [
      {
        $id: '1',
        name: 'algorithm.py',
        language: 'python',
        $updatedAt: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
        workspaceName: 'Interview Prep',
        workspaceId: 'ws1',
        size: 2048,
        lastExecuted: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
      },
      {
        $id: '2',
        name: 'component.tsx',
        language: 'typescript',
        $updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
        workspaceName: 'React Practice',
        workspaceId: 'ws2',
        size: 4096,
      },
      {
        $id: '3',
        name: 'solution.js',
        language: 'javascript',
        $updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),
        workspaceName: 'LeetCode',
        workspaceId: 'ws3',
        size: 1024,
        lastExecuted: new Date(Date.now() - 1000 * 60 * 60 * 20).toISOString(),
      },
      {
        $id: '4',
        name: 'sorting.java',
        language: 'java',
        $updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 48).toISOString(),
        workspaceName: 'Algorithms',
        workspaceId: 'ws4',
        size: 3072,
        lastExecuted: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),
      },
    ];

    setTimeout(() => {
      setRecentFiles(mockRecentFiles);
      setLoading(false);
    }, 500);
  }, []);

  const filteredFiles = recentFiles.filter(file =>
    file.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    file.workspaceName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    file.language.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleOpenFile = (file: RecentFile) => {
    router.push(`/dashboard/workspace/${file.workspaceId}?file=${file.$id}`);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-background">
      <div className="flex-1 overflow-y-auto p-6">
        <div className="max-w-4xl">
          <div className="mb-6">
            <div className="flex items-center gap-3 mb-2">
              <Clock className="h-6 w-6 text-primary" />
              <h1 className="text-2xl font-bold text-foreground">Recent Files</h1>
            </div>
            <p className="text-muted-foreground">
              Your recently accessed and modified files across all workspaces
            </p>
          </div>

          {/* Search */}
          <div className="mb-6">
            <div className="relative max-w-md">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search recent files..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9 bg-background/50 border-input focus:border-input-focus"
              />
            </div>
          </div>

          {/* Recent Files List */}
          <div className="space-y-2">
            {filteredFiles.length === 0 ? (
              <div className="text-center py-12">
                <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground/50" />
                <h3 className="text-lg font-medium text-foreground mb-2">
                  {searchQuery ? 'No files found' : 'No recent files'}
                </h3>
                <p className="text-muted-foreground">
                  {searchQuery 
                    ? 'Try adjusting your search terms'
                    : 'Start working on files to see them here'
                  }
                </p>
              </div>
            ) : (
              filteredFiles.map((file) => {
                const FileIcon = getFileIcon(file.language);
                return (
                  <div
                    key={file.$id}
                    className="flex items-center justify-between p-4 bg-card border border-border rounded-lg hover:bg-accent/50 transition-colors cursor-pointer"
                    onClick={() => handleOpenFile(file)}
                  >
                    <div className="flex items-center gap-4 min-w-0 flex-1">
                      <div className="flex-shrink-0">
                        <FileIcon 
                          className={cn(
                            'h-5 w-5',
                            getLanguageColor(file.language)
                          )}
                        />
                      </div>
                      
                      <div className="min-w-0 flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-medium text-foreground truncate">{file.name}</h3>
                          <span className={cn(
                            "text-xs px-2 py-1 rounded-full capitalize font-medium",
                            "bg-secondary/50 border border-secondary",
                            getLanguageColor(file.language)
                          )}>
                            {file.language}
                          </span>
                        </div>
                        
                        <div className="flex items-center gap-3 text-xs text-muted-foreground">
                          <span className="font-medium">{file.workspaceName}</span>
                          <span>•</span>
                          <span>Updated {formatRelativeTime(file.$updatedAt)}</span>
                          {file.size && (
                            <>
                              <span>•</span>
                              <span>{formatFileSize(file.size)}</span>
                            </>
                          )}
                          {file.lastExecuted && (
                            <>
                              <span>•</span>
                              <span className="text-success">
                                Executed {formatRelativeTime(file.lastExecuted)}
                              </span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>

                    <Button 
                      variant="ghost" 
                      size="sm"
                      className="hover:bg-primary/10 hover:text-primary"
                    >
                      Open
                    </Button>
                  </div>
                );
              })
            )}
          </div>
        </div>
      </div>
    </div>
  );
}