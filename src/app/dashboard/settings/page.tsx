'use client';

import { useState } from 'react';
import { Settings, User, Bell, Palette, Code, Shield, Download, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';

export default function SettingsPage() {
  const [settings, setSettings] = useState({
    profile: {
      name: '<PERSON>',
      email: '<EMAIL>',
    },
    editor: {
      theme: 'dark',
      fontSize: 14,
      wordWrap: true,
      autoSave: true,
      autoSaveDelay: 2000,
    },
    notifications: {
      emailNotifications: true,
      pushNotifications: false,
      soundEnabled: true,
    },
    privacy: {
      analyticsEnabled: true,
      shareUsageData: false,
    },
  });

  const handleInputChange = (section: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section as keyof typeof prev],
        [key]: value,
      },
    }));
  };

  const handleSaveSettings = () => {
    // TODO: Implement actual settings save
    console.log('Saving settings:', settings);
    alert('Settings saved successfully!');
  };

  const handleExportData = () => {
    // TODO: Implement data export
    alert('Data export feature coming soon!');
  };

  const handleDeleteAccount = () => {
    if (confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
      // TODO: Implement account deletion
      alert('Account deletion feature coming soon!');
    }
  };

  return (
    <div className="h-full flex flex-col bg-background">
      <div className="flex-1 overflow-y-auto p-6">
        <div className="max-w-4xl">
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-2">
            <Settings className="h-6 w-6 text-primary" />
            <h1 className="text-2xl font-bold text-foreground">Settings</h1>
          </div>
          <p className="text-muted-foreground">
            Customize your Codeable experience
          </p>
        </div>

        <div className="grid gap-8">
          {/* Profile Settings */}
          <div className="bg-card border border-border rounded-lg p-6">
            <div className="flex items-center gap-3 mb-4">
              <User className="h-5 w-5 text-primary" />
              <h2 className="text-lg font-semibold text-foreground">Profile</h2>
            </div>
            
            <div className="grid gap-4 max-w-md">
              <div>
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  value={settings.profile.name}
                  onChange={(e) => handleInputChange('profile', 'name', e.target.value)}
                />
              </div>
              
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={settings.profile.email}
                  onChange={(e) => handleInputChange('profile', 'email', e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Editor Settings */}
          <div className="bg-card border border-border rounded-lg p-6">
            <div className="flex items-center gap-3 mb-4">
              <Code className="h-5 w-5 text-primary" />
              <h2 className="text-lg font-semibold text-foreground">Editor</h2>
            </div>
            
            <div className="grid gap-4 max-w-md">
              <div>
                <Label htmlFor="theme">Theme</Label>
                <select
                  id="theme"
                  value={settings.editor.theme}
                  onChange={(e) => handleInputChange('editor', 'theme', e.target.value)}
                  className="w-full px-3 py-2 bg-background border border-input rounded-md"
                >
                  <option value="dark">Dark</option>
                  <option value="light">Light</option>
                  <option value="auto">Auto</option>
                </select>
              </div>
              
              <div>
                <Label htmlFor="fontSize">Font Size</Label>
                <Input
                  id="fontSize"
                  type="number"
                  min="10"
                  max="24"
                  value={settings.editor.fontSize}
                  onChange={(e) => handleInputChange('editor', 'fontSize', parseInt(e.target.value))}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="wordWrap">Word Wrap</Label>
                <Switch
                  id="wordWrap"
                  checked={settings.editor.wordWrap}
                  onCheckedChange={(checked) => handleInputChange('editor', 'wordWrap', checked)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="autoSave">Auto Save</Label>
                <Switch
                  id="autoSave"
                  checked={settings.editor.autoSave}
                  onCheckedChange={(checked) => handleInputChange('editor', 'autoSave', checked)}
                />
              </div>
              
              {settings.editor.autoSave && (
                <div>
                  <Label htmlFor="autoSaveDelay">Auto Save Delay (ms)</Label>
                  <Input
                    id="autoSaveDelay"
                    type="number"
                    min="500"
                    max="10000"
                    step="500"
                    value={settings.editor.autoSaveDelay}
                    onChange={(e) => handleInputChange('editor', 'autoSaveDelay', parseInt(e.target.value))}
                  />
                </div>
              )}
            </div>
          </div>

          {/* Notifications */}
          <div className="bg-card border border-border rounded-lg p-6">
            <div className="flex items-center gap-3 mb-4">
              <Bell className="h-5 w-5 text-primary" />
              <h2 className="text-lg font-semibold text-foreground">Notifications</h2>
            </div>
            
            <div className="grid gap-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="emailNotifications">Email Notifications</Label>
                  <p className="text-sm text-muted-foreground">Receive updates via email</p>
                </div>
                <Switch
                  id="emailNotifications"
                  checked={settings.notifications.emailNotifications}
                  onCheckedChange={(checked) => handleInputChange('notifications', 'emailNotifications', checked)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="pushNotifications">Push Notifications</Label>
                  <p className="text-sm text-muted-foreground">Browser notifications</p>
                </div>
                <Switch
                  id="pushNotifications"
                  checked={settings.notifications.pushNotifications}
                  onCheckedChange={(checked) => handleInputChange('notifications', 'pushNotifications', checked)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="soundEnabled">Sound Effects</Label>
                  <p className="text-sm text-muted-foreground">Play sounds for actions</p>
                </div>
                <Switch
                  id="soundEnabled"
                  checked={settings.notifications.soundEnabled}
                  onCheckedChange={(checked) => handleInputChange('notifications', 'soundEnabled', checked)}
                />
              </div>
            </div>
          </div>

          {/* Privacy & Data */}
          <div className="bg-card border border-border rounded-lg p-6">
            <div className="flex items-center gap-3 mb-4">
              <Shield className="h-5 w-5 text-primary" />
              <h2 className="text-lg font-semibold text-foreground">Privacy & Data</h2>
            </div>
            
            <div className="grid gap-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="analyticsEnabled">Analytics</Label>
                  <p className="text-sm text-muted-foreground">Help improve Codeable with usage analytics</p>
                </div>
                <Switch
                  id="analyticsEnabled"
                  checked={settings.privacy.analyticsEnabled}
                  onCheckedChange={(checked) => handleInputChange('privacy', 'analyticsEnabled', checked)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="shareUsageData">Share Usage Data</Label>
                  <p className="text-sm text-muted-foreground">Share anonymous usage data for research</p>
                </div>
                <Switch
                  id="shareUsageData"
                  checked={settings.privacy.shareUsageData}
                  onCheckedChange={(checked) => handleInputChange('privacy', 'shareUsageData', checked)}
                />
              </div>

              <div className="flex gap-3 mt-4">
                <Button
                  variant="outline"
                  onClick={handleExportData}
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  Export Data
                </Button>
                
                <Button
                  variant="destructive"
                  onClick={handleDeleteAccount}
                  className="flex items-center gap-2"
                >
                  <Trash2 className="h-4 w-4" />
                  Delete Account
                </Button>
              </div>
            </div>
          </div>

          {/* Save Settings */}
          <div className="flex justify-end">
            <Button onClick={handleSaveSettings} size="lg">
              Save Settings
            </Button>
          </div>
        </div>
        </div>
      </div>
    </div>
  );
}