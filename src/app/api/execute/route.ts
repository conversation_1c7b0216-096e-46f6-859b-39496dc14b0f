import { NextRequest, NextResponse } from 'next/server';

// Note: Code execution is disabled in production for security reasons
// This API is designed for development/demo purposes only

// Language configurations
const LANGUAGE_CONFIG = {
  javascript: {
    extension: 'js',
    command: 'node',
    dockerImage: 'node:18-alpine',
  },
  python: {
    extension: 'py',
    command: 'python3',
    dockerImage: 'python:3.11-alpine',
  },
  java: {
    extension: 'java',
    command: 'javac && java',
    dockerImage: 'openjdk:17-alpine',
  },
  cpp: {
    extension: 'cpp',
    command: 'g++ -o program && ./program',
    dockerImage: 'gcc:latest',
  },
};

export async function POST(request: NextRequest) {
  try {
    const { code, language, input, timeout = 10000 } = await request.json();

    if (!code || !language) {
      return NextResponse.json(
        { error: 'Code and language are required' },
        { status: 400 }
      );
    }

    const config = LANGUAGE_CONFIG[language as keyof typeof LANGUAGE_CONFIG];
    if (!config) {
      return NextResponse.json(
        { error: `Unsupported language: ${language}` },
        { status: 400 }
      );
    }

    const startTime = Date.now();

    // In production (Vercel), code execution is disabled for security
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json({
        output: '',
        error:
          'Code execution is disabled in production for security reasons. This feature is only available in development mode.',
        executionTime: 0,
      });
    }

    // For development only - simulate code execution
    const result = await simulateExecution(code, language, input);
    return NextResponse.json({
      ...result,
      executionTime: 100, // Simulated execution time
    });
  } catch (error: unknown) {
    return NextResponse.json(
      {
        output: '',
        error: `Server error: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        executionTime: 0,
      },
      { status: 500 }
    );
  }
}

async function simulateExecution(
  code: string,
  language: string,
  input?: string
) {
  // Simple simulation for development/demo purposes
  // In a real implementation, you would use a secure sandboxed environment

  try {
    // Simulate different responses based on language
    if (language === 'javascript') {
      if (code.includes('console.log')) {
        const match = code.match(/console\.log\(['"`](.+?)['"`]\)/);
        if (match) {
          return {
            output: match[1] + '\n',
            error: undefined,
          };
        }
      }
      return {
        output: '// JavaScript code executed successfully (simulated)\n',
        error: undefined,
      };
    }

    if (language === 'python') {
      if (code.includes('print')) {
        const match = code.match(/print\(['"`](.+?)['"`]\)/);
        if (match) {
          return {
            output: match[1] + '\n',
            error: undefined,
          };
        }
      }
      return {
        output: '# Python code executed successfully (simulated)\n',
        error: undefined,
      };
    }

    return {
      output: `// ${language} code executed successfully (simulated)\n`,
      error: undefined,
    };
  } catch (error: unknown) {
    return {
      output: '',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
