// Code templates for different languages
export const CODE_TEMPLATES = {
  javascript: {
    'Hello World': `// JavaScript Hello World
console.log('Hello, World!');
console.log('Welcome to Codeable!');

// Try some basic operations
const numbers = [1, 2, 3, 4, 5];
const doubled = numbers.map(n => n * 2);
console.log('Original:', numbers);
console.log('Doubled:', doubled);

// Function example
function fibonacci(n) {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibon<PERSON><PERSON>(n - 2);
}

console.log('<PERSON>bonacci sequence:');
for (let i = 0; i < 10; i++) {
  console.log(\`F(\${i}) = \${fibonacci(i)}\`);
}`,

    'Two Sum Problem': `// LeetCode #1: Two Sum
// Given an array of integers nums and an integer target,
// return indices of the two numbers such that they add up to target.

function twoSum(nums, target) {
  // Your solution here
  const map = new Map();

  for (let i = 0; i < nums.length; i++) {
    const complement = target - nums[i];

    if (map.has(complement)) {
      return [map.get(complement), i];
    }

    map.set(nums[i], i);
  }

  return [];
}

// Test cases
console.log('Test 1:', twoSum([2, 7, 11, 15], 9)); // Expected: [0, 1]
console.log('Test 2:', twoSum([3, 2, 4], 6)); // Expected: [1, 2]
console.log('Test 3:', twoSum([3, 3], 6)); // Expected: [0, 1]`,

    'Palindrome Check': `// Check if a string is a palindrome
// A palindrome reads the same forward and backward

function isPalindrome(s) {
  // Remove non-alphanumeric characters and convert to lowercase
  const cleaned = s.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();

  // Compare with its reverse
  return cleaned === cleaned.split('').reverse().join('');
}

// Alternative solution using two pointers
function isPalindromeOptimal(s) {
  const cleaned = s.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
  let left = 0;
  let right = cleaned.length - 1;

  while (left < right) {
    if (cleaned[left] !== cleaned[right]) {
      return false;
    }
    left++;
    right--;
  }

  return true;
}

// Test cases
console.log('Test 1:', isPalindrome("A man, a plan, a canal: Panama")); // true
console.log('Test 2:', isPalindrome("race a car")); // false
console.log('Test 3:', isPalindromeOptimal("Was it a car or a cat I saw?")); // true`,

    'FizzBuzz Challenge': `// FizzBuzz: Print numbers 1 to n, but:
// - Print "Fizz" for multiples of 3
// - Print "Buzz" for multiples of 5
// - Print "FizzBuzz" for multiples of both 3 and 5

function fizzBuzz(n) {
  const result = [];

  for (let i = 1; i <= n; i++) {
    if (i % 15 === 0) {
      result.push("FizzBuzz");
    } else if (i % 3 === 0) {
      result.push("Fizz");
    } else if (i % 5 === 0) {
      result.push("Buzz");
    } else {
      result.push(i.toString());
    }
  }

  return result;
}

// Test the function
console.log('FizzBuzz(15):');
const result = fizzBuzz(15);
result.forEach((item, index) => {
  console.log(\`\${index + 1}: \${item}\`);
});

// One-liner version
const fizzBuzzOneLiner = n =>
  Array.from({length: n}, (_, i) =>
    (++i % 15 === 0 && 'FizzBuzz') ||
    (i % 3 === 0 && 'Fizz') ||
    (i % 5 === 0 && 'Buzz') ||
    i
  );

console.log('\\nOne-liner result:', fizzBuzzOneLiner(15));`,

    'Interactive Calculator': `// Interactive Calculator with User Input
// This example shows how to use prompt() for user interaction

function calculator() {
  console.log('=== Interactive Calculator ===');

  // Get user input (in browser, this will show input prompts)
  const num1 = parseFloat(prompt('Enter first number:') || '0');
  const operator = prompt('Enter operator (+, -, *, /):') || '+';
  const num2 = parseFloat(prompt('Enter second number:') || '0');

  console.log(\`Calculating: \${num1} \${operator} \${num2}\`);

  let result;
  switch (operator) {
    case '+':
      result = num1 + num2;
      break;
    case '-':
      result = num1 - num2;
      break;
    case '*':
      result = num1 * num2;
      break;
    case '/':
      result = num2 !== 0 ? num1 / num2 : 'Error: Division by zero';
      break;
    default:
      result = 'Error: Invalid operator';
  }

  console.log(\`Result: \${result}\`);
  alert(\`The answer is: \${result}\`);
}

// Run the calculator
calculator();

// Alternative: Direct calculation example
console.log('\\n=== Direct Examples ===');
console.log('10 + 5 =', 10 + 5);
console.log('20 - 8 =', 20 - 8);
console.log('6 * 7 =', 6 * 7);
console.log('15 / 3 =', 15 / 3);`,

    'Number Guessing Game': `// Number Guessing Game
// Interactive game using prompt() and random numbers

function numberGuessingGame() {
  console.log('🎮 Welcome to the Number Guessing Game!');
  console.log('I\\'m thinking of a number between 1 and 100...');

  const secretNumber = Math.floor(Math.random() * 100) + 1;
  let attempts = 0;
  let maxAttempts = 7;
  let hasWon = false;

  console.log(\`You have \${maxAttempts} attempts to guess the number!\`);

  // Simulate multiple guesses for demo
  const demoGuesses = [50, 75, 62, 68, 65, 67]; // Example guesses

  for (let i = 0; i < Math.min(demoGuesses.length, maxAttempts); i++) {
    attempts++;
    const guess = demoGuesses[i];

    console.log(\`\\nAttempt \${attempts}: You guessed \${guess}\`);

    if (guess === secretNumber) {
      console.log(\`🎉 Congratulations! You guessed it in \${attempts} attempts!\`);
      hasWon = true;
      break;
    } else if (guess < secretNumber) {
      console.log('📈 Too low! Try a higher number.');
    } else {
      console.log('📉 Too high! Try a lower number.');
    }

    if (attempts === maxAttempts) {
      console.log(\`\\n💔 Game Over! The number was \${secretNumber}\`);
      break;
    }
  }

  if (!hasWon && attempts < maxAttempts) {
    console.log(\`\\n🤔 The secret number was \${secretNumber}\`);
    console.log('In a real game, you would continue guessing!');
  }
}

// Play the game
numberGuessingGame();

// Show random number generation examples
console.log('\\n=== Random Number Examples ===');
console.log('Random 1-10:', Math.floor(Math.random() * 10) + 1);
console.log('Random 1-100:', Math.floor(Math.random() * 100) + 1);
console.log('Random decimal 0-1:', Math.random().toFixed(3));`,

    'Binary Search': `// Binary Search Algorithm
// Find the index of target in a sorted array

function binarySearch(nums, target) {
  let left = 0;
  let right = nums.length - 1;

  while (left <= right) {
    const mid = Math.floor((left + right) / 2);

    if (nums[mid] === target) {
      return mid;
    } else if (nums[mid] < target) {
      left = mid + 1;
    } else {
      right = mid - 1;
    }
  }

  return -1; // Target not found
}

// Recursive version
function binarySearchRecursive(nums, target, left = 0, right = nums.length - 1) {
  if (left > right) return -1;

  const mid = Math.floor((left + right) / 2);

  if (nums[mid] === target) return mid;
  if (nums[mid] < target) return binarySearchRecursive(nums, target, mid + 1, right);
  return binarySearchRecursive(nums, target, left, mid - 1);
}

// Test cases
const sortedArray = [1, 3, 5, 7, 9, 11, 13, 15, 17, 19];

console.log('Array:', sortedArray);
console.log('Search for 7:', binarySearch(sortedArray, 7)); // Expected: 3
console.log('Search for 15:', binarySearch(sortedArray, 15)); // Expected: 7
console.log('Search for 4:', binarySearch(sortedArray, 4)); // Expected: -1

console.log('\\nRecursive version:');
console.log('Search for 11:', binarySearchRecursive(sortedArray, 11)); // Expected: 5`,

    'Array Operations': `// Array manipulation examples
const fruits = ['apple', 'banana', 'orange', 'grape'];
console.log('Original fruits:', fruits);

// Filter fruits with more than 5 characters
const longFruits = fruits.filter(fruit => fruit.length > 5);
console.log('Long fruits:', longFruits);

// Transform to uppercase
const upperFruits = fruits.map(fruit => fruit.toUpperCase());
console.log('Uppercase fruits:', upperFruits);

// Find total characters
const totalChars = fruits.reduce((sum, fruit) => sum + fruit.length, 0);
console.log('Total characters:', totalChars);

// Check if any fruit starts with 'a'
const hasApple = fruits.some(fruit => fruit.startsWith('a'));
console.log('Has fruit starting with "a":', hasApple);`,

    'Object Manipulation': `// Working with objects
const person = {
  name: 'Alice',
  age: 30,
  city: 'New York',
  hobbies: ['reading', 'coding', 'hiking']
};

console.log('Person:', person);

// Destructuring
const { name, age, ...rest } = person;
console.log('Name:', name);
console.log('Age:', age);
console.log('Rest:', rest);

// Object methods
const personMethods = {
  ...person,
  introduce() {
    return \`Hi, I'm \${this.name} and I'm \${this.age} years old.\`;
  },
  addHobby(hobby) {
    this.hobbies.push(hobby);
    return this;
  }
};

console.log(personMethods.introduce());
personMethods.addHobby('photography');
console.log('Updated hobbies:', personMethods.hobbies);`,

    'Async Example': `// Async/await example with Promise
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function fetchData() {
  console.log('Starting data fetch...');

  try {
    await delay(1000); // Simulate network delay
    console.log('Data fetched successfully!');
    return { id: 1, name: 'Sample Data', timestamp: new Date().toISOString() };
  } catch (error) {
    console.error('Error fetching data:', error);
    throw error;
  }
}

// Using the async function
fetchData()
  .then(data => {
    console.log('Received data:', data);
  })
  .catch(error => {
    console.error('Failed to fetch:', error);
  });

console.log('This runs immediately while fetch is in progress');`,
  },

  typescript: {
    'Hello World': `// TypeScript Hello World with types
interface Person {
  name: string;
  age: number;
  email?: string;
}

const person: Person = {
  name: 'John Doe',
  age: 25,
  email: '<EMAIL>'
};

console.log('Person:', person);

// Generic function
function identity<T>(arg: T): T {
  return arg;
}

const stringResult = identity<string>('Hello TypeScript!');
const numberResult = identity<number>(42);

console.log('String result:', stringResult);
console.log('Number result:', numberResult);

// Array with types
const numbers: number[] = [1, 2, 3, 4, 5];
const doubled: number[] = numbers.map((n: number) => n * 2);

console.log('Numbers:', numbers);
console.log('Doubled:', doubled);`,

    'Linked List Implementation': `// TypeScript Linked List Implementation
class ListNode<T> {
  value: T;
  next: ListNode<T> | null = null;

  constructor(value: T) {
    this.value = value;
  }
}

class LinkedList<T> {
  private head: ListNode<T> | null = null;
  private size: number = 0;

  // Add element to the end
  append(value: T): void {
    const newNode = new ListNode(value);

    if (!this.head) {
      this.head = newNode;
    } else {
      let current = this.head;
      while (current.next) {
        current = current.next;
      }
      current.next = newNode;
    }
    this.size++;
  }

  // Add element to the beginning
  prepend(value: T): void {
    const newNode = new ListNode(value);
    newNode.next = this.head;
    this.head = newNode;
    this.size++;
  }

  // Find element
  find(value: T): ListNode<T> | null {
    let current = this.head;
    while (current) {
      if (current.value === value) {
        return current;
      }
      current = current.next;
    }
    return null;
  }

  // Convert to array for easy display
  toArray(): T[] {
    const result: T[] = [];
    let current = this.head;
    while (current) {
      result.push(current.value);
      current = current.next;
    }
    return result;
  }

  getSize(): number {
    return this.size;
  }
}

// Test the linked list
const list = new LinkedList<number>();
list.append(1);
list.append(2);
list.append(3);
list.prepend(0);

console.log('Linked List:', list.toArray()); // [0, 1, 2, 3]
console.log('Size:', list.getSize()); // 4
console.log('Find 2:', list.find(2)?.value); // 2
console.log('Find 5:', list.find(5)); // null`,

    'API Response Handler': `// TypeScript API Response Handler with proper typing
interface ApiResponse<T> {
  data: T;
  status: number;
  message: string;
  timestamp: Date;
}

interface User {
  id: number;
  name: string;
  email: string;
  isActive: boolean;
}

interface Post {
  id: number;
  title: string;
  content: string;
  authorId: number;
  createdAt: Date;
}

// Generic API handler
class ApiHandler {
  static async handleResponse<T>(
    response: ApiResponse<T>
  ): Promise<T> {
    if (response.status >= 200 && response.status < 300) {
      console.log(\`✅ Success: \${response.message}\`);
      return response.data;
    } else {
      throw new Error(\`❌ API Error: \${response.message}\`);
    }
  }

  static createMockResponse<T>(
    data: T,
    status: number = 200,
    message: string = 'Success'
  ): ApiResponse<T> {
    return {
      data,
      status,
      message,
      timestamp: new Date()
    };
  }
}

// Usage examples
async function demonstrateApiHandler(): Promise<void> {
  try {
    // Mock user response
    const userResponse = ApiHandler.createMockResponse<User>({
      id: 1,
      name: 'Alice Johnson',
      email: '<EMAIL>',
      isActive: true
    });

    const user = await ApiHandler.handleResponse(userResponse);
    console.log('User data:', user);

    // Mock posts response
    const postsResponse = ApiHandler.createMockResponse<Post[]>([
      {
        id: 1,
        title: 'TypeScript Best Practices',
        content: 'Learn how to write better TypeScript code...',
        authorId: 1,
        createdAt: new Date()
      },
      {
        id: 2,
        title: 'Advanced Type Patterns',
        content: 'Exploring advanced TypeScript patterns...',
        authorId: 1,
        createdAt: new Date()
      }
    ]);

    const posts = await ApiHandler.handleResponse(postsResponse);
    console.log(\`Found \${posts.length} posts:\`);
    posts.forEach(post => {
      console.log(\`- \${post.title}\`);
    });

  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the demonstration
demonstrateApiHandler();`,

    'Class Example': `// TypeScript class with inheritance
abstract class Animal {
  protected name: string;

  constructor(name: string) {
    this.name = name;
  }

  abstract makeSound(): string;

  move(): string {
    return \`\${this.name} is moving\`;
  }
}

class Dog extends Animal {
  private breed: string;

  constructor(name: string, breed: string) {
    super(name);
    this.breed = breed;
  }

  makeSound(): string {
    return \`\${this.name} barks: Woof!\`;
  }

  getBreed(): string {
    return this.breed;
  }
}

class Cat extends Animal {
  makeSound(): string {
    return \`\${this.name} meows: Meow!\`;
  }
}

// Usage
const dog = new Dog('Buddy', 'Golden Retriever');
const cat = new Cat('Whiskers');

console.log(dog.makeSound());
console.log(dog.move());
console.log('Breed:', dog.getBreed());

console.log(cat.makeSound());
console.log(cat.move());`,

    'Interface & Union Types': `// Advanced TypeScript types
interface User {
  id: number;
  username: string;
  email: string;
  isActive: boolean;
}

interface AdminUser extends User {
  permissions: string[];
  lastLogin?: Date;
}

type UserRole = 'admin' | 'user' | 'guest';
type Status = 'online' | 'offline' | 'away';

interface UserWithRole extends User {
  role: UserRole;
  status: Status;
}

// Sample data
const users: UserWithRole[] = [
  {
    id: 1,
    username: 'alice',
    email: '<EMAIL>',
    isActive: true,
    role: 'admin',
    status: 'online'
  },
  {
    id: 2,
    username: 'bob',
    email: '<EMAIL>',
    isActive: true,
    role: 'user',
    status: 'away'
  }
];

// Type guard function
function isAdmin(user: UserWithRole): user is AdminUser & UserWithRole {
  return user.role === 'admin';
}

// Process users
users.forEach(user => {
  console.log(\`User: \${user.username} (\${user.role}) - \${user.status}\`);

  if (isAdmin(user)) {
    console.log('  This user has admin privileges');
  }
});

// Generic utility function
function filterByStatus<T extends { status: Status }>(
  items: T[],
  status: Status
): T[] {
  return items.filter(item => item.status === status);
}

const onlineUsers = filterByStatus(users, 'online');
console.log('Online users:', onlineUsers.map(u => u.username));`,
  },

  python: {
    'Hello World': `# Python Hello World - Test Pyodide Loading
print("🐍 Hello from Python!")
print("Welcome to Codeable!")

# Test basic Python features
print("\\n=== Basic Operations ===")
numbers = [1, 2, 3, 4, 5]
doubled = [n * 2 for n in numbers]
print(f"Original: {numbers}")
print(f"Doubled: {doubled}")

# Test math operations
import math
print(f"\\n=== Math Operations ===")
print(f"Pi: {math.pi:.3f}")
print(f"Square root of 16: {math.sqrt(16)}")
print(f"2 to the power of 8: {2**8}")

# Test string operations
print("\\n=== String Operations ===")
message = "Python is running in your browser!"
print(f"Message: {message}")
print(f"Uppercase: {message.upper()}")
print(f"Word count: {len(message.split())}")

# Simple function
def greet(name):
    return f"Hello, {name}! Python is working! 🎉"

print(f"\\n{greet('Developer')}")`,

    'Data Analysis Example': `# Python Data Analysis with Lists and Dictionaries
import math

# Sample data
students = [
    {"name": "Alice", "scores": [85, 92, 78, 96]},
    {"name": "Bob", "scores": [79, 85, 88, 82]},
    {"name": "Charlie", "scores": [92, 88, 95, 90]},
    {"name": "Diana", "scores": [88, 91, 87, 93]}
]

print("📊 Student Grade Analysis")
print("=" * 40)

# Calculate statistics for each student
for student in students:
    scores = student["scores"]
    average = sum(scores) / len(scores)
    highest = max(scores)
    lowest = min(scores)

    print(f"\\n👤 {student['name']}")
    print(f"   Scores: {scores}")
    print(f"   Average: {average:.1f}")
    print(f"   Highest: {highest}")
    print(f"   Lowest: {lowest}")
    print(f"   Grade: {get_letter_grade(average)}")

def get_letter_grade(score):
    if score >= 90:
        return "A"
    elif score >= 80:
        return "B"
    elif score >= 70:
        return "C"
    elif score >= 60:
        return "D"
    else:
        return "F"

# Class statistics
all_scores = [score for student in students for score in student["scores"]]
class_average = sum(all_scores) / len(all_scores)

print(f"\\n📈 Class Statistics")
print(f"   Class Average: {class_average:.1f}")
print(f"   Total Students: {len(students)}")
print(f"   Total Scores: {len(all_scores)}")`,

    'Algorithm Practice': `# Python Algorithm Practice
# Common algorithms and data structures

def binary_search(arr, target):
    """Binary search implementation"""
    left, right = 0, len(arr) - 1

    while left <= right:
        mid = (left + right) // 2
        if arr[mid] == target:
            return mid
        elif arr[mid] < target:
            left = mid + 1
        else:
            right = mid - 1

    return -1

def bubble_sort(arr):
    """Bubble sort implementation"""
    n = len(arr)
    for i in range(n):
        for j in range(0, n - i - 1):
            if arr[j] > arr[j + 1]:
                arr[j], arr[j + 1] = arr[j + 1], arr[j]
    return arr

def factorial(n):
    """Calculate factorial recursively"""
    if n <= 1:
        return 1
    return n * factorial(n - 1)

def is_prime(n):
    """Check if a number is prime"""
    if n < 2:
        return False
    for i in range(2, int(n ** 0.5) + 1):
        if n % i == 0:
            return False
    return True

# Test the algorithms
print("🔍 Algorithm Testing")
print("=" * 30)

# Binary search test
sorted_list = [1, 3, 5, 7, 9, 11, 13, 15]
target = 7
result = binary_search(sorted_list, target)
print(f"Binary search for {target} in {sorted_list}: index {result}")

# Sorting test
unsorted_list = [64, 34, 25, 12, 22, 11, 90]
print(f"\\nOriginal: {unsorted_list}")
sorted_result = bubble_sort(unsorted_list.copy())
print(f"Sorted: {sorted_result}")

# Factorial test
n = 5
print(f"\\nFactorial of {n}: {factorial(n)}")

# Prime number test
print(f"\\nPrime numbers up to 20:")
primes = [i for i in range(2, 21) if is_prime(i)]
print(primes)`,

    'Interactive Input Example': `# Python Interactive Input Example
# This demonstrates how to use input() in your Python programs

print("🎯 Interactive Python Program")
print("=" * 30)

# Get user information
name = input("What's your name? ")
age = input("How old are you? ")

print(f"\\nHello, {name}! You are {age} years old.")

# Simple calculator with input
print("\\n🧮 Simple Calculator")
num1 = float(input("Enter first number: "))
operator = input("Enter operator (+, -, *, /): ")
num2 = float(input("Enter second number: "))

if operator == '+':
    result = num1 + num2
elif operator == '-':
    result = num1 - num2
elif operator == '*':
    result = num1 * num2
elif operator == '/':
    result = num1 / num2 if num2 != 0 else "Error: Division by zero"
else:
    result = "Error: Invalid operator"

print(f"\\nResult: {num1} {operator} {num2} = {result}")

# List operations with input
print("\\n📝 Favorite Things")
favorites = []
while True:
    item = input("Enter a favorite thing (or 'done' to finish): ")
    if item.lower() == 'done':
        break
    favorites.append(item)

print(f"\\nYour favorite things: {', '.join(favorites)}")
print(f"You entered {len(favorites)} items!")

print("\\n✨ Program completed!")`,

    'Class Example': `# Python class example
class Animal:
    def __init__(self, name, species):
        self.name = name
        self.species = species

    def make_sound(self):
        return f"{self.name} makes a sound"

    def __str__(self):
        return f"{self.name} ({self.species})"

class Dog(Animal):
    def __init__(self, name, breed):
        super().__init__(name, "Dog")
        self.breed = breed

    def make_sound(self):
        return f"{self.name} barks: Woof!"

    def fetch(self):
        return f"{self.name} fetches the ball"

# Usage
dog = Dog("Buddy", "Golden Retriever")
print(dog)
print(dog.make_sound())
print(dog.fetch())`,
  },
};

export function getTemplate(language: string, templateName: string): string {
  const languageTemplates = CODE_TEMPLATES[language as keyof typeof CODE_TEMPLATES];
  if (!languageTemplates) {
    return getDefaultTemplate(language);
  }

  return languageTemplates[templateName as keyof typeof languageTemplates] || getDefaultTemplate(language);
}

export function getTemplateNames(language: string): string[] {
  const languageTemplates = CODE_TEMPLATES[language as keyof typeof CODE_TEMPLATES];
  return languageTemplates ? Object.keys(languageTemplates) : [];
}

export function getDefaultTemplate(language: string): string {
  const templates: Record<string, string> = {
    javascript: `// JavaScript solution
console.log('Hello, World!');

// Your code here
function solution() {
    // Write your solution
}`,

    typescript: `// TypeScript solution
console.log('Hello, World!');

// Your code here
function solution(): void {
    // Write your solution
}`,

    python: `# Python solution
print("Hello, World!")

# Your code here
def solution():
    # Write your solution
    pass`,

    java: `// Java solution
public class Solution {
    public static void main(String[] args) {
        System.out.println("Hello, World!");
        // Your code here
    }
}`,

    cpp: `// C++ solution
#include <iostream>
using namespace std;

int main() {
    cout << "Hello, World!" << endl;
    // Your code here
    return 0;
}`,

    go: `// Go solution
package main

import "fmt"

func main() {
    fmt.Println("Hello, World!")
    // Your code here
}`,

    rust: `// Rust solution
fn main() {
    println!("Hello, World!");
    // Your code here
}`,

    markdown: `# Notes

Write your notes here...

## Code Examples

\`\`\`javascript
// Example code
console.log('Hello, World!');
\`\`\`
`,

    text: `Write your notes here...
`,
  };

  return templates[language] || '// Your code here\n';
}
