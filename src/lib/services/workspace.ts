import { databases, COLLECTIONS, APPWRITE_DATABASE_ID } from '@/lib/appwrite/config';
import { ID, Query } from 'appwrite';
import type { Workspace, CreateWorkspaceData, UpdateWorkspaceData } from '@/types';

export class WorkspaceService {
  // Create a new workspace
  static async createWorkspace(data: CreateWorkspaceData, userId: string): Promise<Workspace> {
    try {
      const workspace = await databases.createDocument(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.WORKSPACES,
        ID.unique(),
        {
          ...data,
          userId,
        }
      );
      
      return workspace as Workspace;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to create workspace');
    }
  }

  // Get all workspaces for a user
  static async getWorkspaces(userId: string): Promise<Workspace[]> {
    try {
      const response = await databases.listDocuments(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.WORKSPACES,
        [
          Query.equal('userId', userId),
          Query.orderDesc('$updatedAt'),
          Query.limit(100)
        ]
      );
      
      return response.documents as Workspace[];
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch workspaces');
    }
  }

  // Get a specific workspace
  static async getWorkspace(workspaceId: string): Promise<Workspace> {
    try {
      const workspace = await databases.getDocument(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.WORKSPACES,
        workspaceId
      );
      
      return workspace as Workspace;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch workspace');
    }
  }

  // Update a workspace
  static async updateWorkspace(workspaceId: string, data: UpdateWorkspaceData): Promise<Workspace> {
    try {
      const workspace = await databases.updateDocument(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.WORKSPACES,
        workspaceId,
        {
          ...data,
        }
      );
      
      return workspace as Workspace;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to update workspace');
    }
  }

  // Delete a workspace
  static async deleteWorkspace(workspaceId: string): Promise<void> {
    try {
      await databases.deleteDocument(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.WORKSPACES,
        workspaceId
      );
    } catch (error: any) {
      throw new Error(error.message || 'Failed to delete workspace');
    }
  }

  // Search workspaces
  static async searchWorkspaces(userId: string, query: string): Promise<Workspace[]> {
    try {
      const response = await databases.listDocuments(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.WORKSPACES,
        [
          Query.equal('userId', userId),
          Query.search('name', query),
          Query.orderDesc('$updatedAt'),
          Query.limit(50)
        ]
      );
      
      return response.documents as Workspace[];
    } catch (error: any) {
      throw new Error(error.message || 'Failed to search workspaces');
    }
  }

  // Get workspaces by category
  static async getWorkspacesByCategory(userId: string, category: string): Promise<Workspace[]> {
    try {
      const response = await databases.listDocuments(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.WORKSPACES,
        [
          Query.equal('userId', userId),
          Query.equal('category', category),
          Query.orderDesc('$updatedAt'),
          Query.limit(100)
        ]
      );
      
      return response.documents as Workspace[];
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch workspaces by category');
    }
  }
}