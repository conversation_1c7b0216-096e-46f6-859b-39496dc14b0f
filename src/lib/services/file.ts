import { databases, COLLECTIONS, APPWRITE_DATABASE_ID, client } from '@/lib/appwrite/config';
import { ID, Query } from 'appwrite';
import type { File, CreateFileData } from '@/types';

export class FileService {
  // Create a new file
  static async createFile(data: CreateFileData, userId: string): Promise<File> {
    try {
      const file = await databases.createDocument(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.FILES,
        ID.unique(),
        {
          ...data,
          content: data.content || '',
          userId,
        }
      );
      
      return file as File;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to create file');
    }
  }

  // Get all files in a workspace
  static async getFiles(workspaceId: string): Promise<File[]> {
    try {
      const response = await databases.listDocuments(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.FILES,
        [
          Query.equal('workspaceId', workspaceId),
          Query.orderDesc('$updatedAt'),
          Query.limit(100)
        ]
      );
      
      return response.documents as File[];
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch files');
    }
  }

  // Get a specific file
  static async getFile(fileId: string): Promise<File> {
    try {
      const file = await databases.getDocument(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.FILES,
        fileId
      );
      
      return file as File;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch file');
    }
  }

  // Update file content
  static async updateFile(fileId: string, content: string): Promise<File> {
    try {
      const file = await databases.updateDocument(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.FILES,
        fileId,
        {
          content,
        }
      );
      
      return file as File;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to update file');
    }
  }

  // Update file metadata
  static async updateFileMetadata(fileId: string, data: { name?: string; language?: string }): Promise<File> {
    try {
      const file = await databases.updateDocument(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.FILES,
        fileId,
        {
          ...data,
        }
      );
      
      return file as File;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to update file metadata');
    }
  }

  // Delete a file
  static async deleteFile(fileId: string): Promise<void> {
    try {
      await databases.deleteDocument(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.FILES,
        fileId
      );
    } catch (error: any) {
      throw new Error(error.message || 'Failed to delete file');
    }
  }

  // Search files across all workspaces for a user
  static async searchFiles(userId: string, query: string): Promise<File[]> {
    try {
      const response = await databases.listDocuments(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.FILES,
        [
          Query.equal('userId', userId),
          Query.search('name', query),
          Query.orderDesc('$updatedAt'),
          Query.limit(50)
        ]
      );
      
      return response.documents as File[];
    } catch (error: any) {
      throw new Error(error.message || 'Failed to search files');
    }
  }

  // Get recent files for a user
  static async getRecentFiles(userId: string, limit: number = 10): Promise<File[]> {
    try {
      const response = await databases.listDocuments(
        APPWRITE_DATABASE_ID,
        COLLECTIONS.FILES,
        [
          Query.equal('userId', userId),
          Query.orderDesc('$updatedAt'),
          Query.limit(limit)
        ]
      );
      
      return response.documents as File[];
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch recent files');
    }
  }

  // Subscribe to file changes for real-time updates
  static subscribeToFileChanges(fileId: string, callback: (file: File) => void): () => void {
    const unsubscribe = client.subscribe(
      `databases.${APPWRITE_DATABASE_ID}.collections.${COLLECTIONS.FILES}.documents.${fileId}`,
      (response) => {
        if (response.events.includes('databases.*.collections.*.documents.*.update')) {
          callback(response.payload as File);
        }
      }
    );

    return unsubscribe;
  }

  // Subscribe to workspace file changes
  static subscribeToWorkspaceFiles(workspaceId: string, callback: (file: File, action: 'create' | 'update' | 'delete') => void): () => void {
    const unsubscribe = client.subscribe(
      `databases.${APPWRITE_DATABASE_ID}.collections.${COLLECTIONS.FILES}.documents`,
      (response) => {
        const file = response.payload as File;
        
        // Only handle files from the specified workspace
        if (file.workspaceId !== workspaceId) return;

        if (response.events.includes('databases.*.collections.*.documents.*.create')) {
          callback(file, 'create');
        } else if (response.events.includes('databases.*.collections.*.documents.*.update')) {
          callback(file, 'update');
        } else if (response.events.includes('databases.*.collections.*.documents.*.delete')) {
          callback(file, 'delete');
        }
      }
    );

    return unsubscribe;
  }
}