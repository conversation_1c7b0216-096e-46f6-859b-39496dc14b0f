import { account } from '@/lib/appwrite/config';
import { ID } from 'appwrite';
import type { User } from '@/types';

export class AuthService {
  // Register a new user
  static async register(email: string, password: string, name: string): Promise<User> {
    try {
      // Create account and immediately create session
      const user = await account.create(ID.unique(), email, password, name);
      
      // Create session immediately after account creation
      await account.createEmailPasswordSession(email, password);
      
      // Send email verification
      await account.createVerification('http://localhost:3000/verify');
      
      return user as User;
    } catch (error: any) {
      throw new Error(error.message || 'Registration failed');
    }
  }

  // Login user
  static async login(email: string, password: string): Promise<User> {
    try {
      await account.createEmailPasswordSession(email, password);
      const user = await account.get();
      return user as User;
    } catch (error: any) {
      throw new Error(error.message || 'Login failed');
    }
  }

  // Get current user
  static async getCurrentUser(): Promise<User | null> {
    try {
      const user = await account.get();
      return user as User;
    } catch (error) {
      return null;
    }
  }

  // Logout
  static async logout(): Promise<void> {
    try {
      await account.deleteSession('current');
    } catch (error: any) {
      throw new Error(error.message || 'Logout failed');
    }
  }

  // Reset password
  static async resetPassword(email: string): Promise<void> {
    try {
      await account.createRecovery(email, 'http://localhost:3000/reset-password');
    } catch (error: any) {
      throw new Error(error.message || 'Password reset failed');
    }
  }

  // Update password
  static async updatePassword(newPassword: string, oldPassword: string): Promise<void> {
    try {
      await account.updatePassword(newPassword, oldPassword);
    } catch (error: any) {
      throw new Error(error.message || 'Password update failed');
    }
  }

  // Verify email
  static async verifyEmail(userId: string, secret: string): Promise<void> {
    try {
      await account.updateVerification(userId, secret);
    } catch (error: any) {
      throw new Error(error.message || 'Email verification failed');
    }
  }
}