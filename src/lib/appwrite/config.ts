import { Client, Account, Databases, Storage } from 'appwrite';

// Environment variables
export const APPWRITE_ENDPOINT = process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT!;
export const APPWRITE_PROJECT_ID = process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID!;
export const APPWRITE_DATABASE_ID = process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID!;

// Collection IDs
export const COLLECTIONS = {
  WORKSPACES: process.env.NEXT_PUBLIC_WORKSPACES_COLLECTION_ID!,
  FILES: process.env.NEXT_PUBLIC_FILES_COLLECTION_ID!,
  SESSIONS: process.env.NEXT_PUBLIC_SESSIONS_COLLECTION_ID!,
} as const;

// Initialize Appwrite client
export const client = new Client()
  .setEndpoint(APPWRITE_ENDPOINT)
  .setProject(APPWRITE_PROJECT_ID);

// Initialize services
export const account = new Account(client);
export const databases = new Databases(client);
export const storage = new Storage(client);

// Helper function to check if environment variables are configured
export function checkAppwriteConfig() {
  const requiredVars = [
    'NEXT_PUBLIC_APPWRITE_ENDPOINT',
    'NEXT_PUBLIC_APPWRITE_PROJECT_ID',
    'NEXT_PUBLIC_APPWRITE_DATABASE_ID',
    'NEXT_PUBLIC_WORKSPACES_COLLECTION_ID',
    'NEXT_PUBLIC_FILES_COLLECTION_ID',
    'NEXT_PUBLIC_SESSIONS_COLLECTION_ID',
  ];

  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
}