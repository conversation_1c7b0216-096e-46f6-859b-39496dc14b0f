// Server-side code execution (requires backend API)
export class ServerCodeExecutor {
  private static readonly API_BASE = process.env.NEXT_PUBLIC_CODE_EXECUTION_API || '/api/execute';

  static async executeCode(
    code: string,
    language: string,
    input?: string
  ): Promise<{
    output: string;
    error?: string;
    executionTime: number;
    memoryUsage?: number;
  }> {
    try {
      const response = await fetch(`${this.API_BASE}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code,
          language,
          input,
          timeout: 10000, // 10 seconds
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error: any) {
      return {
        output: '',
        error: `Execution failed: ${error.message}`,
        executionTime: 0,
      };
    }
  }
}
