// Browser-based code execution for JavaScript/TypeScript
export class BrowserCodeExecutor {
  static async executeJavaScript(code: string): Promise<{
    output: string;
    error?: string;
    executionTime: number;
  }> {
    const startTime = Date.now();
    const logs: string[] = [];
    const errors: string[] = [];

    // Debug: Log the code being executed
    console.log('Executing code:', code);

    try {
      // Create safe console that captures output
      const safeConsole = {
        log: (...args: any[]) => {
          const message = args.map(arg => {
            if (typeof arg === 'object' && arg !== null) {
              try {
                return JSON.stringify(arg, null, 2);
              } catch {
                return '[Object]';
              }
            }
            return String(arg);
          }).join(' ');
          logs.push(message);
        },
        error: (...args: any[]) => {
          const message = args.map(arg => String(arg)).join(' ');
          errors.push(message);
        },
        warn: (...args: any[]) => {
          const message = 'WARN: ' + args.map(arg => String(arg)).join(' ');
          logs.push(message);
        },
        info: (...args: any[]) => {
          const message = 'INFO: ' + args.map(arg => String(arg)).join(' ');
          logs.push(message);
        },
      };

      // Execute with timeout
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Code execution timeout (5 seconds)')), 5000)
      );

      const executionPromise = new Promise((resolve, reject) => {
        try {
          // Create a simple execution context with input support
          const context = {
            console: safeConsole,
            Math,
            Date,
            JSON,
            Array,
            Object,
            String,
            Number,
            Boolean,
            RegExp,
            parseInt,
            parseFloat,
            isNaN,
            isFinite,
            // Simple prompt simulation for user input
            prompt: (message?: string) => {
              safeConsole.log(`INPUT REQUESTED: ${message || 'Enter input:'}`);
              return 'user_input_placeholder'; // This would be replaced with actual input
            },
            // Simple alert simulation
            alert: (message: any) => {
              safeConsole.log(`ALERT: ${message}`);
            },
          };

          // Use a simpler approach - create function with context
          const wrappedCode = `
            (function() {
              // Destructure safe globals
              const { console, Math, Date, JSON, Array, Object, String, Number, Boolean, RegExp, parseInt, parseFloat, isNaN, isFinite, prompt, alert } = arguments[0];

              // User code
              ${code}
            })
          `;

          const userFunction = eval(wrappedCode);
          userFunction(context);
          resolve(undefined);
        } catch (err: any) {
          reject(err);
        }
      });

      await Promise.race([executionPromise, timeoutPromise]);

      const result = {
        output: logs.join('\n'),
        error: errors.length > 0 ? errors.join('\n') : undefined,
        executionTime: Date.now() - startTime,
      };

      // Debug: Log the execution result
      console.log('Execution result:', result);

      return result;

    } catch (err: any) {
      // Provide more detailed error information
      let errorMessage = err.message;

      // Add line number information if available
      if (err.stack) {
        const stackLines = err.stack.split('\n');
        const relevantLine = stackLines.find(line => line.includes('eval') || line.includes('<anonymous>'));
        if (relevantLine) {
          const match = relevantLine.match(/:(\d+):(\d+)/);
          if (match) {
            errorMessage += ` (Line ${match[1]}, Column ${match[2]})`;
          }
        }
      }

      return {
        output: logs.join('\n'),
        error: errorMessage,
        executionTime: Date.now() - startTime,
      };
    }
  }

  static async executeTypeScript(code: string): Promise<{
    output: string;
    error?: string;
    executionTime: number;
  }> {
    // Simple TypeScript to JavaScript conversion
    let jsCode = code
      // Remove type annotations from variables
      .replace(/:\s*\w+(\[\])?(\s*=)/g, '$2')
      // Remove type annotations from function parameters
      .replace(/(\w+):\s*\w+(\[\])?/g, '$1')
      // Remove function return types
      .replace(/\):\s*\w+(\[\])?\s*{/g, ') {')
      // Remove interface declarations
      .replace(/interface\s+\w+\s*{[^}]*}/g, '')
      // Remove type aliases
      .replace(/type\s+\w+\s*=\s*[^;]+;/g, '')
      // Remove generic type parameters
      .replace(/<[^>]+>/g, '')
      // Remove 'as' type assertions
      .replace(/\s+as\s+\w+/g, '');

    return this.executeJavaScript(jsCode);
  }

  static async executePython(code: string, userInputs: string[] = []): Promise<{
    output: string;
    error?: string;
    executionTime: number;
  }> {
    const startTime = Date.now();

    try {
      // Try to get or load Pyodide on demand
      let pyodide = (window as any).pyodide;

      if (!pyodide) {
        // Load Pyodide on demand
        if (typeof window !== 'undefined' && (window as any).loadPyodideOnDemand) {
          try {
            pyodide = await (window as any).loadPyodideOnDemand();
            if (!pyodide) {
              return {
                output: '🔄 Loading Python environment...\n\nPyodide is being loaded on demand.\nThis may take 10-30 seconds on first load.\n\nPlease wait and try again in a moment.',
                error: undefined,
                executionTime: Date.now() - startTime,
              };
            }
          } catch (error) {
            return {
              output: '',
              error: `Failed to load Python environment: ${error}`,
              executionTime: Date.now() - startTime,
            };
          }
        } else {
          return {
            output: '',
            error: 'Python environment is not available. Please refresh the page and try again.',
            executionTime: Date.now() - startTime,
          };
        }
      }

      // Set up output capture
      pyodide.runPython(`
import sys
from io import StringIO
_captured_output = StringIO()
sys.stdout = _captured_output
      `);

      // Execute the user's Python code
      pyodide.runPython(code);

      // Get the captured output
      const output = pyodide.runPython('_captured_output.getvalue()');

      // Reset stdout
      pyodide.runPython('sys.stdout = sys.__stdout__');

      return {
        output: output || 'Code executed successfully (no output)',
        executionTime: Date.now() - startTime,
      };
    } catch (err: any) {
      return {
        output: '',
        error: `Python Error: ${err.message}`,
        executionTime: Date.now() - startTime,
      };
    }
  }

  private static async loadPyodide() {
    try {
      if (typeof window !== 'undefined' && typeof (window as any).loadPyodide === 'function') {
        console.log('🐍 Loading Pyodide...');
        (window as any).pyodide = await (window as any).loadPyodide();
        (window as any).pyodideLoading = false;
        console.log('✅ Pyodide loaded successfully!');
      }
    } catch (error) {
      console.error('❌ Failed to load Pyodide:', error);
      (window as any).pyodideLoading = false;
    }
  }
}
