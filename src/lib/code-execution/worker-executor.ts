export class WorkerCodeExecutor {
  static async executeCode(
    code: string,
    language: string,
    timeout: number = 5000
  ): Promise<{
    output: string;
    error?: string;
    executionTime: number;
  }> {
    return new Promise((resolve, reject) => {
      const worker = new Worker('/code-worker.js');

      const cleanup = () => {
        worker.terminate();
      };

      worker.onmessage = (e) => {
        cleanup();
        resolve(e.data);
      };

      worker.onerror = (err) => {
        cleanup();
        reject(new Error(`Worker error: ${err.message}`));
      };

      // Send code to worker
      worker.postMessage({ code, language, timeout });

      // Fallback timeout
      setTimeout(() => {
        cleanup();
        resolve({
          output: '',
          error: 'Worker timeout exceeded',
          executionTime: timeout,
        });
      }, timeout + 1000);
    });
  }
}
