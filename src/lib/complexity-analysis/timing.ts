/**
 * High-precision timing utilities for complexity analysis
 */

import { TimeMeasurement, PerformanceMeasurement } from './types';

/**
 * High-resolution timer for accurate performance measurement
 */
export class PrecisionTimer {
  private startTime: number = 0;
  private endTime: number = 0;
  private isRunning: boolean = false;

  /**
   * Starts the timer
   */
  start(): void {
    this.isRunning = true;
    this.startTime = performance.now();
  }

  /**
   * Stops the timer and returns elapsed time
   */
  stop(): number {
    if (!this.isRunning) {
      throw new Error('Timer not started');
    }

    this.endTime = performance.now();
    this.isRunning = false;

    return this.getElapsedTime();
  }

  /**
   * Gets elapsed time without stopping the timer
   */
  getElapsedTime(): number {
    if (this.isRunning) {
      return performance.now() - this.startTime;
    }

    return this.endTime - this.startTime;
  }

  /**
   * Resets the timer
   */
  reset(): void {
    this.startTime = 0;
    this.endTime = 0;
    this.isRunning = false;
  }
}

/**
 * Timing utilities for code execution measurement
 */
export class TimingUtils {
  /**
   * Measures execution time of a synchronous function
   */
  static measureSync<T>(fn: () => T): { result: T; time: number } {
    const timer = new PrecisionTimer();

    timer.start();
    const result = fn();
    const time = timer.stop();

    return { result, time };
  }

  /**
   * Measures execution time of an asynchronous function
   */
  static async measureAsync<T>(fn: () => Promise<T>): Promise<{ result: T; time: number }> {
    const timer = new PrecisionTimer();

    timer.start();
    const result = await fn();
    const time = timer.stop();

    return { result, time };
  }

  /**
   * Measures execution time with multiple iterations for statistical accuracy
   */
  static async measureWithIterations<T>(
    fn: () => Promise<T>,
    iterations: number = 3,
    warmupRuns: number = 1
  ): Promise<{
    results: T[];
    times: number[];
    averageTime: number;
    minTime: number;
    maxTime: number;
    standardDeviation: number;
  }> {
    if (iterations < 1) {
      throw new Error('Iterations must be at least 1');
    }

    const results: T[] = [];
    const times: number[] = [];

    // Warm-up runs to stabilize performance
    for (let i = 0; i < warmupRuns; i++) {
      try {
        await fn();
      } catch {
        // Ignore warm-up errors
      }
    }

    // Actual measurement runs
    for (let i = 0; i < iterations; i++) {
      try {
        const { result, time } = await this.measureAsync(fn);
        results.push(result);
        times.push(time);
      } catch (error) {
        console.warn(`Iteration ${i + 1} failed:`, error);
        // Use a penalty time for failed iterations
        times.push(Number.MAX_SAFE_INTEGER);
      }
    }

    // Calculate statistics
    const averageTime = times.reduce((sum, time) => sum + time, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);

    // Calculate standard deviation
    const variance = times.reduce((sum, time) => sum + Math.pow(time - averageTime, 2), 0) / times.length;
    const standardDeviation = Math.sqrt(variance);

    return {
      results,
      times,
      averageTime,
      minTime,
      maxTime,
      standardDeviation,
    };
  }

  /**
   * Measures execution time with timeout protection
   */
  static async measureWithTimeout<T>(
    fn: () => Promise<T>,
    timeoutMs: number
  ): Promise<{ result: T; time: number } | { error: string; time: number }> {
    const timer = new PrecisionTimer();

    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error(`Execution timed out after ${timeoutMs}ms`)), timeoutMs);
    });

    timer.start();

    try {
      const result = await Promise.race([fn(), timeoutPromise]);
      const time = timer.stop();
      return { result, time };
    } catch (error) {
      const time = timer.stop();
      return {
        error: error instanceof Error ? error.message : 'Unknown error',
        time
      };
    }
  }

  /**
   * Creates a timing measurement record
   */
  static createTimeMeasurement(
    inputSize: number,
    executionTime: number,
    iterations: number = 1
  ): TimeMeasurement {
    return {
      inputSize,
      executionTime,
      iterations,
      timestamp: new Date(),
    };
  }

  /**
   * Calculates timing statistics from multiple measurements
   */
  static calculateTimingStats(measurements: TimeMeasurement[]): {
    averageTime: number;
    minTime: number;
    maxTime: number;
    totalTime: number;
    timePerIteration: number;
    consistency: number; // 0-1, higher is more consistent
  } {
    if (measurements.length === 0) {
      throw new Error('No measurements provided');
    }

    const times = measurements.map(m => m.executionTime);
    const totalIterations = measurements.reduce((sum, m) => sum + m.iterations, 0);

    const averageTime = times.reduce((sum, time) => sum + time, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    const totalTime = times.reduce((sum, time) => sum + time, 0);
    const timePerIteration = totalTime / totalIterations;

    // Calculate consistency (inverse of coefficient of variation)
    const variance = times.reduce((sum, time) => sum + Math.pow(time - averageTime, 2), 0) / times.length;
    const standardDeviation = Math.sqrt(variance);
    const coefficientOfVariation = standardDeviation / averageTime;
    const consistency = Math.max(0, Math.min(1, 1 - coefficientOfVariation));

    return {
      averageTime,
      minTime,
      maxTime,
      totalTime,
      timePerIteration,
      consistency,
    };
  }

  /**
   * Filters out outlier measurements based on statistical analysis
   */
  static filterOutliers(measurements: TimeMeasurement[], threshold: number = 2): TimeMeasurement[] {
    if (measurements.length < 3) {
      return measurements; // Not enough data to filter outliers
    }

    const times = measurements.map(m => m.executionTime);
    const mean = times.reduce((sum, time) => sum + time, 0) / times.length;
    const variance = times.reduce((sum, time) => sum + Math.pow(time - mean, 2), 0) / times.length;
    const standardDeviation = Math.sqrt(variance);

    return measurements.filter(measurement => {
      const zScore = Math.abs(measurement.executionTime - mean) / standardDeviation;
      return zScore <= threshold;
    });
  }

  /**
   * Smooths timing data using moving average
   */
  static smoothTimingData(measurements: TimeMeasurement[], windowSize: number = 3): TimeMeasurement[] {
    if (measurements.length < windowSize) {
      return measurements;
    }

    const smoothed: TimeMeasurement[] = [];
    const halfWindow = Math.floor(windowSize / 2);

    for (let i = 0; i < measurements.length; i++) {
      const start = Math.max(0, i - halfWindow);
      const end = Math.min(measurements.length, i + halfWindow + 1);
      const window = measurements.slice(start, end);

      const averageTime = window.reduce((sum, m) => sum + m.executionTime, 0) / window.length;
      const averageIterations = window.reduce((sum, m) => sum + m.iterations, 0) / window.length;

      smoothed.push({
        inputSize: measurements[i].inputSize,
        executionTime: averageTime,
        iterations: Math.round(averageIterations),
        timestamp: measurements[i].timestamp,
      });
    }

    return smoothed;
  }

  /**
   * Validates timing measurements for consistency
   */
  static validateTimingMeasurements(measurements: TimeMeasurement[]): {
    isValid: boolean;
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];

    if (measurements.length === 0) {
      issues.push('No measurements provided');
      return { isValid: false, issues, recommendations };
    }

    // Check for negative times
    const negativeTimes = measurements.filter(m => m.executionTime < 0);
    if (negativeTimes.length > 0) {
      issues.push(`${negativeTimes.length} measurements have negative execution times`);
    }

    // Check for extremely high variance
    const stats = this.calculateTimingStats(measurements);
    if (stats.consistency < 0.5) {
      issues.push('High variance in timing measurements detected');
      recommendations.push('Consider increasing the number of iterations or filtering outliers');
    }

    // Check for insufficient iterations
    const lowIterationMeasurements = measurements.filter(m => m.iterations < 3);
    if (lowIterationMeasurements.length > measurements.length * 0.5) {
      recommendations.push('Consider using more iterations per measurement for better accuracy');
    }

    // Check for monotonic growth (expected for most algorithms)
    const sortedByInputSize = [...measurements].sort((a, b) => a.inputSize - b.inputSize);
    let nonMonotonicCount = 0;
    for (let i = 1; i < sortedByInputSize.length; i++) {
      if (sortedByInputSize[i].executionTime < sortedByInputSize[i - 1].executionTime) {
        nonMonotonicCount++;
      }
    }

    if (nonMonotonicCount > measurements.length * 0.3) {
      issues.push('Timing measurements do not show expected growth pattern');
      recommendations.push('This might indicate measurement noise or an unusual algorithm behavior');
    }

    return {
      isValid: issues.length === 0,
      issues,
      recommendations,
    };
  }
}

/**
 * Specialized timer for browser-based code execution
 */
export class CodeExecutionTimer {
  private measurements: Map<string, TimeMeasurement[]> = new Map();

  /**
   * Records a timing measurement for a specific code execution
   */
  recordMeasurement(
    codeId: string,
    inputSize: number,
    executionTime: number,
    iterations: number = 1
  ): void {
    const measurement = TimingUtils.createTimeMeasurement(inputSize, executionTime, iterations);

    if (!this.measurements.has(codeId)) {
      this.measurements.set(codeId, []);
    }

    this.measurements.get(codeId)!.push(measurement);
  }

  /**
   * Gets all measurements for a specific code execution
   */
  getMeasurements(codeId: string): TimeMeasurement[] {
    return this.measurements.get(codeId) || [];
  }

  /**
   * Gets timing statistics for a specific code execution
   */
  getTimingStats(codeId: string) {
    const measurements = this.getMeasurements(codeId);
    if (measurements.length === 0) {
      return null;
    }

    return TimingUtils.calculateTimingStats(measurements);
  }

  /**
   * Clears all measurements
   */
  clear(): void {
    this.measurements.clear();
  }

  /**
   * Clears measurements for a specific code execution
   */
  clearMeasurements(codeId: string): void {
    this.measurements.delete(codeId);
  }

  /**
   * Gets all code IDs that have measurements
   */
  getCodeIds(): string[] {
    return Array.from(this.measurements.keys());
  }
}

/**
 * Global timer instance for the application
 */
export const globalCodeExecutionTimer = new CodeExecutionTimer();
