/**
 * Pattern detection algorithms for automatic input type recognition
 * Analyzes input data to determine the most appropriate scaling strategy
 */

import { InputPattern } from './types';

/**
 * Pattern detection result
 */
interface PatternDetectionResult {
  pattern: InputPattern;
  confidence: number; // 0-1, higher is more confident
  characteristics: string[];
  recommendations: string[];
}

/**
 * Advanced pattern detector
 */
export class PatternDetector {
  /**
   * Analyzes input and detects its pattern with confidence scoring
   */
  static analyzeInput(input: any): PatternDetectionResult {
    const detectors = [
      this.detectArrayPattern,
      this.detectStringPattern,
      this.detectNumberPattern,
      this.detectMatrixPattern,
      this.detectTreePattern,
      this.detectGraphPattern,
      this.detectObjectPattern,
    ];

    let bestResult: PatternDetectionResult = {
      pattern: 'custom',
      confidence: 0,
      characteristics: ['Unknown input type'],
      recommendations: ['Use custom scaling strategy'],
    };

    for (const detector of detectors) {
      try {
        const result = detector.call(this, input);
        if (result.confidence > bestResult.confidence) {
          bestResult = result;
        }
      } catch (error) {
        console.warn('Pattern detection error:', error);
      }
    }

    return bestResult;
  }

  /**
   * Detects array patterns
   */
  private static detectArrayPattern(input: any): PatternDetectionResult {
    if (!Array.isArray(input)) {
      return { pattern: 'array', confidence: 0, characteristics: [], recommendations: [] };
    }

    const characteristics: string[] = [];
    const recommendations: string[] = [];
    let confidence = 0.9; // High confidence for arrays

    // Analyze array contents
    if (input.length === 0) {
      characteristics.push('Empty array');
      recommendations.push('Will generate default values during scaling');
      confidence = 0.7;
    } else {
      // Check element types
      const elementTypes = new Set(input.map(item => typeof item));
      characteristics.push(`${input.length} elements`);
      characteristics.push(`Element types: ${Array.from(elementTypes).join(', ')}`);

      // Check for homogeneous types
      if (elementTypes.size === 1) {
        const elementType = Array.from(elementTypes)[0];
        characteristics.push(`Homogeneous ${elementType} array`);
        confidence += 0.05;

        if (elementType === 'number') {
          const numericAnalysis = this.analyzeNumericArray(input);
          characteristics.push(...numericAnalysis.characteristics);
          recommendations.push(...numericAnalysis.recommendations);
        }
      } else {
        characteristics.push('Mixed-type array');
        recommendations.push('Consider using pattern-based scaling');
        confidence -= 0.1;
      }

      // Check for nested arrays (matrix pattern)
      if (input.some(item => Array.isArray(item))) {
        characteristics.push('Contains nested arrays');
        recommendations.push('Consider matrix pattern detection');
      }

      // Check for sorted order
      if (this.isNumericArray(input)) {
        const sortedAsc = this.isSorted(input, true);
        const sortedDesc = this.isSorted(input, false);

        if (sortedAsc) {
          characteristics.push('Sorted in ascending order');
          recommendations.push('Use interpolation scaling for smooth growth');
        } else if (sortedDesc) {
          characteristics.push('Sorted in descending order');
          recommendations.push('Consider reversing before scaling');
        }
      }
    }

    return {
      pattern: 'array',
      confidence,
      characteristics,
      recommendations,
    };
  }

  /**
   * Detects string patterns
   */
  private static detectStringPattern(input: any): PatternDetectionResult {
    if (typeof input !== 'string') {
      return { pattern: 'string', confidence: 0, characteristics: [], recommendations: [] };
    }

    const characteristics: string[] = [];
    const recommendations: string[] = [];
    let confidence = 0.9;

    characteristics.push(`Length: ${input.length} characters`);

    if (input.length === 0) {
      characteristics.push('Empty string');
      recommendations.push('Will generate default characters during scaling');
      confidence = 0.7;
    } else {
      // Character analysis
      const uniqueChars = new Set(input).size;
      const charDiversity = uniqueChars / input.length;

      characteristics.push(`Unique characters: ${uniqueChars}`);
      characteristics.push(`Character diversity: ${(charDiversity * 100).toFixed(1)}%`);

      // Pattern detection
      const repeatingPattern = this.findRepeatingPattern(input);
      if (repeatingPattern) {
        characteristics.push(`Repeating pattern: "${repeatingPattern}"`);
        recommendations.push('Use pattern-based scaling for consistency');
        confidence += 0.05;
      }

      // Character type analysis
      const hasLetters = /[a-zA-Z]/.test(input);
      const hasNumbers = /[0-9]/.test(input);
      const hasSpecialChars = /[^a-zA-Z0-9]/.test(input);

      const charTypes = [];
      if (hasLetters) charTypes.push('letters');
      if (hasNumbers) charTypes.push('numbers');
      if (hasSpecialChars) charTypes.push('special characters');

      characteristics.push(`Contains: ${charTypes.join(', ')}`);

      // Recommend scaling strategy
      if (charDiversity < 0.1) {
        recommendations.push('Low diversity - repetition scaling recommended');
      } else if (repeatingPattern) {
        recommendations.push('Pattern detected - use pattern-based scaling');
      } else {
        recommendations.push('High diversity - use frequency-based scaling');
      }
    }

    return {
      pattern: 'string',
      confidence,
      characteristics,
      recommendations,
    };
  }

  /**
   * Detects number patterns
   */
  private static detectNumberPattern(input: any): PatternDetectionResult {
    if (typeof input !== 'number') {
      return { pattern: 'number', confidence: 0, characteristics: [], recommendations: [] };
    }

    const characteristics: string[] = [];
    const recommendations: string[] = [];
    const confidence = 0.8;

    characteristics.push(`Value: ${input}`);

    if (Number.isInteger(input)) {
      characteristics.push('Integer value');
    } else {
      characteristics.push('Floating-point value');
    }

    if (input < 0) {
      characteristics.push('Negative number');
      recommendations.push('Consider absolute value for size scaling');
    } else if (input === 0) {
      characteristics.push('Zero value');
      recommendations.push('Will use target size directly');
    } else {
      characteristics.push('Positive number');
      recommendations.push('Can be used directly as size parameter');
    }

    return {
      pattern: 'number',
      confidence,
      characteristics,
      recommendations,
    };
  }

  /**
   * Detects matrix patterns (2D arrays)
   */
  private static detectMatrixPattern(input: any): PatternDetectionResult {
    if (!Array.isArray(input) || input.length === 0) {
      return { pattern: 'matrix', confidence: 0, characteristics: [], recommendations: [] };
    }

    // Check if all elements are arrays
    const isMatrix = input.every(row => Array.isArray(row));

    if (!isMatrix) {
      return { pattern: 'matrix', confidence: 0, characteristics: [], recommendations: [] };
    }

    const characteristics: string[] = [];
    const recommendations: string[] = [];
    let confidence = 0.85;

    const rows = input.length;
    const cols = input[0]?.length || 0;

    characteristics.push(`${rows}x${cols} matrix`);

    // Check for rectangular matrix
    const isRectangular = input.every(row => row.length === cols);
    if (isRectangular) {
      characteristics.push('Rectangular matrix');
      confidence += 0.05;
    } else {
      characteristics.push('Jagged matrix (irregular row lengths)');
      recommendations.push('Consider normalizing row lengths');
      confidence -= 0.1;
    }

    // Check for square matrix
    if (rows === cols) {
      characteristics.push('Square matrix');
      recommendations.push('Can scale by adjusting dimensions proportionally');
    } else {
      characteristics.push('Non-square matrix');
      recommendations.push('Scale to approximate square for balanced growth');
    }

    // Analyze element types
    const allElements = input.flat();
    const elementTypes = new Set(allElements.map(item => typeof item));

    if (elementTypes.size === 1) {
      const elementType = Array.from(elementTypes)[0];
      characteristics.push(`Homogeneous ${elementType} matrix`);

      if (elementType === 'number') {
        characteristics.push('Numeric matrix - suitable for mathematical operations');
        recommendations.push('Use interpolation for smooth scaling');
      }
    } else {
      characteristics.push('Mixed-type matrix');
      recommendations.push('Consider element-wise scaling strategies');
    }

    return {
      pattern: 'matrix',
      confidence,
      characteristics,
      recommendations,
    };
  }

  /**
   * Detects tree patterns
   */
  private static detectTreePattern(input: any): PatternDetectionResult {
    if (typeof input !== 'object' || input === null) {
      return { pattern: 'tree', confidence: 0, characteristics: [], recommendations: [] };
    }

    const characteristics: string[] = [];
    const recommendations: string[] = [];
    let confidence = 0;

    // Check for common tree properties
    const hasValue = 'value' in input;
    const hasLeft = 'left' in input;
    const hasRight = 'right' in input;
    const hasChildren = 'children' in input;
    const hasParent = 'parent' in input;

    if (hasValue && (hasLeft || hasRight)) {
      confidence = 0.8;
      characteristics.push('Binary tree structure detected');

      if (hasLeft && hasRight) {
        characteristics.push('Has both left and right children');
      } else if (hasLeft) {
        characteristics.push('Has left child only');
      } else {
        characteristics.push('Has right child only');
      }
    } else if (hasValue && hasChildren) {
      confidence = 0.75;
      characteristics.push('N-ary tree structure detected');

      if (Array.isArray(input.children)) {
        characteristics.push(`${input.children.length} children`);
      }
    } else if (hasValue || hasChildren) {
      confidence = 0.4;
      characteristics.push('Possible tree-like structure');
    }

    if (confidence > 0.5) {
      // Analyze tree properties
      const depth = this.calculateTreeDepth(input);
      const nodeCount = this.countTreeNodes(input);

      characteristics.push(`Tree depth: ${depth}`);
      characteristics.push(`Node count: ${nodeCount}`);

      recommendations.push('Use balanced tree generation for scaling');

      if (depth > 10) {
        recommendations.push('Deep tree detected - consider limiting depth during scaling');
      }

      if (nodeCount > 1000) {
        recommendations.push('Large tree - use efficient node generation strategies');
      }
    }

    return {
      pattern: 'tree',
      confidence,
      characteristics,
      recommendations,
    };
  }

  /**
   * Detects graph patterns
   */
  private static detectGraphPattern(input: any): PatternDetectionResult {
    if (typeof input !== 'object' || input === null) {
      return { pattern: 'graph', confidence: 0, characteristics: [], recommendations: [] };
    }

    const characteristics: string[] = [];
    const recommendations: string[] = [];
    let confidence = 0;

    const hasNodes = 'nodes' in input && Array.isArray(input.nodes);
    const hasEdges = 'edges' in input && Array.isArray(input.edges);
    const hasAdjacencyList = 'adjacencyList' in input;
    const hasAdjacencyMatrix = Array.isArray(input) && input.every(row => Array.isArray(row));

    if (hasNodes && hasEdges) {
      confidence = 0.9;
      characteristics.push('Graph with nodes and edges arrays');
      characteristics.push(`${input.nodes.length} nodes, ${input.edges.length} edges`);

      // Calculate graph density
      const maxEdges = (input.nodes.length * (input.nodes.length - 1)) / 2;
      const density = input.edges.length / maxEdges;
      characteristics.push(`Graph density: ${(density * 100).toFixed(1)}%`);

      if (density < 0.1) {
        recommendations.push('Sparse graph - efficient for large-scale generation');
      } else if (density > 0.5) {
        recommendations.push('Dense graph - consider edge pruning for scaling');
      }
    } else if (hasAdjacencyList) {
      confidence = 0.8;
      characteristics.push('Graph with adjacency list representation');

      const nodeCount = Object.keys(input.adjacencyList).length;
      characteristics.push(`${nodeCount} nodes in adjacency list`);

      recommendations.push('Adjacency list format - efficient for sparse graphs');
    } else if (hasAdjacencyMatrix) {
      confidence = 0.7;
      characteristics.push('Adjacency matrix representation');
      characteristics.push(`${input.length}x${input[0]?.length || 0} matrix`);

      recommendations.push('Matrix format - suitable for dense graphs');
    }

    if (confidence > 0.5) {
      recommendations.push('Use graph generation algorithms for scaling');

      if (hasNodes && input.nodes.length > 100) {
        recommendations.push('Large graph - use efficient scaling strategies');
      }
    }

    return {
      pattern: 'graph',
      confidence,
      characteristics,
      recommendations,
    };
  }

  /**
   * Detects general object patterns
   */
  private static detectObjectPattern(input: any): PatternDetectionResult {
    if (typeof input !== 'object' || input === null || Array.isArray(input)) {
      return { pattern: 'object', confidence: 0, characteristics: [], recommendations: [] };
    }

    const characteristics: string[] = [];
    const recommendations: string[] = [];
    let confidence = 0.6; // Default confidence for objects

    const keys = Object.keys(input);
    const values = Object.values(input);

    characteristics.push(`${keys.length} properties`);

    if (keys.length === 0) {
      characteristics.push('Empty object');
      recommendations.push('Will generate properties during scaling');
      confidence = 0.4;
    } else {
      // Analyze key patterns
      const keyTypes = new Set(keys.map(key => typeof key));
      if (keyTypes.size === 1 && keyTypes.has('string')) {
        characteristics.push('String keys');

        // Check for numeric-like keys
        const numericKeys = keys.filter(key => /^\d+$/.test(key));
        if (numericKeys.length === keys.length) {
          characteristics.push('Numeric string keys (array-like)');
          recommendations.push('Consider array representation');
        }
      }

      // Analyze value types
      const valueTypes = new Set(values.map(value => typeof value));
      characteristics.push(`Value types: ${Array.from(valueTypes).join(', ')}`);

      if (valueTypes.size === 1) {
        const valueType = Array.from(valueTypes)[0];
        characteristics.push(`Homogeneous ${valueType} values`);
        confidence += 0.1;
      } else {
        characteristics.push('Mixed-type values');
      }

      // Check for nested objects
      const nestedObjects = values.filter(value =>
        typeof value === 'object' && value !== null && !Array.isArray(value)
      );

      if (nestedObjects.length > 0) {
        characteristics.push(`${nestedObjects.length} nested objects`);
        recommendations.push('Use nested scaling strategy');
        confidence += 0.05;
      }

      // Check nesting depth
      const maxDepth = this.calculateObjectDepth(input);
      if (maxDepth > 1) {
        characteristics.push(`Maximum nesting depth: ${maxDepth}`);

        if (maxDepth > 5) {
          recommendations.push('Deep nesting detected - consider flattening for scaling');
        }
      }
    }

    return {
      pattern: 'object',
      confidence,
      characteristics,
      recommendations,
    };
  }

  // Helper methods

  private static analyzeNumericArray(arr: number[]): { characteristics: string[]; recommendations: string[] } {
    const characteristics: string[] = [];
    const recommendations: string[] = [];

    const min = Math.min(...arr);
    const max = Math.max(...arr);
    const range = max - min;

    characteristics.push(`Range: ${min} to ${max}`);

    // Check for arithmetic progression
    if (arr.length >= 3) {
      const diffs = [];
      for (let i = 1; i < arr.length; i++) {
        diffs.push(arr[i] - arr[i - 1]);
      }

      const avgDiff = diffs.reduce((sum, diff) => sum + diff, 0) / diffs.length;
      const diffVariance = diffs.reduce((sum, diff) => sum + Math.pow(diff - avgDiff, 2), 0) / diffs.length;

      if (diffVariance < 0.01) {
        characteristics.push(`Arithmetic progression (step: ${avgDiff.toFixed(2)})`);
        recommendations.push('Use arithmetic sequence generation');
      }
    }

    return { characteristics, recommendations };
  }

  private static isNumericArray(arr: any[]): boolean {
    return arr.every(item => typeof item === 'number');
  }

  private static isSorted(arr: number[], ascending: boolean = true): boolean {
    for (let i = 1; i < arr.length; i++) {
      if (ascending ? arr[i] < arr[i - 1] : arr[i] > arr[i - 1]) {
        return false;
      }
    }
    return true;
  }

  private static findRepeatingPattern(str: string): string | null {
    for (let len = 1; len <= str.length / 2; len++) {
      const pattern = str.substring(0, len);
      const repeated = pattern.repeat(Math.floor(str.length / len));

      if (str.startsWith(repeated) && repeated.length >= str.length * 0.8) {
        return pattern;
      }
    }
    return null;
  }

  private static calculateTreeDepth(node: any): number {
    if (!node || typeof node !== 'object') return 0;

    let maxDepth = 0;

    if (node.left) {
      maxDepth = Math.max(maxDepth, this.calculateTreeDepth(node.left));
    }

    if (node.right) {
      maxDepth = Math.max(maxDepth, this.calculateTreeDepth(node.right));
    }

    if (node.children && Array.isArray(node.children)) {
      for (const child of node.children) {
        maxDepth = Math.max(maxDepth, this.calculateTreeDepth(child));
      }
    }

    return maxDepth + 1;
  }

  private static countTreeNodes(node: any): number {
    if (!node || typeof node !== 'object') return 0;

    let count = 1;

    if (node.left) {
      count += this.countTreeNodes(node.left);
    }

    if (node.right) {
      count += this.countTreeNodes(node.right);
    }

    if (node.children && Array.isArray(node.children)) {
      for (const child of node.children) {
        count += this.countTreeNodes(child);
      }
    }

    return count;
  }

  private static calculateObjectDepth(obj: any): number {
    if (typeof obj !== 'object' || obj === null) return 0;

    let maxDepth = 0;

    for (const value of Object.values(obj)) {
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        maxDepth = Math.max(maxDepth, this.calculateObjectDepth(value));
      }
    }

    return maxDepth + 1;
  }
}

/**
 * Pattern-based input generator that uses detection results
 */
export class SmartInputGenerator {
  /**
   * Generates inputs based on automatic pattern detection
   */
  static generateSmartInputs(baseInput: any, sizes: number[]): {
    inputs: any[];
    pattern: PatternDetectionResult;
    scalingStrategy: string;
  } {
    const pattern = PatternDetector.analyzeInput(baseInput);
    const scalingStrategy = SmartInputGenerator.selectScalingStrategy(pattern);

    const inputs = sizes.map(size => {
      try {
        return SmartInputGenerator.scaleWithStrategy(baseInput, size, pattern.pattern, scalingStrategy);
      } catch (error) {
        console.warn(`Failed to scale input to size ${size}:`, error);
        return SmartInputGenerator.fallbackScale(baseInput, size);
      }
    });

    return {
      inputs,
      pattern,
      scalingStrategy,
    };
  }

  public static selectScalingStrategy(pattern: PatternDetectionResult): string {
    if (pattern.confidence > 0.8) {
      // High confidence - use specialized strategy
      switch (pattern.pattern) {
        case 'array':
          return pattern.characteristics.includes('Arithmetic progression') ? 'arithmetic' : 'interpolation';
        case 'string':
          return pattern.characteristics.includes('Repeating pattern') ? 'pattern' : 'frequency';
        case 'matrix':
          return 'proportional';
        case 'tree':
          return 'balanced';
        case 'graph':
          return 'density-preserving';
        default:
          return 'adaptive';
      }
    } else {
      // Low confidence - use safe fallback
      return 'linear';
    }
  }

  public static scaleWithStrategy(input: any, size: number, pattern: InputPattern, strategy: string): any {
    // Simple scaling implementation to avoid circular dependencies
    return SmartInputGenerator.fallbackScale(input, size);
  }

  public static fallbackScale(input: any, size: number): any {
    if (Array.isArray(input)) {
      return new Array(size).fill(input[0] || 0);
    } else if (typeof input === 'string') {
      return 'a'.repeat(size);
    } else if (typeof input === 'number') {
      return size;
    } else {
      return new Array(size).fill(input);
    }
  }
}

/**
 * Export pattern detection utilities
 */
export const patternDetection = {
  analyze: PatternDetector.analyzeInput,
  generateSmart: SmartInputGenerator.generateSmartInputs,
};

// SmartInputGenerator is already exported above with export class
