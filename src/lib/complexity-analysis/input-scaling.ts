/**
 * Advanced input scaling algorithms for complexity analysis
 * Handles sophisticated scaling strategies for different data structures
 */

import { InputPattern } from './types';

/**
 * Scaling strategy interface
 */
interface ScalingStrategy {
  scale(input: any, targetSize: number): any;
  validate(input: any): boolean;
  estimateSize(input: any): number;
}

/**
 * Array scaling strategies
 */
export class ArrayScalingStrategies {
  /**
   * Linear scaling - maintains element distribution
   */
  static linear: ScalingStrategy = {
    scale(input: any[], targetSize: number): any[] {
      if (!Array.isArray(input) || input.length === 0) {
        return new Array(targetSize).fill(0);
      }

      if (targetSize <= input.length) {
        return input.slice(0, targetSize);
      }

      const result: any[] = [];
      const ratio = targetSize / input.length;

      for (let i = 0; i < input.length; i++) {
        const count = Math.round(ratio);
        for (let j = 0; j < count && result.length < targetSize; j++) {
          result.push(input[i]);
        }
      }

      // Fill remaining slots if needed
      while (result.length < targetSize) {
        result.push(input[result.length % input.length]);
      }

      return result;
    },

    validate(input: any): boolean {
      return Array.isArray(input);
    },

    estimateSize(input: any[]): number {
      return input.length;
    },
  };

  /**
   * Interpolation scaling - creates smooth transitions
   */
  static interpolation: ScalingStrategy = {
    scale(input: any[], targetSize: number): any[] {
      if (!Array.isArray(input) || input.length === 0) {
        return new Array(targetSize).fill(0);
      }

      if (input.length === 1) {
        return new Array(targetSize).fill(input[0]);
      }

      const result: any[] = [];
      const step = (input.length - 1) / (targetSize - 1);

      for (let i = 0; i < targetSize; i++) {
        const index = i * step;
        const lowerIndex = Math.floor(index);
        const upperIndex = Math.ceil(index);

        if (lowerIndex === upperIndex) {
          result.push(input[lowerIndex]);
        } else {
          // Linear interpolation for numbers
          const lowerValue = input[lowerIndex];
          const upperValue = input[upperIndex];

          if (typeof lowerValue === 'number' && typeof upperValue === 'number') {
            const fraction = index - lowerIndex;
            result.push(lowerValue + (upperValue - lowerValue) * fraction);
          } else {
            result.push(index - lowerIndex < 0.5 ? lowerValue : upperValue);
          }
        }
      }

      return result;
    },

    validate(input: any): boolean {
      return Array.isArray(input);
    },

    estimateSize(input: any[]): number {
      return input.length;
    },
  };

  /**
   * Pattern-based scaling - maintains patterns in data
   */
  static pattern: ScalingStrategy = {
    scale(input: any[], targetSize: number): any[] {
      if (!Array.isArray(input) || input.length === 0) {
        return new Array(targetSize).fill(0);
      }

      // Detect pattern in the input
      const pattern = this.detectPattern(input);

      if (pattern.type === 'arithmetic') {
        return this.generateArithmeticSequence(targetSize, pattern.start, pattern.step);
      } else if (pattern.type === 'geometric') {
        return this.generateGeometricSequence(targetSize, pattern.start, pattern.ratio);
      } else {
        // Fallback to repetition
        const result: any[] = [];
        for (let i = 0; i < targetSize; i++) {
          result.push(input[i % input.length]);
        }
        return result;
      }
    },

    validate(input: any): boolean {
      return Array.isArray(input);
    },

    estimateSize(input: any[]): number {
      return input.length;
    },

    detectPattern(input: any[]): { type: string; start?: number; step?: number; ratio?: number } {
      if (input.length < 3 || !input.every(x => typeof x === 'number')) {
        return { type: 'none' };
      }

      // Check for arithmetic progression
      const diff1 = input[1] - input[0];
      const diff2 = input[2] - input[1];

      if (Math.abs(diff1 - diff2) < 0.001) {
        return { type: 'arithmetic', start: input[0], step: diff1 };
      }

      // Check for geometric progression
      if (input[0] !== 0 && input[1] !== 0) {
        const ratio1 = input[1] / input[0];
        const ratio2 = input[2] / input[1];

        if (Math.abs(ratio1 - ratio2) < 0.001) {
          return { type: 'geometric', start: input[0], ratio: rat};
        }
      }

      return { type: 'none' };
    },

    generateArithmeticSequence(size: number, start: number, step: number): number[] {
      return Array.from({ length: size }, (_, i) => start + i * step);
    },

    generateGeometricSequence(size: number, start: number, ratio: number): number[] {
      return Array.from({ length: size }, (_, i) => start * Math.pow(ratio, i));
    },
  };
}

/**
 * String scaling strategies
 */
export class StringScalingStrategies {
  /**
   * Character repetition scaling
   */
  static repetition: ScalingStrategy = {
    scale(input: string, targetSize: number): string {
      if (typeof input !== 'string' || input.length === 0) {
        return 'a'.repeat(targetSize);
      }

      if (targetSize <= input.length) {
        return input.substring(0, targetSize);
      }

      return input.repeat(Math.ceil(targetSize / input.length)).substring(0, targetSize);
    },

    validate(input: any): boolean {
      return typeof input === 'string';
    },

    estimateSize(input: string): number {
      return input.length;
    },
  };

  /**
   * Pattern-based string scaling
   */
  static pattern: ScalingStrategy = {
    scale(input: string, targetSize: number): string {
      if (typeof input !== 'string' || input.length === 0) {
        return 'a'.repeat(targetSize);
      }

      // Detect repeating patterns
      const pattern = this.findRepeatingPattern(input);

      if (pattern) {
        return pattern.repeat(Math.ceil(targetSize / pattern.length)).substring(0, targetSize);
      }

      // Fallback to character frequency-based generation
      return this.generateByFrequency(input, targetSize);
    },

    validate(input: any): boolean {
      return typeof input === 'string';
    },

    estimateSize(input: string): number {
      return input.length;
    },

    findRepeatingPattern(str: string): string | null {
      for (let len = 1; len <= str.length / 2; len++) {
        const pattern = str.substring(0, len);
        const repeated = pattern.repeat(Math.floor(str.length / len));

        if (str.startsWith(repeated)) {
          return pattern;
        }
      }
      return null;
    },

    generateByFrequency(str: string, targetSize: number): string {
      // Calculate character frequencies
      const freq: { [char: string]: number } = {};
      for (const char of str) {
        freq[char] = (freq[char] || 0) + 1;
      }

      // Create weighted character array
      const chars: string[] = [];
      for (const [char, count] of Object.entries(freq)) {
        for (let i = 0; i < count; i++) {
          chars.push(char);
        }
      }

      // Generate string with similar character distribution
      let result = '';
      for (let i = 0; i < targetSize; i++) {
        result += chars[Math.floor(Math.random() * chars.length)];
      }

      return result;
    },
  };
}

/**
 * Object scaling strategies
 */
export class ObjectScalingStrategies {
  /**
   * Property multiplication scaling
   */
  static multiplication: ScalingStrategy = {
    scale(input: any, targetSize: number): any {
      if (typeof input !== 'object' || input === null) {
        const result: any = {};
        for (let i = 0; i < targetSize; i++) {
          result[`key_${i}`] = i;
        }
        return result;
      }

      const keys = Object.keys(input);
      if (keys.length === 0) {
        const result: any = {};
        for (let i = 0; i < targetSize; i++) {
          result[`key_${i}`] = i;
        }
        return result;
      }

      const result: any = {};
      const keysPerOriginal = Math.ceil(targetSize / keys.length);

      for (let i = 0; i < targetSize; i++) {
        const originalKey = keys[i % keys.length];
        const suffix = Math.floor(i / keys.length);
        const newKey = suffix === 0 ? originalKey : `${originalKey}_${suffix}`;

        result[newKey] = input[originalKey];
      }

      return result;
    },

    validate(input: any): boolean {
      return typeof input === 'object' && input !== null;
    },

    estimateSize(input: any): number {
      return Object.keys(input).length;
    },
  };

  /**
   * Nested structure scaling
   */
  static nested: ScalingStrategy = {
    scale(input: any, targetSize: number): any {
      if (typeof input !== 'object' || input === null) {
        return this.createNestedStructure(targetSize);
      }

      return this.scaleNestedStructure(input, targetSize);
    },

    validate(input: any): boolean {
      return typeof input === 'object' && input !== null;
    },

    estimateSize(input: any): number {
      return this.countNestedProperties(input);
    },

    createNestedStructure(size: number): any {
      const result: any = {};
      let remaining = size;
      let level = 0;

      while (remaining > 0 && level < 10) { // Prevent infinite nesting
        const keysAtLevel = Math.min(remaining, Math.ceil(Math.sqrt(remaining)));

        for (let i = 0; i < keysAtLevel && remaining > 0; i++) {
          const key = `level_${level}_key_${i}`;

          if (remaining > 1 && Math.random() > 0.5) {
            result[key] = this.createNestedStructure(Math.min(remaining - 1, 3));
            remaining -= this.countNestedProperties(result[key]);
          } else {
            result[key] = `value_${level}_${i}`;
            remaining--;
          }
        }

        level++;
      }

      return result;
    },

    scaleNestedStructure(input: any, targetSize: number): any {
      const currentSize = this.countNestedProperties(input);

      if (currentSize >= targetSize) {
        return this.truncateNestedStructure(input, targetSize);
      } else {
        return this.expandNestedStructure(input, targetSize);
      }
    },

    countNestedProperties(obj: any): number {
      if (typeof obj !== 'object' || obj === null) {
        return 1;
      }

      let count = 0;
      for (const value of Object.values(obj)) {
        count += this.countNestedProperties(value);
      }

      return count;
    },

    truncateNestedStructure(obj: any, maxSize: number): any {
      if (maxSize <= 0) return {};

      const result: any = {};
      let remaining = maxSize;

      for (const [key, value] of Object.entries(obj)) {
        if (remaining <= 0) break;

        if (typeof value === 'object' && value !== null) {
          const valueSize = this.countNestedProperties(value);
          if (valueSize <= remaining) {
            result[key] = value;
            remaining -= valueSize;
          } else {
            result[key] = this.truncateNestedStructure(value, remaining);
            remaining = 0;
          }
        } else {
          result[key] = value;
          remaining--;
        }
      }

      return result;
    },

    expandNestedStructure(obj: any, targetSize: number): any {
      const currentSize = this.countNestedProperties(obj);
      const additionalSize = targetSize - currentSize;

      const result = { ...obj };

      // Add additional properties
      for (let i = 0; i < additionalSize; i++) {
        result[`expanded_${i}`] = `value_${i}`;
      }

      return result;
    },
  };
}

/**
 * Graph scaling strategies
 */
export class GraphScalingStrategies {
  /**
   * Node multiplication with edge preservation
   */
  static nodeMultiplication: ScalingStrategy = {
    scale(input: any, targetSize: number): any {
      if (!this.validate(input)) {
        return this.createRandomGraph(targetSize);
      }

      const currentNodes = input.nodes?.length || 0;

      if (currentNodes >= targetSize) {
        return this.truncateGraph(input, targetSize);
      } else {
        return this.expandGraph(input, targetSize);
      }
    },

    validate(input: any): boolean {
      return (
        typeof input === 'object' &&
        input !== null &&
        Array.isArray(input.nodes)
      );
    },

    estimateSize(input: any): number {
      return input.nodes?.length || 0;
    },

    createRandomGraph(nodeCount: number): any {
      const nodes = Array.from({ length: nodeCount }, (_, i) => ({
        id: i,
        value: i,
      }));

      const edges = [];
      const edgeDensity = Math.min(0.3, 10 / nodeCount); // Adjust density based on size

      for (let i = 0; i < nodeCount; i++) {
        for (let j = i + 1; j < nodeCount; j++) {
          if (Math.random() < edgeDensity) {
            edges.push({
              from: i,
              to: j,
              weight: Math.floor(Math.random() * 10) + 1,
            });
          }
        }
      }

      return {
        nodes,
        edges,
        adjacencyList: this.createAdjacencyList(nodes, edges),
      };
    },

    truncateGraph(graph: any, targetSize: number): any {
      const nodes = graph.nodes.slice(0, targetSize);
      const nodeIds = new Set(nodes.map((n: any) => n.id));

      const edges = graph.edges.filter((e: any) =>
        nodeIds.has(e.from) && nodeIds.has(e.to)
      );

      return {
        nodes,
        edges,
        adjacencyList: this.createAdjacencyList(nodes, edges),
      };
    },

    expandGraph(graph: any, targetSize: number): any {
      const existingNodes = [...graph.nodes];
      const existingEdges = [...graph.edges];

      // Add new nodes
      for (let i = existingNodes.length; i < targetSize; i++) {
        existingNodes.push({
          id: i,
          value: i,
        });
      }

      // Add edges to connect new nodes
      const newNodeStart = graph.nodes.length;
      for (let i = newNodeStart; i < targetSize; i++) {
        // Connect each new node to a few existing nodes
        const connectionsCount = Math.min(3, i);
        for (let j = 0; j < connectionsCount; j++) {
          const targetNode = Math.floor(Math.random() * i);
          existingEdges.push({
            from: i,
            to: targetNode,
            weight: Math.floor(Math.random() * 10) + 1,
          });
        }
      }

      return {
        nodes: existingNodes,
        edges: existingEdges,
        adjacencyList: this.createAdjacencyList(existingNodes, existingEdges),
      };
    },

    createAdjacencyList(nodes: any[], edges: any[]): { [key: number]: number[] } {
      const adjacencyList: { [key: number]: number[] } = {};

      nodes.forEach(node => {
        adjacencyList[node.id] = [];
      });

      edges.forEach(edge => {
        adjacencyList[edge.from].push(edge.to);
        adjacencyList[edge.to].push(edge.from); // Undirected graph
      });

      return adjacencyList;
    },
  };
}

/**
 * Adaptive scaling system that chooses the best strategy
 */
export class AdaptiveScaler {
  private strategies: Map<InputPattern, ScalingStrategy[]> = new Map([
    ['array', [
      ArrayScalingStrategies.pattern,
      ArrayScalingStrategies.interpolation,
      ArrayScalingStrategies.linear,
    ]],
    ['string', [
      StringScalingStrategies.pattern,
      StringScalingStrategies.repetition,
    ]],
    ['object', [
      ObjectScalingStrategies.nested,
      ObjectScalingStrategies.multiplication,
    ]],
    ['graph', [
      GraphScalingStrategies.nodeMultiplication,
    ]],
  ]);

  /**
   * Scales input using the most appropriate strategy
   */
  scale(input: any, targetSize: number, pattern: InputPattern): any {
    const strategies = this.strategies.get(pattern) || [];

    for (const strategy of strategies) {
      if (strategy.validate(input)) {
        try {
          return strategy.scale(input, targetSize);
        } catch (error) {
          console.warn(`Scaling strategy failed:`, error);
          continue;
        }
      }
    }

    // Fallback to basic scaling
    return this.basicScale(input, targetSize, pattern);
  }

  /**
   * Basic fallback scaling
   */
  private basicScale(input: any, targetSize: number, pattern: InputPattern): any {
    switch (pattern) {
      case 'array':
        return Array.isArray(input)
          ? ArrayScalingStrategies.linear.scale(input, targetSize)
          : new Array(targetSize).fill(0);

      case 'string':
        return typeof input === 'string'
          ? StringScalingStrategies.repetition.scale(input, targetSize)
          : 'a'.repeat(targetSize);

      case 'number':
        return targetSize;

      default:
        return new Array(targetSize).fill(input);
    }
  }
}

/**
 * Export the adaptive scaler as the default scaling system
 */
export const defaultScaler = new AdaptiveScaler();
