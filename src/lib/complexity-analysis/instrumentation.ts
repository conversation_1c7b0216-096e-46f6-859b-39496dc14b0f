/**
 * Instrumentation hooks for integrating complexity analysis with existing code execution
 */

import { PerformanceMeasurement, AnalysisOptions } from './types';
import { TimingUtils, PrecisionTimer } from './timing';
import { MemoryUtils, MemoryTracker } from './memory';

/**
 * Execution context for instrumented code execution
 */
interface InstrumentedExecutionContext {
  timer: PrecisionTimer;
  memoryTracker: MemoryTracker;
  inputSize: number;
  iterations: number;
  startTime: number;
  startMemory: number;
}

/**
 * Result of instrumented execution
 */
interface InstrumentedExecutionResult {
  output: string;
  error?: string;
  executionTime: number;
  memoryUsage: number;
  peakMemory: number;
  measurement: PerformanceMeasurement;
}

/**
 * Instrumentation wrapper for the existing BrowserCodeExecutor
 */
export class ExecutionInstrumentation {
  private static contexts: Map<string, InstrumentedExecutionContext> = new Map();

  /**
   * Wraps the existing BrowserCodeExecutor with performance instrumentation
   */
  static async instrumentExecution(
    executorMethod: () => Promise<{ output: string; error?: string; executionTime: number }>,
    inputSize: number,
    iterations: number = 1
  ): Promise<InstrumentedExecutionResult> {
    const contextId = this.generateContextId();
    const context = this.createExecutionContext(inputSize, iterations);
    this.contexts.set(contextId, context);

    try {
      // Start instrumentation
      this.startInstrumentation(context);

      // Execute the original method
      const result = await executorMethod();

      // Stop instrumentation and collect metrics
      const metrics = this.stopInstrumentation(context);

      // Create performance measurement
      const measurement: PerformanceMeasurement = {
        inputSize,
        executionTime: metrics.executionTime,
        memoryUsage: metrics.memoryUsage,
        peakMemory: metrics.peakMemory,
        iterations,
        timestamp: new Date(),
      };

      return {
        output: result.output,
        error: result.error,
        executionTime: metrics.executionTime,
        memoryUsage: metrics.memoryUsage,
        peakMemory: metrics.peakMemory,
        measurement,
      };
    } finally {
      this.contexts.delete(contextId);
    }
  }

  /**
   * Creates enhanced versions of BrowserCodeExecutor methods with instrumentation
   */
  static createInstrumentedExecutor() {
    return {
      executeJavaScript: async (code: string, inputSize: number = 1, iterations: number = 1) => {
        const { BrowserCodeExecutor } = await import('../code-execution/browser-executor');

        return this.instrumentExecution(
          () => BrowserCodeExecutor.executeJavaScript(code),
          inputSize,
          iterations
        );
      },

      executeTypeScript: async (code: string, inputSize: number = 1, iterations: number = 1) => {
        const { BrowserCodeExecutor } = await import('../code-execution/browser-executor');

        return this.instrumentExecution(
          () => BrowserCodeExecutor.executeTypeScript(code),
          inputSize,
          iterations
        );
      },

      executePython: async (code: string, inputSize: number = 1, iterations: number = 1, userInputs: string[] = []) => {
        const { BrowserCodeExecutor } = await import('../code-execution/browser-executor');

        return this.instrumentExecution(
          () => BrowserCodeExecutor.executePython(code, userInputs),
          inputSize,
          iterations
        );
      },
    };
  }

  /**
   * Instruments code execution with input injection
   */
  static async instrumentCodeWithInput(
    code: string,
    language: string,
    input: any,
    options: AnalysisOptions
  ): Promise<InstrumentedExecutionResult> {
    const inputSize = this.calculateInputSize(input);
    const codeWithInput = this.injectInputIntoCode(code, input, language);

    const instrumentedExecutor = this.createInstrumentedExecutor();

    switch (language.toLowerCase()) {
      case 'javascript':
        return instrumentedExecutor.executeJavaScript(codeWithInput, inputSize, options.iterations);

      case 'typescript':
        return instrumentedExecutor.executeTypeScript(codeWithInput, inputSize, options.iterations);

      case 'python':
        return instrumentedExecutor.executePython(codeWithInput, inputSize, options.iterations);

      default:
        throw new Error(`Unsupported language: ${language}`);
    }
  }

  /**
   * Creates execution context for instrumentation
   */
  private static createExecutionContext(inputSize: number, iterations: number): InstrumentedExecutionContext {
    return {
      timer: new PrecisionTimer(),
      memoryTracker: new MemoryTracker(),
      inputSize,
      iterations,
      startTime: 0,
      startMemory: 0,
    };
  }

  /**
   * Starts instrumentation for an execution context
   */
  private static startInstrumentation(context: InstrumentedExecutionContext): void {
    context.startTime = performance.now();
    context.timer.start();
    context.memoryTracker.startTracking();
  }

  /**
   * Stops instrumentation and returns metrics
   */
  private static stopInstrumentation(context: InstrumentedExecutionContext): {
    executionTime: number;
    memoryUsage: number;
    peakMemory: number;
  } {
    const executionTime = context.timer.stop();
    const memoryStats = context.memoryTracker.stopTracking();

    return {
      executionTime,
      memoryUsage: memoryStats.totalAllocated,
      peakMemory: memoryStats.peakMemory,
    };
  }

  /**
   * Injects input data into code for execution
   */
  private static injectInputIntoCode(code: string, input: any, language: string): string {
    const inputJson = JSON.stringify(input);

    switch (language.toLowerCase()) {
      case 'javascript':
      case 'typescript':
        return `
// Complexity analysis input injection
const __complexity_input = ${inputJson};
const input = __complexity_input;

${code}
        `;

      case 'python':
        return `
# Complexity analysis input injection
import json
__complexity_input = json.loads('${inputJson.replace(/'/g, "\\'")}')
input = __complexity_input

${code}
        `;

      default:
        return code;
    }
  }

  /**
   * Calculates input size for different data types
   */
  private static calculateInputSize(input: any): number {
    if (Array.isArray(input)) {
      return input.length;
    }

    if (typeof input === 'string') {
      return input.length;
    }

    if (typeof input === 'number') {
      return Math.abs(input);
    }

    if (typeof input === 'object' && input !== null) {
      return Object.keys(input).length;
    }

    return 1;
  }

  /**
   * Generates unique context ID
   */
  private static generateContextId(): string {
    return `ctx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * Hooks for integrating with existing code editor execution flow
 */
export class CodeEditorHooks {
  private static isInstrumentationEnabled: boolean = false;
  private static currentAnalysisOptions: AnalysisOptions | null = null;

  /**
   * Enables complexity analysis instrumentation
   */
  static enableInstrumentation(options: AnalysisOptions): void {
    this.isInstrumentationEnabled = true;
    this.currentAnalysisOptions = options;
  }

  /**
   * Disables complexity analysis instrumentation
   */
  static disableInstrumentation(): void {
    this.isInstrumentationEnabled = false;
    this.currentAnalysisOptions = null;
  }

  /**
   * Checks if instrumentation is enabled
   */
  static isEnabled(): boolean {
    return this.isInstrumentationEnabled;
  }

  /**
   * Gets current analysis options
   */
  static getAnalysisOptions(): AnalysisOptions | null {
    return this.currentAnalysisOptions;
  }

  /**
   * Hook that can be called before code execution in the editor
   */
  static async beforeExecution(
    code: string,
    language: string,
    input?: any
  ): Promise<{
    shouldInstrument: boolean;
    instrumentedCode?: string;
    context?: any;
  }> {
    if (!this.isInstrumentationEnabled || !this.currentAnalysisOptions) {
      return { shouldInstrument: false };
    }

    if (input) {
      const instrumentedCode = ExecutionInstrumentation['injectInputIntoCode'](code, input, language);
      return {
        shouldInstrument: true,
        instrumentedCode,
        context: {
          inputSize: ExecutionInstrumentation['calculateInputSize'](input),
          options: this.currentAnalysisOptions,
        },
      };
    }

    return { shouldInstrument: false };
  }

  /**
   * Hook that can be called after code execution in the editor
   */
  static async afterExecution(
    result: { output: string; error?: string; executionTime: number },
    context?: any
  ): Promise<{
    enhancedResult: InstrumentedExecutionResult | null;
    measurement: PerformanceMeasurement | null;
  }> {
    if (!this.isInstrumentationEnabled || !context) {
      return {
        enhancedResult: null,
        measurement: null,
      };
    }

    // Create measurement from execution result
    const measurement: PerformanceMeasurement = {
      inputSize: context.inputSize || 1,
      executionTime: result.executionTime,
      memoryUsage: 0, // Would need to be tracked during execution
      peakMemory: 0,
      iterations: 1,
      timestamp: new Date(),
    };

    const enhancedResult: InstrumentedExecutionResult = {
      output: result.output,
      error: result.error,
      executionTime: result.executionTime,
      memoryUsage: 0,
      peakMemory: 0,
      measurement,
    };

    return {
      enhancedResult,
      measurement,
    };
  }
}

/**
 * Utility for batch instrumentation of multiple executions
 */
export class BatchInstrumentation {
  /**
   * Instruments multiple code executions with different inputs
   */
  static async instrumentBatch(
    code: string,
    language: string,
    inputs: any[],
    options: AnalysisOptions
  ): Promise<InstrumentedExecutionResult[]> {
    const results: InstrumentedExecutionResult[] = [];

    for (const input of inputs) {
      try {
        const result = await ExecutionInstrumentation.instrumentCodeWithInput(
          code,
          language,
          input,
          options
        );
        results.push(result);
      } catch (error) {
        console.warn(`Batch execution failed for input:`, input, error);

        // Create error result
        const errorResult: InstrumentedExecutionResult = {
          output: '',
          error: error instanceof Error ? error.message : 'Unknown error',
          executionTime: 0,
          memoryUsage: 0,
          peakMemory: 0,
          measurement: {
            inputSize: ExecutionInstrumentation['calculateInputSize'](input),
            executionTime: 0,
            memoryUsage: 0,
            peakMemory: 0,
            iterations: 1,
            timestamp: new Date(),
          },
        };

        results.push(errorResult);
      }
    }

    return results;
  }

  /**
   * Instruments executions with timeout protection
   */
  static async instrumentWithTimeout(
    code: string,
    language: string,
    input: any,
    options: AnalysisOptions,
    timeoutMs: number
  ): Promise<InstrumentedExecutionResult> {
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error(`Execution timed out after ${timeoutMs}ms`)), timeoutMs);
    });

    const executionPromise = ExecutionInstrumentation.instrumentCodeWithInput(
      code,
      language,
      input,
      options
    );

    try {
      return await Promise.race([executionPromise, timeoutPromise]);
    } catch (error) {
      // Create timeout result
      return {
        output: '',
        error: error instanceof Error ? error.message : 'Execution timed out',
        executionTime: timeoutMs,
        memoryUsage: 0,
        peakMemory: 0,
        measurement: {
          inputSize: ExecutionInstrumentation['calculateInputSize'](input),
          executionTime: timeoutMs,
          memoryUsage: 0,
          peakMemory: 0,
          iterations: 1,
          timestamp: new Date(),
        },
      };
    }
  }
}

/**
 * Global instrumentation state
 */
export const globalInstrumentation = {
  hooks: CodeEditorHooks,
  execution: ExecutionInstrumentation,
  batch: BatchInstrumentation,
};
