/**
 * Memory usage tracking utilities for complexity analysis
 */

import { MemoryMeasurement } from './types';

/**
 * Memory tracking interface for different environments
 */
interface MemoryInfo {
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
}

/**
 * Memory tracker for browser-based code execution
 */
export class MemoryTracker {
  private isTracking: boolean = false;
  private startMemory: number = 0;
  private peakMemory: number = 0;
  private currentMemory: number = 0;
  private samples: number[] = [];
  private trackingInterval: number | null = null;
  private sampleInterval: number = 10; // Sample every 10ms

  /**
   * Starts memory tracking
   */
  startTracking(): void {
    this.isTracking = true;
    this.startMemory = this.getCurrentMemoryUsage();
    this.peakMemory = this.startMemory;
    this.currentMemory = this.startMemory;
    this.samples = [this.startMemory];

    // Start periodic sampling
    this.trackingInterval = window.setInterval(() => {
      if (this.isTracking) {
        this.sampleMemoryUsage();
      }
    }, this.sampleInterval);
  }

  /**
   * Stops memory tracking and returns statistics
   */
  stopTracking(): {
    startMemory: number;
    endMemory: number;
    peakMemory: number;
    totalAllocated: number;
    samples: number[];
    averageUsage: number;
  } {
    this.isTracking = false;

    if (this.trackingInterval) {
      clearInterval(this.trackingInterval);
      this.trackingInterval = null;
    }

    const endMemory = this.getCurrentMemoryUsage();
    const totalAllocated = Math.max(0, this.peakMemory - this.startMemory);
    const averageUsage = this.samples.reduce((sum, sample) => sum + sample, 0) / this.samples.length;

    return {
      startMemory: this.startMemory,
      endMemory,
      peakMemory: this.peakMemory,
      totalAllocated,
      samples: [...this.samples],
      averageUsage,
    };
  }

  /**
   * Samples current memory usage
   */
  private sampleMemoryUsage(): void {
    this.currentMemory = this.getCurrentMemoryUsage();
    this.peakMemory = Math.max(this.peakMemory, this.currentMemory);
    this.samples.push(this.currentMemory);
  }

  /**
   * Gets current memory usage from browser APIs
   */
  private getCurrentMemoryUsage(): number {
    // Try Performance API memory info (Chrome/Edge)
    if ('memory' in performance) {
      const memInfo = (performance as any).memory as MemoryInfo;
      return memInfo.usedJSHeapSize || 0;
    }

    // Fallback: estimate based on available information
    return this.estimateMemoryUsage();
  }

  /**
   * Estimates memory usage when direct measurement isn't available
   */
  private estimateMemoryUsage(): number {
    // Very rough estimation based on time and random factors
    // This is a fallback when proper memory APIs aren't available
    const baseMemory = 1024 * 1024; // 1MB base
    const timeVariation = (Date.now() % 10000) * 100; // Time-based variation
    return baseMemory + timeVariation;
  }

  /**
   * Checks if memory tracking is supported
   */
  static isMemoryTrackingSupported(): boolean {
    return 'memory' in performance;
  }

  /**
   * Gets memory information if available
   */
  static getMemoryInfo(): MemoryInfo | null {
    if ('memory' in performance) {
      return (performance as any).memory as MemoryInfo;
    }
    return null;
  }
}

/**
 * Memory measurement utilities
 */
export class MemoryUtils {
  /**
   * Measures memory usage during function execution
   */
  static async measureMemoryUsage<T>(
    fn: () => Promise<T>
  ): Promise<{
    result: T;
    memoryStats: {
      startMemory: number;
      endMemory: number;
      peakMemory: number;
      totalAllocated: number;
      averageUsage: number;
    };
  }> {
    const tracker = new MemoryTracker();

    tracker.startTracking();

    try {
      const result = await fn();
      const memoryStats = tracker.stopTracking();

      return {
        result,
        memoryStats,
      };
    } catch (error) {
      tracker.stopTracking();
      throw error;
    }
  }

  /**
   * Estimates object size in memory
   */
  static estimateObjectSize(obj: any): number {
    const seen = new WeakSet();

    function calculateSize(obj: any): number {
      if (obj === null || obj === undefined) return 0;

      // Primitive types
      if (typeof obj === 'boolean') return 4;
      if (typeof obj === 'number') return 8;
      if (typeof obj === 'string') return obj.length * 2; // UTF-16
      if (typeof obj === 'symbol') return 8;
      if (typeof obj === 'bigint') return 8;

      // Avoid circular references
      if (seen.has(obj)) return 0;
      seen.add(obj);

      let size = 0;

      if (Array.isArray(obj)) {
        size += 24; // Array overhead
        for (const item of obj) {
          size += calculateSize(item);
        }
      } else if (typeof obj === 'object') {
        size += 24; // Object overhead
        for (const key in obj) {
          if (obj.hasOwnProperty(key)) {
            size += calculateSize(key);
            size += calculateSize(obj[key]);
          }
        }
      } else if (typeof obj === 'function') {
        size += obj.toString().length * 2;
      }

      return size;
    }

    return calculateSize(obj);
  }

  /**
   * Creates a memory measurement record
   */
  static createMemoryMeasurement(
    inputSize: number,
    memoryUsage: number,
    peakMemory: number,
    allocations: number = 1
  ): MemoryMeasurement {
    return {
      inputSize,
      memoryUsage,
      peakMemory,
      allocations,
      timestamp: new Date(),
    };
  }

  /**
   * Calculates memory statistics from multiple measurements
   */
  static calculateMemoryStats(measurements: MemoryMeasurement[]): {
    averageMemory: number;
    peakMemory: number;
    totalAllocations: number;
    memoryEfficiency: number; // Memory usage per input size
    growthRate: number; // How memory grows with input size
  } {
    if (measurements.length === 0) {
      throw new Error('No measurements provided');
    }

    const memoryUsages = measurements.map(m => m.memoryUsage);
    const peakMemories = measurements.map(m => m.peakMemory);
    const inputSizes = measurements.map(m => m.inputSize);

    const averageMemory = memoryUsages.reduce((sum, mem) => sum + mem, 0) / memoryUsages.length;
    const peakMemory = Math.max(...peakMemories);
    const totalAllocations = measurements.reduce((sum, m) => sum + m.allocations, 0);

    // Calculate memory efficiency (memory per input unit)
    const totalInputSize = inputSizes.reduce((sum, size) => sum + size, 0);
    const totalMemoryUsage = memoryUsages.reduce((sum, mem) => sum + mem, 0);
    const memoryEfficiency = totalMemoryUsage / totalInputSize;

    // Calculate growth rate using linear regression
    const growthRate = this.calculateMemoryGrowthRate(measurements);

    return {
      averageMemory,
      peakMemory,
      totalAllocations,
      memoryEfficiency,
      growthRate,
    };
  }

  /**
   * Calculates memory growth rate using linear regression
   */
  private static calculateMemoryGrowthRate(measurements: MemoryMeasurement[]): number {
    if (measurements.length < 2) return 0;

    const n = measurements.length;
    const sumX = measurements.reduce((sum, m) => sum + m.inputSize, 0);
    const sumY = measurements.reduce((sum, m) => sum + m.memoryUsage, 0);
    const sumXY = measurements.reduce((sum, m) => sum + m.inputSize * m.memoryUsage, 0);
    const sumXX = measurements.reduce((sum, m) => sum + m.inputSize * m.inputSize, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    return slope;
  }

  /**
   * Filters memory measurements to remove outliers
   */
  static filterMemoryOutliers(measurements: MemoryMeasurement[], threshold: number = 2): MemoryMeasurement[] {
    if (measurements.length < 3) return measurements;

    const memoryUsages = measurements.map(m => m.memoryUsage);
    const mean = memoryUsages.reduce((sum, mem) => sum + mem, 0) / memoryUsages.length;
    const variance = memoryUsages.reduce((sum, mem) => sum + Math.pow(mem - mean, 2), 0) / memoryUsages.length;
    const standardDeviation = Math.sqrt(variance);

    return measurements.filter(measurement => {
      const zScore = Math.abs(measurement.memoryUsage - mean) / standardDeviation;
      return zScore <= threshold;
    });
  }

  /**
   * Validates memory measurements
   */
  static validateMemoryMeasurements(measurements: MemoryMeasurement[]): {
    isValid: boolean;
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];

    if (measurements.length === 0) {
      issues.push('No memory measurements provided');
      return { isValid: false, issues, recommendations };
    }

    // Check for negative memory values
    const negativeMemory = measurements.filter(m => m.memoryUsage < 0 || m.peakMemory < 0);
    if (negativeMemory.length > 0) {
      issues.push(`${negativeMemory.length} measurements have negative memory values`);
    }

    // Check for peak memory less than usage
    const invalidPeaks = measurements.filter(m => m.peakMemory < m.memoryUsage);
    if (invalidPeaks.length > 0) {
      issues.push(`${invalidPeaks.length} measurements have peak memory less than usage`);
    }

    // Check if memory tracking is supported
    if (!MemoryTracker.isMemoryTrackingSupported()) {
      recommendations.push('Memory tracking is not fully supported in this browser. Results may be estimated.');
    }

    // Check for extremely high memory usage
    const highMemoryThreshold = 100 * 1024 * 1024; // 100MB
    const highMemoryMeasurements = measurements.filter(m => m.memoryUsage > highMemoryThreshold);
    if (highMemoryMeasurements.length > 0) {
      recommendations.push('Some measurements show very high memory usage. Consider optimizing the algorithm.');
    }

    return {
      isValid: issues.length === 0,
      issues,
      recommendations,
    };
  }

  /**
   * Formats memory size for display
   */
  static formatMemorySize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  /**
   * Converts memory measurements to chart data
   */
  static toChartData(measurements: MemoryMeasurement[]): Array<{ x: number; y: number; peak: number }> {
    return measurements.map(m => ({
      x: m.inputSize,
      y: m.memoryUsage,
      peak: m.peakMemory,
    }));
  }
}

/**
 * Python-specific memory tracking utilities
 */
export class PythonMemoryTracker {
  /**
   * Tracks memory usage in Python code using tracemalloc
   */
  static async trackPythonMemory(code: string): Promise<{
    current: number;
    peak: number;
    output: string;
  }> {
    const pyodide = (window as any).pyodide;

    if (!pyodide) {
      throw new Error('Python environment not available');
    }

    try {
      // Set up memory tracking
      const result = pyodide.runPython(`
import tracemalloc
import sys
from io import StringIO

# Start memory tracking
tracemalloc.start()

# Capture output
_captured_output = StringIO()
_original_stdout = sys.stdout
sys.stdout = _captured_output

try:
    # Execute user code
    ${code.split('\n').map(line => `    ${line}`).join('\n')}

    # Get memory stats
    current, peak = tracemalloc.get_traced_memory()
    output = _captured_output.getvalue()

    result = {
        'current': current,
        'peak': peak,
        'output': output
    }
finally:
    # Cleanup
    tracemalloc.stop()
    sys.stdout = _original_stdout

result
      `);

      return result.toJs();
    } catch (error) {
      // Ensure cleanup on error
      try {
        pyodide.runPython(`
import sys
if 'tracemalloc' in sys.modules:
    tracemalloc.stop()
if '_original_stdout' in locals():
    sys.stdout = _original_stdout
        `);
      } catch {}

      throw error;
    }
  }

  /**
   * Estimates Python object memory usage
   */
  static estimatePythonObjectSize(pyodide: any, objectName: string): number {
    try {
      return pyodide.runPython(`
import sys
sys.getsizeof(${objectName})
      `);
    } catch {
      return 0;
    }
  }
}

/**
 * Global memory tracker instance
 */
export const globalMemoryTracker = new MemoryTracker();
