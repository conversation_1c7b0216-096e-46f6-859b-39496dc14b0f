/**
 * Core types and interfaces for complexity analysis system
 */

// Complexity classification types
export type ComplexityCategory =
  | 'constant'
  | 'logarithmic'
  | 'linear'
  | 'linearithmic'
  | 'quadratic'
  | 'cubic'
  | 'exponential'
  | 'factorial';

export type EfficiencyRating = 'excellent' | 'good' | 'fair' | 'poor' | 'terrible';

export interface ComplexityClass {
  notation: string; // "O(1)", "O(n)", "O(n²)", etc.
  category: ComplexityCategory;
  description: string;
  efficiency: EfficiencyRating;
  growthRate: number; // Numerical representation for comparison
}

// Performance measurement types
export interface TimeMeasurement {
  inputSize: number;
  executionTime: number; // in milliseconds
  iterations: number;
  timestamp: Date;
}

export interface MemoryMeasurement {
  inputSize: number;
  memoryUsage: number; // in bytes
  peakMemory: number; // in bytes
  allocations: number;
  timestamp: Date;
}

export interface PerformanceMeasurement {
  inputSize: number;
  executionTime: number;
  memoryUsage: number;
  peakMemory: number;
  iterations: number;
  timestamp: Date;
}

// Analysis configuration types
export interface AnalysisOptions {
  enableTimeAnalysis: boolean;
  enableSpaceAnalysis: boolean;
  inputSizes: number[];
  maxExecutionTime: number; // timeout in milliseconds
  iterations: number; // number of runs per input size
  customInputGenerator?: (size: number) => any;
}

export interface AnalysisConfig {
  defaultInputSizes: number[];
  maxTimeout: number;
  defaultIterations: number;
  memoryTrackingEnabled: boolean;
}

// Input generation types
export type InputPattern =
  | 'array'
  | 'string'
  | 'number'
  | 'object'
  | 'nested-array'
  | 'matrix'
  | 'tree'
  | 'graph'
  | 'custom';

export interface InputGeneratorConfig {
  pattern: InputPattern;
  baseValue: any;
  scalingStrategy: 'linear' | 'exponential' | 'custom';
  customScaler?: (baseValue: any, targetSize: number) => any;
}

// Results and analysis types
export interface ComplexityResult {
  id: string;
  timestamp: Date;
  code: string;
  language: string;
  timeComplexity: ComplexityClass | null;
  spaceComplexity: ComplexityClass | null;
  measurements: PerformanceMeasurement[];
  confidence: {
    time: number; // 0-1 confidence score
    space: number; // 0-1 confidence score
  };
  suggestions: OptimizationSuggestion[];
  analysisOptions: AnalysisOptions;
  executionSuccess: boolean;
  errorMessage?: string;
}

export interface OptimizationSuggestion {
  type: 'time' | 'space' | 'general';
  severity: 'info' | 'warning' | 'critical';
  title: string;
  description: string;
  codeExample?: string;
  expectedImprovement?: string;
}

// Comparison types
export interface ComplexityComparison {
  results: ComplexityResult[];
  winner: {
    time: string | null; // result ID
    space: string | null; // result ID
    overall: string | null; // result ID
  };
  tradeoffs: TradeoffAnalysis[];
}

export interface TradeoffAnalysis {
  aspect: 'time-vs-space' | 'readability-vs-performance' | 'memory-vs-speed';
  description: string;
  recommendation: string;
}

// Visualization types
export interface ChartDataPoint {
  x: number; // input size
  y: number; // time or memory
  label?: string;
}

export interface ComplexityChartData {
  timeData: ChartDataPoint[];
  memoryData: ChartDataPoint[];
  fittedCurve?: {
    time: ChartDataPoint[];
    space: ChartDataPoint[];
  };
  complexityClass?: ComplexityClass;
}

// Error types
export interface AnalysisError {
  type: 'timeout' | 'memory-limit' | 'execution-error' | 'analysis-error';
  message: string;
  details?: any;
  recoverable: boolean;
}

// Controller interfaces
export interface ComplexityAnalysisController {
  analyzeCode(
    code: string,
    language: string,
    options: AnalysisOptions
  ): Promise<ComplexityResult>;

  generateInputSizes(baseInput: any, maxSize: number): number[];

  classifyComplexity(
    measurements: PerformanceMeasurement[]
  ): {
    time: ComplexityClass | null;
    space: ComplexityClass | null;
    confidence: { time: number; space: number };
  };

  compareResults(results: ComplexityResult[]): ComplexityComparison;
}

export interface ComplexityProfiler {
  profileExecution(
    code: string,
    inputs: any[],
    language: string
  ): Promise<PerformanceMeasurement[]>;

  measureTime(
    executionFn: () => Promise<any>,
    input: any,
    iterations: number
  ): Promise<TimeMeasurement>;

  measureMemory(
    executionFn: () => Promise<any>,
    input: any
  ): Promise<MemoryMeasurement>;
}

export interface InputGenerator {
  generateTestInputs(baseInput: any, sizes: number[]): any[];
  detectInputPattern(input: any): InputPattern;
  scaleInput(input: any, targetSize: number): any;
  validateInput(input: any, expectedPattern: InputPattern): boolean;
}

export interface ComplexityClassifier {
  classifyTimeComplexity(measurements: TimeMeasurement[]): {
    complexity: ComplexityClass | null;
    confidence: number;
    fittingError: number;
  };

  classifySpaceComplexity(measurements: MemoryMeasurement[]): {
    complexity: ComplexityClass | null;
    confidence: number;
    fittingError: number;
  };

  calculateConfidence(
    measurements: PerformanceMeasurement[],
    classification: ComplexityClass
  ): number;
}

// Constants for complexity classes
export const COMPLEXITY_CLASSES: Record<ComplexityCategory, ComplexityClass> = {
  constant: {
    notation: 'O(1)',
    category: 'constant',
    description: 'Constant time - execution time does not depend on input size',
    efficiency: 'excellent',
    growthRate: 1,
  },
  logarithmic: {
    notation: 'O(log n)',
    category: 'logarithmic',
    description: 'Logarithmic time - execution time grows logarithmically with input size',
    efficiency: 'excellent',
    growthRate: 2,
  },
  linear: {
    notation: 'O(n)',
    category: 'linear',
    description: 'Linear time - execution time grows linearly with input size',
    efficiency: 'good',
    growthRate: 3,
  },
  linearithmic: {
    notation: 'O(n log n)',
    category: 'linearithmic',
    description: 'Linearithmic time - common in efficient sorting algorithms',
    efficiency: 'good',
    growthRate: 4,
  },
  quadratic: {
    notation: 'O(n²)',
    category: 'quadratic',
    description: 'Quadratic time - execution time grows quadratically with input size',
    efficiency: 'fair',
    growthRate: 5,
  },
  cubic: {
    notation: 'O(n³)',
    category: 'cubic',
    description: 'Cubic time - execution time grows cubically with input size',
    efficiency: 'poor',
    growthRate: 6,
  },
  exponential: {
    notation: 'O(2ⁿ)',
    category: 'exponential',
    description: 'Exponential time - execution time doubles with each additional input element',
    efficiency: 'terrible',
    growthRate: 7,
  },
  factorial: {
    notation: 'O(n!)',
    category: 'factorial',
    description: 'Factorial time - execution time grows factorially with input size',
    efficiency: 'terrible',
    growthRate: 8,
  },
};

// Default configuration
export const DEFAULT_ANALYSIS_CONFIG: AnalysisConfig = {
  defaultInputSizes: [10, 50, 100, 500, 1000, 2000],
  maxTimeout: 30000, // 30 seconds
  defaultIterations: 3,
  memoryTrackingEnabled: true,
};

export const DEFAULT_ANALYSIS_OPTIONS: AnalysisOptions = {
  enableTimeAnalysis: true,
  enableSpaceAnalysis: true,
  inputSizes: DEFAULT_ANALYSIS_CONFIG.defaultInputSizes,
  maxExecutionTime: DEFAULT_ANALYSIS_CONFIG.maxTimeout,
  iterations: DEFAULT_ANALYSIS_CONFIG.defaultIterations,
};
