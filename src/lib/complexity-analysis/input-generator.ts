/**
 * Input generation system for complexity analysis
 * Generates test inputs of various sizes and patterns for algorithm testing
 */

import { InputPattern, InputGenerator, InputGeneratorConfig } from './types';

/**
 * Main input generator implementation
 */
export class InputGeneratorImpl implements InputGenerator {
  /**
   * Generates test inputs of different sizes based on a base input
   */
  generateTestInputs(baseInput: any, sizes: number[]): any[] {
    const pattern = this.detectInputPattern(baseInput);

    return sizes.map(size => this.scaleInput(baseInput, size, pattern));
  }

  /**
   * Detects the pattern of input data
   */
  detectInputPattern(input: any): InputPattern {
    if (input === null || input === undefined) {
      return 'custom';
    }

    if (Array.isArray(input)) {
      if (input.length === 0) {
        return 'array';
      }

      // Check if it's a matrix (2D array)
      if (Array.isArray(input[0])) {
        return 'matrix';
      }

      // Check if it contains objects (nested structure)
      if (typeof input[0] === 'object' && input[0] !== null) {
        return 'nested-array';
      }

      return 'array';
    }

    if (typeof input === 'string') {
      return 'string';
    }

    if (typeof input === 'number') {
      return 'number';
    }

    if (typeof input === 'object') {
      // Check if it looks like a tree structure
      if (this.isTreeLike(input)) {
        return 'tree';
      }

      // Check if it looks like a graph structure
      if (this.isGraphLike(input)) {
        return 'graph';
      }

      return 'object';
    }

    return 'custom';
  }

  /**
   * Scales input to target size based on detected pattern
   */
  scaleInput(input: any, targetSize: number, pattern?: InputPattern): any {
    const detectedPattern = pattern || this.detectInputPattern(input);

    switch (detectedPattern) {
      case 'array':
        return this.scaleArray(input, targetSize);

      case 'string':
        return this.scaleString(input, targetSize);

      case 'number':
        return this.scaleNumber(input, targetSize);

      case 'matrix':
        return this.scaleMatrix(input, targetSize);

      case 'nested-array':
        return this.scaleNestedArray(input, targetSize);

      case 'object':
        return this.scaleObject(input, targetSize);

      case 'tree':
        return this.scaleTree(input, targetSize);

      case 'graph':
        return this.scaleGraph(input, targetSize);

      default:
        return this.scaleCustom(input, targetSize);
    }
  }

  /**
   * Validates input against expected pattern
   */
  validateInput(input: any, expectedPattern: InputPattern): boolean {
    const actualPattern = this.detectInputPattern(input);
    return actualPattern === expectedPattern;
  }

  /**
   * Scales an array to target size
   */
  private scaleArray(arr: any[], targetSize: number): any[] {
    if (!Array.isArray(arr)) {
      return new Array(targetSize).fill(0);
    }

    if (arr.length === 0) {
      return new Array(targetSize).fill(0);
    }

    const result: any[] = [];

    // If target size is smaller, truncate
    if (targetSize <= arr.length) {
      return arr.slice(0, targetSize);
    }

    // If target size is larger, repeat pattern
    for (let i = 0; i < targetSize; i++) {
      result.push(arr[i % arr.length]);
    }

    return result;
  }

  /**
   * Scales a string to target length
   */
  private scaleString(str: string, targetSize: number): string {
    if (typeof str !== 'string') {
      return 'a'.repeat(targetSize);
    }

    if (str.length === 0) {
      return 'a'.repeat(targetSize);
    }

    if (targetSize <= str.length) {
      return str.substring(0, targetSize);
    }

    return str.repeat(Math.ceil(targetSize / str.length)).substring(0, targetSize);
  }

  /**
   * Scales a number to target value
   */
  private scaleNumber(num: number, targetSize: number): number {
    if (typeof num !== 'number') {
      return targetSize;
    }

    // For numbers, we can scale proportionally or use target size directly
    return targetSize;
  }

  /**
   * Scales a matrix (2D array) to target size
   */
  private scaleMatrix(matrix: any[][], targetSize: number): any[][] {
    if (!Array.isArray(matrix) || matrix.length === 0) {
      const size = Math.ceil(Math.sqrt(targetSize));
      return Array(size).fill(null).map(() => Array(size).fill(0));
    }

    const size = Math.ceil(Math.sqrt(targetSize));
    const result: any[][] = [];

    for (let i = 0; i < size; i++) {
      const row: any[] = [];
      const sourceRow = matrix[i % matrix.length] || [];

      for (let j = 0; j < size; j++) {
        row.push(sourceRow[j % sourceRow.length] || 0);
      }

      result.push(row);
    }

    return result;
  }

  /**
   * Scales a nested array structure
   */
  private scaleNestedArray(arr: any[], targetSize: number): any[] {
    if (!Array.isArray(arr) || arr.length === 0) {
      return new Array(targetSize).fill({});
    }

    const result: any[] = [];

    for (let i = 0; i < targetSize; i++) {
      const sourceItem = arr[i % arr.length];

      if (Array.isArray(sourceItem)) {
        result.push([...sourceItem]);
      } else if (typeof sourceItem === 'object' && sourceItem !== null) {
        result.push({ ...sourceItem });
      } else {
        result.push(sourceItem);
      }
    }

    return result;
  }

  /**
   * Scales an object by adding more properties
   */
  private scaleObject(obj: any, targetSize: number): any {
    if (typeof obj !== 'object' || obj === null) {
      const result: any = {};
      for (let i = 0; i < targetSize; i++) {
        result[`key_${i}`] = i;
      }
      return result;
    }

    const keys = Object.keys(obj);
    if (keys.length === 0) {
      const result: any = {};
      for (let i = 0; i < targetSize; i++) {
        result[`key_${i}`] = i;
      }
      return result;
    }

    const result: any = {};

    for (let i = 0; i < targetSize; i++) {
      const sourceKey = keys[i % keys.length];
      const newKey = i < keys.length ? sourceKey : `${sourceKey}_${i}`;
      result[newKey] = obj[sourceKey];
    }

    return result;
  }

  /**
   * Scales a tree structure
   */
  private scaleTree(tree: any, targetSize: number): any {
    if (typeof tree !== 'object' || tree === null) {
      return this.generateBinaryTree(targetSize);
    }

    // Create a balanced binary tree with target number of nodes
    return this.generateBinaryTree(targetSize, tree.value || 0);
  }

  /**
   * Scales a graph structure
   */
  private scaleGraph(graph: any, targetSize: number): any {
    if (typeof graph !== 'object' || graph === null) {
      return this.generateGraph(targetSize);
    }

    // Generate a graph with target number of nodes
    return this.generateGraph(targetSize, graph.nodes?.[0]?.value || 0);
  }

  /**
   * Scales custom input types
   */
  private scaleCustom(input: any, targetSize: number): any {
    // For unknown types, try to create a reasonable scaled version
    if (typeof input === 'function') {
      return input; // Functions don't scale
    }

    // Default to creating an array of the input
    return new Array(targetSize).fill(input);
  }

  /**
   * Checks if an object looks like a tree structure
   */
  private isTreeLike(obj: any): boolean {
    return (
      typeof obj === 'object' &&
      obj !== null &&
      ('left' in obj || 'right' in obj || 'children' in obj || 'value' in obj)
    );
  }

  /**
   * Checks if an object looks like a graph structure
   */
  private isGraphLike(obj: any): boolean {
    return (
      typeof obj === 'object' &&
      obj !== null &&
      ('nodes' in obj || 'edges' in obj || 'adjacencyList' in obj)
    );
  }

  /**
   * Generates a binary tree with specified number of nodes
   */
  private generateBinaryTree(nodeCount: number, baseValue: number = 0): any {
    if (nodeCount <= 0) return null;

    const nodes: any[] = [];

    // Create nodes
    for (let i = 0; i < nodeCount; i++) {
      nodes.push({
        value: baseValue + i,
        left: null,
        right: null,
      });
    }

    // Connect nodes to form a balanced binary tree
    for (let i = 0; i < Math.floor(nodeCount / 2); i++) {
      const leftIndex = 2 * i + 1;
      const rightIndex = 2 * i + 2;

      if (leftIndex < nodeCount) {
        nodes[i].left = nodes[leftIndex];
      }

      if (rightIndex < nodeCount) {
        nodes[i].right = nodes[rightIndex];
      }
    }

    return nodes[0];
  }

  /**
   * Generates a graph with specified number of nodes
   */
  private generateGraph(nodeCount: number, baseValue: number = 0): any {
    const nodes = [];
    const edges = [];

    // Create nodes
    for (let i = 0; i < nodeCount; i++) {
      nodes.push({
        id: i,
        value: baseValue + i,
      });
    }

    // Create edges (simple connected graph)
    for (let i = 0; i < nodeCount - 1; i++) {
      edges.push({
        from: i,
        to: i + 1,
        weight: 1,
      });
    }

    // Add some additional edges for complexity
    const additionalEdges = Math.min(nodeCount, 5);
    for (let i = 0; i < additionalEdges; i++) {
      const from = Math.floor(Math.random() * nodeCount);
      const to = Math.floor(Math.random() * nodeCount);

      if (from !== to) {
        edges.push({
          from,
          to,
          weight: Math.floor(Math.random() * 10) + 1,
        });
      }
    }

    return {
      nodes,
      edges,
      adjacencyList: this.createAdjacencyList(nodes, edges),
    };
  }

  /**
   * Creates adjacency list representation of a graph
   */
  private createAdjacencyList(nodes: any[], edges: any[]): { [key: number]: number[] } {
    const adjacencyList: { [key: number]: number[] } = {};

    // Initialize adjacency list
    nodes.forEach(node => {
      adjacencyList[node.id] = [];
    });

    // Add edges to adjacency list
    edges.forEach(edge => {
      adjacencyList[edge.from].push(edge.to);
    });

    return adjacencyList;
  }
}

/**
 * Specialized input generators for common algorithm patterns
 */
export class SpecializedGenerators {
  /**
   * Generates sorted arrays for testing sorting algorithms
   */
  static generateSortedArray(size: number, ascending: boolean = true): number[] {
    const arr = Array.from({ length: size }, (_, i) => i);
    return ascending ? arr : arr.reverse();
  }

  /**
   * Generates random arrays
   */
  static generateRandomArray(size: number, min: number = 0, max: number = 1000): number[] {
    return Array.from({ length: size }, () =>
      Math.floor(Math.random() * (max - min + 1)) + min
    );
  }

  /**
   * Generates arrays with duplicates
   */
  static generateArrayWithDuplicates(size: number, uniqueValues: number = 10): number[] {
    const values = Array.from({ length: uniqueValues }, (_, i) => i);
    return Array.from({ length: size }, () =>
      values[Math.floor(Math.random() * values.length)]
    );
  }

  /**
   * Generates strings with specific patterns
   */
  static generatePatternedString(size: number, pattern: 'random' | 'repeated' | 'alternating' = 'random'): string {
    const chars = 'abcdefghijklmnopqrstuvwxyz';

    switch (pattern) {
      case 'repeated':
        return 'a'.repeat(size);

      case 'alternating':
        return Array.from({ length: size }, (_, i) => i % 2 === 0 ? 'a' : 'b').join('');

      case 'random':
      default:
        return Array.from({ length: size }, () =>
          chars[Math.floor(Math.random() * chars.length)]
        ).join('');
    }
  }

  /**
   * Generates linked list structure
   */
  static generateLinkedList(size: number, baseValue: number = 0): any {
    if (size <= 0) return null;

    const head = { value: baseValue, next: null };
    let current = head;

    for (let i = 1; i < size; i++) {
      current.next = { value: baseValue + i, next: null };
      current = current.next;
    }

    return head;
  }

  /**
   * Generates complete binary tree
   */
  static generateCompleteBinaryTree(levels: number, baseValue: number = 0): any {
    if (levels <= 0) return null;

    const totalNodes = Math.pow(2, levels) - 1;
    return new InputGeneratorImpl()['generateBinaryTree'](totalNodes, baseValue);
  }

  /**
   * Generates adjacency matrix for graph algorithms
   */
  static generateAdjacencyMatrix(nodeCount: number, density: number = 0.3): number[][] {
    const matrix = Array(nodeCount).fill(null).map(() => Array(nodeCount).fill(0));

    for (let i = 0; i < nodeCount; i++) {
      for (let j = i + 1; j < nodeCount; j++) {
        if (Math.random() < density) {
          const weight = Math.floor(Math.random() * 10) + 1;
          matrix[i][j] = weight;
          matrix[j][i] = weight; // Undirected graph
        }
      }
    }

    return matrix;
  }

  /**
   * Generates test cases for specific algorithm types
   */
  static generateForAlgorithmType(
    type: 'sorting' | 'searching' | 'graph' | 'dynamic-programming' | 'string',
    size: number
  ): any {
    switch (type) {
      case 'sorting':
        return {
          random: this.generateRandomArray(size),
          sorted: this.generateSortedArray(size),
          reverse: this.generateSortedArray(size, false),
          duplicates: this.generateArrayWithDuplicates(size),
        };

      case 'searching':
        return {
          array: this.generateSortedArray(size),
          target: Math.floor(size / 2),
        };

      case 'graph':
        return {
          adjacencyMatrix: this.generateAdjacencyMatrix(size),
          adjacencyList: new InputGeneratorImpl()['generateGraph'](size).adjacencyList,
        };

      case 'dynamic-programming':
        return {
          array: this.generateRandomArray(size, 1, 100),
          target: Math.floor(size * 2.5),
        };

      case 'string':
        return {
          text: this.generatePatternedString(size, 'random'),
          pattern: this.generatePatternedString(Math.min(size / 10, 10), 'random'),
        };

      default:
        return this.generateRandomArray(size);
    }
  }
}

/**
 * Factory function to create input generator
 */
export function createInputGenerator(): InputGenerator {
  return new InputGeneratorImpl();
}

/**
 * Utility functions for input generation
 */
export class InputGenerationUtils {
  /**
   * Generates logarithmic size progression
   */
  static generateLogSizes(minSize: number, maxSize: number, steps: number): number[] {
    const sizes: number[] = [];
    const logMin = Math.log(minSize);
    const logMax = Math.log(maxSize);
    const logStep = (logMax - logMin) / (steps - 1);

    for (let i = 0; i < steps; i++) {
      const logSize = logMin + i * logStep;
      sizes.push(Math.round(Math.exp(logSize)));
    }

    return sizes;
  }

  /**
   * Generates linear size progression
   */
  static generateLinearSizes(minSize: number, maxSize: number, steps: number): number[] {
    const sizes: number[] = [];
    const step = (maxSize - minSize) / (steps - 1);

    for (let i = 0; i < steps; i++) {
      sizes.push(Math.round(minSize + i * step));
    }

    return sizes;
  }

  /**
   * Generates exponential size progression
   */
  static generateExponentialSizes(minSize: number, maxSize: number, steps: number): number[] {
    const sizes: number[] = [];
    const ratio = Math.pow(maxSize / minSize, 1 / (steps - 1));

    for (let i = 0; i < steps; i++) {
      sizes.push(Math.round(minSize * Math.pow(ratio, i)));
    }

    return sizes;
  }

  /**
   * Validates generated input sizes
   */
  static validateSizes(sizes: number[]): boolean {
    return (
      sizes.length > 0 &&
      sizes.every(size => size > 0) &&
      sizes.every((size, index) => index === 0 || size >= sizes[index - 1])
    );
  }
}
