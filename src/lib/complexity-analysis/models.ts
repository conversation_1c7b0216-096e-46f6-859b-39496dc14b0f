/**
 * Data models and utility functions for complexity analysis
 */

import {
  ComplexityResult,
  PerformanceMeasurement,
  ComplexityClass,
  OptimizationSuggestion,
  AnalysisOptions,
  ComplexityComparison,
  TradeoffAnalysis,
  COMPLEXITY_CLASSES
} from './types';

/**
 * Creates a new ComplexityResult instance
 */
export function createComplexityResult(
  code: string,
  language: string,
  options: AnalysisOptions
): ComplexityResult {
  return {
    id: generateResultId(),
    timestamp: new Date(),
    code,
    language,
    timeComplexity: null,
    spaceComplexity: null,
    measurements: [],
    confidence: {
      time: 0,
      space: 0,
    },
    suggestions: [],
    analysisOptions: options,
    executionSuccess: false,
  };
}

/**
 * Creates a new PerformanceMeasurement instance
 */
export function createPerformanceMeasurement(
  inputSize: number,
  executionTime: number,
  memoryUsage: number = 0,
  peakMemory: number = 0,
  iterations: number = 1
): PerformanceMeasurement {
  return {
    inputSize,
    executionTime,
    memoryUsage,
    peakMemory,
    iterations,
    timestamp: new Date(),
  };
}

/**
 * Creates an optimization suggestion
 */
export function createOptimizationSuggestion(
  type: 'time' | 'space' | 'general',
  severity: 'info' | 'warning' | 'critical',
  title: string,
  description: string,
  codeExample?: string,
  expectedImprovement?: string
): OptimizationSuggestion {
  return {
    type,
    severity,
    title,
    description,
    codeExample,
    expectedImprovement,
  };
}

/**
 * Generates a unique ID for complexity results
 */
export function generateResultId(): string {
  return `complexity_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Calculates average execution time from measurements
 */
export function calculateAverageExecutionTime(measurements: PerformanceMeasurement[]): number {
  if (measurements.length === 0) return 0;

  const totalTime = measurements.reduce((sum, m) => sum + m.executionTime, 0);
  return totalTime / measurements.length;
}

/**
 * Calculates peak memory usage from measurements
 */
export function calculatePeakMemoryUsage(measurements: PerformanceMeasurement[]): number {
  if (measurements.length === 0) return 0;

  return Math.max(...measurements.map(m => m.peakMemory));
}

/**
 * Sorts measurements by input size
 */
export function sortMeasurementsByInputSize(measurements: PerformanceMeasurement[]): PerformanceMeasurement[] {
  return [...measurements].sort((a, b) => a.inputSize - b.inputSize);
}

/**
 * Filters measurements by input size range
 */
export function filterMeasurementsByInputSize(
  measurements: PerformanceMeasurement[],
  minSize: number,
  maxSize: number
): PerformanceMeasurement[] {
  return measurements.filter(m => m.inputSize >= minSize && m.inputSize <= maxSize);
}

/**
 * Calculates growth rate between two measurements
 */
export function calculateGrowthRate(
  measurement1: PerformanceMeasurement,
  measurement2: PerformanceMeasurement
): { timeGrowth: number; spaceGrowth: number } {
  const sizeRatio = measurement2.inputSize / measurement1.inputSize;
  const timeGrowth = measurement2.executionTime / measurement1.executionTime;
  const spaceGrowth = measurement2.memoryUsage / measurement1.memoryUsage;

  return {
    timeGrowth: timeGrowth / sizeRatio,
    spaceGrowth: spaceGrowth / sizeRatio,
  };
}

/**
 * Validates a complexity result
 */
export function validateComplexityResult(result: ComplexityResult): boolean {
  return (
    result.id.length > 0 &&
    result.code.length > 0 &&
    result.language.length > 0 &&
    result.measurements.length > 0 &&
    result.confidence.time >= 0 && result.confidence.time <= 1 &&
    result.confidence.space >= 0 && result.confidence.space <= 1
  );
}

/**
 * Compares two complexity classes by efficiency
 */
export function compareComplexityClasses(class1: ComplexityClass, class2: ComplexityClass): number {
  return class1.growthRate - class2.growthRate;
}

/**
 * Gets complexity class by category
 */
export function getComplexityClass(category: keyof typeof COMPLEXITY_CLASSES): ComplexityClass {
  return COMPLEXITY_CLASSES[category];
}

/**
 * Determines if a complexity class is considered efficient
 */
export function isEfficientComplexity(complexityClass: ComplexityClass): boolean {
  return complexityClass.efficiency === 'excellent' || complexityClass.efficiency === 'good';
}

/**
 * Creates a comparison between multiple complexity results
 */
export function createComplexityComparison(results: ComplexityResult[]): ComplexityComparison {
  if (results.length < 2) {
    throw new Error('At least 2 results are required for comparison');
  }

  // Find winners for each category
  const timeWinner = findTimeComplexityWinner(results);
  const spaceWinner = findSpaceComplexityWinner(results);
  const overallWinner = findOverallWinner(results);

  // Generate tradeoff analysis
  const tradeoffs = generateTradeoffAnalysis(results);

  return {
    results,
    winner: {
      time: timeWinner?.id || null,
      space: spaceWinner?.id || null,
      overall: overallWinner?.id || null,
    },
    tradeoffs,
  };
}

/**
 * Finds the result with the best time complexity
 */
function findTimeComplexityWinner(results: ComplexityResult[]): ComplexityResult | null {
  const validResults = results.filter(r => r.timeComplexity !== null);
  if (validResults.length === 0) return null;

  return validResults.reduce((best, current) => {
    if (!best.timeComplexity || !current.timeComplexity) return best;
    return compareComplexityClasses(current.timeComplexity, best.timeComplexity) < 0 ? current : best;
  });
}

/**
 * Finds the result with the best space complexity
 */
function findSpaceComplexityWinner(results: ComplexityResult[]): ComplexityResult | null {
  const validResults = results.filter(r => r.spaceComplexity !== null);
  if (validResults.length === 0) return null;

  return validResults.reduce((best, current) => {
    if (!best.spaceComplexity || !current.spaceComplexity) return best;
    return compareComplexityClasses(current.spaceComplexity, best.spaceComplexity) < 0 ? current : best;
  });
}

/**
 * Finds the overall best result considering both time and space
 */
function findOverallWinner(results: ComplexityResult[]): ComplexityResult | null {
  const validResults = results.filter(r => r.timeComplexity !== null && r.spaceComplexity !== null);
  if (validResults.length === 0) return null;

  return validResults.reduce((best, current) => {
    if (!best.timeComplexity || !best.spaceComplexity ||
        !current.timeComplexity || !current.spaceComplexity) return best;

    const bestScore = best.timeComplexity.growthRate + best.spaceComplexity.growthRate;
    const currentScore = current.timeComplexity.growthRate + current.spaceComplexity.growthRate;

    return currentScore < bestScore ? current : best;
  });
}

/**
 * Generates tradeoff analysis between results
 */
function generateTradeoffAnalysis(results: ComplexityResult[]): TradeoffAnalysis[] {
  const tradeoffs: TradeoffAnalysis[] = [];

  // Time vs Space tradeoff analysis
  const timeWinner = findTimeComplexityWinner(results);
  const spaceWinner = findSpaceComplexityWinner(results);

  if (timeWinner && spaceWinner && timeWinner.id !== spaceWinner.id) {
    tradeoffs.push({
      aspect: 'time-vs-space',
      description: `Solution ${timeWinner.id.slice(-4)} is faster (${timeWinner.timeComplexity?.notation}) but uses more memory, while solution ${spaceWinner.id.slice(-4)} uses less memory (${spaceWinner.spaceComplexity?.notation}) but is slower.`,
      recommendation: 'Choose based on whether your application is more constrained by time or memory requirements.',
    });
  }

  return tradeoffs;
}

/**
 * Formats execution time for display
 */
export function formatExecutionTime(timeMs: number): string {
  if (timeMs < 1) {
    return `${(timeMs * 1000).toFixed(1)}μs`;
  } else if (timeMs < 1000) {
    return `${timeMs.toFixed(1)}ms`;
  } else {
    return `${(timeMs / 1000).toFixed(2)}s`;
  }
}

/**
 * Formats memory usage for display
 */
export function formatMemoryUsage(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(1)} ${units[unitIndex]}`;
}

/**
 * Calculates confidence score based on measurement consistency
 */
export function calculateMeasurementConfidence(measurements: PerformanceMeasurement[]): number {
  if (measurements.length < 2) return 0;

  // Calculate coefficient of variation for execution times
  const times = measurements.map(m => m.executionTime);
  const mean = times.reduce((sum, time) => sum + time, 0) / times.length;
  const variance = times.reduce((sum, time) => sum + Math.pow(time - mean, 2), 0) / times.length;
  const standardDeviation = Math.sqrt(variance);
  const coefficientOfVariation = standardDeviation / mean;

  // Convert to confidence score (lower variation = higher confidence)
  return Math.max(0, Math.min(1, 1 - coefficientOfVariation));
}
