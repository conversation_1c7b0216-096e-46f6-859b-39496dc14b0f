/**
 * Utility functions for complexity analysis
 */

import {
  PerformanceMeasurement,
  ComplexityClass,
  COMPLEXITY_CLASSES,
  InputPattern,
  AnalysisError
} from './types';

/**
 * Mathematical utilities for curve fitting and analysis
 */
export class MathUtils {
  /**
   * Performs linear regression on data points
   */
  static linearRegression(points: { x: number; y: number }[]): {
    slope: number;
    intercept: number;
    rSquared: number;
  } {
    const n = points.length;
    if (n < 2) throw new Error('Need at least 2 points for linear regression');

    const sumX = points.reduce((sum, p) => sum + p.x, 0);
    const sumY = points.reduce((sum, p) => sum + p.y, 0);
    const sumXY = points.reduce((sum, p) => sum + p.x * p.y, 0);
    const sumXX = points.reduce((sum, p) => sum + p.x * p.x, 0);
    const sumYY = points.reduce((sum, p) => sum + p.y * p.y, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;

    // Calculate R-squared
    const yMean = sumY / n;
    const totalSumSquares = points.reduce((sum, p) => sum + Math.pow(p.y - yMean, 2), 0);
    const residualSumSquares = points.reduce((sum, p) => {
      const predicted = slope * p.x + intercept;
      return sum + Math.pow(p.y - predicted, 2);
    }, 0);
    const rSquared = 1 - (residualSumSquares / totalSumSquares);

    return { slope, intercept, rSquared };
  }

  /**
   * Fits various complexity curves to data and returns best fit
   */
  static fitComplexityCurves(measurements: PerformanceMeasurement[]): {
    bestFit: ComplexityClass;
    confidence: number;
    allFits: Array<{ complexity: ComplexityClass; rSquared: number; error: number }>;
  } {
    if (measurements.length < 3) {
      throw new Error('Need at least 3 measurements for curve fitting');
    }

    const points = measurements.map(m => ({ x: m.inputSize, y: m.executionTime }));
    const fits: Array<{ complexity: ComplexityClass; rSquared: number; error: number }> = [];

    // Try fitting different complexity curves
    const complexityFunctions = {
      constant: (x: number) => 1,
      logarithmic: (x: number) => Math.log(x),
      linear: (x: number) => x,
      linearithmic: (x: number) => x * Math.log(x),
      quadratic: (x: number) => x * x,
      cubic: (x: number) => x * x * x,
      exponential: (x: number) => Math.pow(2, x / 100), // Scaled to prevent overflow
      factorial: (x: number) => this.factorial(Math.min(x, 10)), // Limited to prevent overflow
    };

    for (const [category, func] of Object.entries(complexityFunctions)) {
      try {
        const transformedPoints = points.map(p => ({ x: func(p.x), y: p.y }));
        const regression = this.linearRegression(transformedPoints);

        // Calculate fitting error
        const error = this.calculateFittingError(points, func, regression);

        fits.push({
          complexity: COMPLEXITY_CLASSES[category as keyof typeof COMPLEXITY_CLASSES],
          rSquared: Math.max(0, regression.rSquared),
          error,
        });
      } catch (e) {
        // Skip this complexity class if fitting fails
        continue;
      }
    }

    if (fits.length === 0) {
      throw new Error('Could not fit any complexity curves to the data');
    }

    // Find best fit (highest R-squared, lowest error)
    const bestFit = fits.reduce((best, current) => {
      const bestScore = best.rSquared - best.error * 0.1;
      const currentScore = current.rSquared - current.error * 0.1;
      return currentScore > bestScore ? current : best;
    });

    return {
      bestFit: bestFit,
      confidence: Math.min(bestFit.rSquared, 1 - bestFit.error),
      allFits: fits.sort((a, b) => (b.rSquared - b.error * 0.1) - (a.rSquared - a.error * 0.1)),
    };
  }

  /**
   * Calculates fitting error for a given function
   */
  private static calculateFittingError(
    points: { x: number; y: number }[],
    func: (x: number) => number,
    regression: { slope: number; intercept: number }
  ): number {
    const errors = points.map(p => {
      const predicted = regression.slope * func(p.x) + regression.intercept;
      return Math.abs(p.y - predicted) / p.y;
    });

    return errors.reduce((sum, error) => sum + error, 0) / errors.length;
  }

  /**
   * Calculates factorial (with limit to prevent overflow)
   */
  private static factorial(n: number): number {
    if (n <= 1) return 1;
    if (n > 10) return Infinity; // Prevent overflow

    let result = 1;
    for (let i = 2; i <= n; i++) {
      result *= i;
    }
    return result;
  }

  /**
   * Smooths data using moving average
   */
  static smoothData(data: number[], windowSize: number = 3): number[] {
    if (data.length < windowSize) return [...data];

    const smoothed: number[] = [];
    const halfWindow = Math.floor(windowSize / 2);

    for (let i = 0; i < data.length; i++) {
      const start = Math.max(0, i - halfWindow);
      const end = Math.min(data.length, i + halfWindow + 1);
      const window = data.slice(start, end);
      const average = window.reduce((sum, val) => sum + val, 0) / window.length;
      smoothed.push(average);
    }

    return smoothed;
  }
}

/**
 * Input generation utilities
 */
export class InputUtils {
  /**
   * Detects the pattern of input data
   */
  static detectInputPattern(input: any): InputPattern {
    if (Array.isArray(input)) {
      if (input.length > 0 && Array.isArray(input[0])) {
        return 'matrix';
      }
      if (input.length > 0 && typeof input[0] === 'object' && input[0] !== null) {
        return 'nested-array';
      }
      return 'array';
    }

    if (typeof input === 'string') {
      return 'string';
    }

    if (typeof input === 'number') {
      return 'number';
    }

    if (typeof input === 'object' && input !== null) {
      return 'object';
    }

    return 'custom';
  }

  /**
   * Generates test inputs of different sizes
   */
  static generateTestInputs(baseInput: any, sizes: number[]): any[] {
    const pattern = this.detectInputPattern(baseInput);

    return sizes.map(size => this.scaleInput(baseInput, size, pattern));
  }

  /**
   * Scales input to target size based on pattern
   */
  static scaleInput(input: any, targetSize: number, pattern?: InputPattern): any {
    const detectedPattern = pattern || this.detectInputPattern(input);

    switch (detectedPattern) {
      case 'array':
        return this.scaleArray(input, targetSize);

      case 'string':
        return this.scaleString(input, targetSize);

      case 'number':
        return targetSize;

      case 'matrix':
        return this.scaleMatrix(input, targetSize);

      case 'nested-array':
        return this.scaleNestedArray(input, targetSize);

      case 'object':
        return this.scaleObject(input, targetSize);

      default:
        return input;
    }
  }

  /**
   * Scales an array to target size
   */
  private static scaleArray(arr: any[], targetSize: number): any[] {
    if (arr.length === 0) {
      return new Array(targetSize).fill(0);
    }

    const result: any[] = [];
    for (let i = 0; i < targetSize; i++) {
      result.push(arr[i % arr.length]);
    }

    return result;
  }

  /**
   * Scales a string to target length
   */
  private static scaleString(str: string, targetSize: number): string {
    if (str.length === 0) {
      return 'a'.repeat(targetSize);
    }

    return str.repeat(Math.ceil(targetSize / str.length)).substring(0, targetSize);
  }

  /**
   * Scales a matrix to target size
   */
  private static scaleMatrix(matrix: any[][], targetSize: number): any[][] {
    const size = Math.ceil(Math.sqrt(targetSize));
    const result: any[][] = [];

    for (let i = 0; i < size; i++) {
      const row: any[] = [];
      for (let j = 0; j < size; j++) {
        const sourceRow = matrix[i % matrix.length] || [];
        row.push(sourceRow[j % sourceRow.length] || 0);
      }
      result.push(row);
    }

    return result;
  }

  /**
   * Scales a nested array structure
   */
  private static scaleNestedArray(arr: any[], targetSize: number): any[] {
    const result: any[] = [];

    for (let i = 0; i < targetSize; i++) {
      const sourceItem = arr[i % arr.length];
      if (Array.isArray(sourceItem)) {
        result.push([...sourceItem]);
      } else {
        result.push(sourceItem);
      }
    }

    return result;
  }

  /**
   * Scales an object by duplicating properties
   */
  private static scaleObject(obj: any, targetSize: number): any {
    const keys = Object.keys(obj);
    const result: any = {};

    for (let i = 0; i < targetSize; i++) {
      const key = keys[i % keys.length];
      result[`${key}_${i}`] = obj[key];
    }

    return result;
  }
}

/**
 * Performance measurement utilities
 */
export class PerformanceUtils {
  /**
   * Measures high-resolution time
   */
  static measureTime<T>(fn: () => T): { result: T; time: number } {
    const start = performance.now();
    const result = fn();
    const end = performance.now();

    return {
      result,
      time: end - start,
    };
  }

  /**
   * Measures async function execution time
   */
  static async measureAsyncTime<T>(fn: () => Promise<T>): Promise<{ result: T; time: number }> {
    const start = performance.now();
    const result = await fn();
    const end = performance.now();

    return {
      result,
      time: end - start,
    };
  }

  /**
   * Estimates memory usage (basic implementation)
   */
  static estimateMemoryUsage(obj: any): number {
    const seen = new WeakSet();

    function sizeOf(obj: any): number {
      if (obj === null || obj === undefined) return 0;
      if (typeof obj === 'boolean') return 4;
      if (typeof obj === 'number') return 8;
      if (typeof obj === 'string') return obj.length * 2;
      if (typeof obj === 'symbol') return 8;

      if (seen.has(obj)) return 0;
      seen.add(obj);

      if (Array.isArray(obj)) {
        return obj.reduce((sum, item) => sum + sizeOf(item), 0);
      }

      if (typeof obj === 'object') {
        return Object.keys(obj).reduce((sum, key) => {
          return sum + sizeOf(key) + sizeOf(obj[key]);
        }, 0);
      }

      return 0;
    }

    return sizeOf(obj);
  }

  /**
   * Creates a timeout promise
   */
  static createTimeout(ms: number): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => reject(new Error(`Operation timed out after ${ms}ms`)), ms);
    });
  }

  /**
   * Runs function with timeout
   */
  static async withTimeout<T>(
    fn: () => Promise<T>,
    timeoutMs: number
  ): Promise<T> {
    return Promise.race([
      fn(),
      this.createTimeout(timeoutMs),
    ]);
  }
}

/**
 * Error handling utilities
 */
export class ErrorUtils {
  /**
   * Creates an analysis error
   */
  static createAnalysisError(
    type: AnalysisError['type'],
    message: string,
    details?: any,
    recoverable: boolean = false
  ): AnalysisError {
    return {
      type,
      message,
      details,
      recoverable,
    };
  }

  /**
   * Determines if an error is recoverable
   */
  static isRecoverableError(error: any): boolean {
    if (error instanceof Error) {
      return (
        error.message.includes('timeout') ||
        error.message.includes('memory') ||
        error.message.includes('limit')
      );
    }
    return false;
  }

  /**
   * Formats error for user display
   */
  static formatErrorForUser(error: AnalysisError): string {
    switch (error.type) {
      case 'timeout':
        return `Analysis timed out. Try reducing input sizes or simplifying the code.`;

      case 'memory-limit':
        return `Analysis exceeded memory limits. The algorithm may have high space complexity.`;

      case 'execution-error':
        return `Code execution failed: ${error.message}`;

      case 'analysis-error':
        return `Analysis failed: ${error.message}`;

      default:
        return `An error occurred during analysis: ${error.message}`;
    }
  }
}

/**
 * Validation utilities
 */
export class ValidationUtils {
  /**
   * Validates input sizes array
   */
  static validateInputSizes(sizes: number[]): boolean {
    return (
      Array.isArray(sizes) &&
      sizes.length > 0 &&
      sizes.every(size => typeof size === 'number' && size > 0) &&
      sizes.every((size, index) => index === 0 || size > sizes[index - 1])
    );
  }

  /**
   * Validates measurement data
   */
  static validateMeasurements(measurements: PerformanceMeasurement[]): boolean {
    return (
      Array.isArray(measurements) &&
      measurements.length > 0 &&
      measurements.every(m =>
        typeof m.inputSize === 'number' &&
        typeof m.executionTime === 'number' &&
        typeof m.memoryUsage === 'number' &&
        m.inputSize > 0 &&
        m.executionTime >= 0 &&
        m.memoryUsage >= 0
      )
    );
  }

  /**
   * Validates code string
   */
  static validateCode(code: string): boolean {
    return typeof code === 'string' && code.trim().length > 0;
  }

  /**
   * Validates language string
   */
  static validateLanguage(language: string): boolean {
    const supportedLanguages = ['javascript', 'typescript', 'python'];
    return supportedLanguages.includes(language.toLowerCase());
  }
}
