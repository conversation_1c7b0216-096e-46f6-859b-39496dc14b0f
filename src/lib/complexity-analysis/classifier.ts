/**
 * Complexity classification algorithms
 * Analyzes performance measurements to determine Big O complexity classes
 */

import {
  ComplexityClass,
  ComplexityClassifier,
  PerformanceMeasurement,
  TimeMeasurement,
  MemoryMeasurement,
  COMPLEXITY_CLASSES,
  ComplexityCategory
} from './types';

/**
 * Curve fitting result for complexity analysis
 */
interface CurveFitResult {
  complexity: ComplexityCategory;
  rSquared: number;
  coefficients: number[];
  equation: string;
  residualError: number;
}

/**
 * Classification result with confidence metrics
 */
interface ClassificationResult {
  complexity: ComplexityClass | null;
  confidence: number;
  fittingError: number;
  alternativeClassifications: Array<{
    complexity: ComplexityClass;
    confidence: number;
    reason: string;
  }>;
}

/**
 * Main complexity classifier implementation
 */
export class ComplexityClassifierImpl implements ComplexityClassifier {
  private readonly MIN_MEASUREMENTS = 3;
  private readonly MIN_CONFIDENCE_THRESHOLD = 0.6;

  /**
   * Classifies time complexity from timing measurements
   */
  classifyTimeComplexity(measurements: TimeMeasurement[]): ClassificationResult {
    if (measurements.length < this.MIN_MEASUREMENTS) {
      return {
        complexity: null,
        confidence: 0,
        fittingError: 1,
        alternativeClassifications: [],
      };
    }

    try {
      // Sort measurements by input size
      const sortedMeasurements = [...measurements].sort((a, b) => a.inputSize - b.inputSize);

      console.log('🔍 Analyzing time complexity with measurements:', sortedMeasurements);

      // Use pattern-based classification instead of curve fitting
      const result = this.classifyByGrowthPattern(sortedMeasurements, 'time');

      console.log('🎯 Time complexity classification result:', result);

      return result;
    } catch (error) {
      console.error('Time complexity classification failed:', error);
      return {
        complexity: COMPLEXITY_CLASSES.linear, // Safe fallback
        confidence: 0.3,
        fittingError: 1,
        alternativeClassifications: [],
      };
    }
  }

  /**
   * Classifies space complexity from memory measurements
   */
  classifySpaceComplexity(measurements: MemoryMeasurement[]): ClassificationResult {
    if (measurements.length < this.MIN_MEASUREMENTS) {
      return {
        complexity: null,
        confidence: 0,
        fittingError: 1,
        alternativeClassifications: [],
      };
    }

    try {
      // Sort measurements by input size
      const sortedMeasurements = [...measurements].sort((a, b) => a.inputSize - b.inputSize);

      console.log('🔍 Analyzing space complexity with measurements:', sortedMeasurements);

      // Use pattern-based classification for space complexity
      const result = this.classifyByGrowthPattern(sortedMeasurements, 'space');

      console.log('🎯 Space complexity classification result:', result);

      return result;
    } catch (error) {
      console.error('Space complexity classification failed:', error);
      return {
        complexity: COMPLEXITY_CLASSES.constant, // Safe fallback for space
        confidence: 0.3,
        fittingError: 1,
        alternativeClassifications: [],
      };
    }
  }

  /**
   * Calculates confidence score for a complexity classification
   */
  calculateConfidence(
    measurements: PerformanceMeasurement[],
    classification: ComplexityClass
  ): number {
    if (measurements.length < this.MIN_MEASUREMENTS) {
      return 0;
    }

    const dataPoints = measurements.map(m => ({
      x: m.inputSize,
      y: m.executionTime,
    }));

    // Test how well the classification fits the data
    const expectedFunction = this.getComplexityFunction(classification.category);
    const fittingResult = this.fitCurveToFunction(dataPoints, expectedFunction);

    return this.calculateFitConfidence(fittingResult, dataPoints);
  }

  /**
   * Classifies complexity based on growth patterns instead of curve fitting
   */
  private classifyByGrowthPattern(
    measurements: Array<{ inputSize: number; executionTime?: number; memoryUsage?: number }>,
    type: 'time' | 'space'
  ): ClassificationResult {
    if (measurements.length < 2) {
      return {
        complexity: null,
        confidence: 0,
        fittingError: 1,
        alternativeClassifications: [],
      };
    }

    const firstMeasurement = measurements[0];
    const lastMeasurement = measurements[measurements.length - 1];

    const sizeRatio = lastMeasurement.inputSize / Math.max(firstMeasurement.inputSize, 1);

    let valueRatio: number;
    if (type === 'time') {
      valueRatio = (lastMeasurement.executionTime || 0) / Math.max(firstMeasurement.executionTime || 1, 0.001);
    } else {
      valueRatio = (lastMeasurement.memoryUsage || 0) / Math.max(firstMeasurement.memoryUsage || 1, 1);
    }

    console.log(`📊 Growth analysis: Size ratio = ${sizeRatio.toFixed(2)}x, Value ratio = ${valueRatio.toFixed(2)}x`);

    // Pattern recognition based on growth ratios
    const pattern = this.recognizeComplexityPattern(sizeRatio, valueRatio, measurements, type);

    return {
      complexity: COMPLEXITY_CLASSES[pattern.complexity],
      confidence: pattern.confidence,
      fittingError: pattern.error,
      alternativeClassifications: pattern.alternatives.map(alt => ({
        complexity: COMPLEXITY_CLASSES[alt.complexity],
        confidence: alt.confidence,
        reason: alt.reason,
      })),
    };
  }

  /**
   * Recognizes complexity patterns based on growth ratios and measurement trends
   */
  private recognizeComplexityPattern(
    sizeRatio: number,
    valueRatio: number,
    measurements: Array<{ inputSize: number; executionTime?: number; memoryUsage?: number }>,
    type: 'time' | 'space'
  ): {
    complexity: ComplexityCategory;
    confidence: number;
    error: number;
    alternatives: Array<{ complexity: ComplexityCategory; confidence: number; reason: string }>;
  } {
    const alternatives: Array<{ complexity: ComplexityCategory; confidence: number; reason: string }> = [];

    console.log(`📊 Analyzing ${type} complexity: Size ratio = ${sizeRatio.toFixed(2)}x, Value ratio = ${valueRatio.toFixed(2)}x`);

    // FIXED: Constant complexity O(1) - value should NOT grow with input size
    if (valueRatio <= 1.3) {
      console.log('🎯 Detected constant complexity: value barely grows with input size');
      alternatives.push({ complexity: 'linear', confidence: 0.2, reason: 'Could be linear with very small constant factor' });
      return {
        complexity: 'constant',
        confidence: 0.85,
        error: 0.1,
        alternatives,
      };
    }

    // FIXED: Linear complexity O(n) - value should grow proportionally to input size
    if (valueRatio >= 0.8 * sizeRatio && valueRatio <= 2.0 * sizeRatio) {
      console.log('🎯 Detected linear complexity: value grows proportionally to input size');
      let confidence = 0.8;

      // Higher confidence if ratio is close to 1:1
      if (valueRatio >= 0.9 * sizeRatio && valueRatio <= 1.1 * sizeRatio) {
        confidence = 0.9;
      }

      alternatives.push({ complexity: 'constant', confidence: 0.2, reason: 'Could be constant if growth is due to measurement noise' });
      alternatives.push({ complexity: 'linearithmic', confidence: 0.3, reason: 'Could be n log n if log factor is small' });

      return {
        complexity: 'linear',
        confidence,
        error: Math.abs(valueRatio - sizeRatio) / sizeRatio,
        alternatives,
      };
    }

    // FIXED: Quadratic complexity O(n²) - value should grow as square of input size
    const expectedQuadraticRatio = sizeRatio * sizeRatio;
    if (valueRatio >= 0.5 * expectedQuadraticRatio && valueRatio <= 1.5 * expectedQuadraticRatio) {
      console.log('🎯 Detected quadratic complexity: value grows as square of input size');

      alternatives.push({ complexity: 'linear', confidence: 0.3, reason: 'Could be linear with very high constant factor' });
      alternatives.push({ complexity: 'linearithmic', confidence: 0.4, reason: 'Could be n log n with large log factor' });

      return {
        complexity: 'quadratic',
        confidence: 0.75,
        error: Math.abs(valueRatio - expectedQuadraticRatio) / expectedQuadraticRatio,
        alternatives,
      };
    }

    // FIXED: Logarithmic complexity O(log n) - value grows very slowly
    const expectedLogRatio = Math.log2(sizeRatio);
    if (valueRatio > 1.1 && valueRatio <= Math.max(2.0, expectedLogRatio * 2.0) && valueRatio < 0.5 * sizeRatio) {
      console.log('🎯 Detected logarithmic complexity: value grows very slowly');

      alternatives.push({ complexity: 'constant', confidence: 0.4, reason: 'Could be constant with slight measurement variance' });
      alternatives.push({ complexity: 'linear', confidence: 0.2, reason: 'Could be linear with very small constant factor' });

      return {
        complexity: 'logarithmic',
        confidence: 0.7,
        error: Math.abs(valueRatio - expectedLogRatio) / Math.max(expectedLogRatio, 1),
        alternatives,
      };
    }

    // FIXED: Linearithmic complexity O(n log n) - value grows faster than linear but slower than quadratic
    const expectedLinearithmicRatio = sizeRatio * Math.log2(sizeRatio);
    if (valueRatio > 1.5 * sizeRatio && valueRatio <= 1.5 * expectedLinearithmicRatio && valueRatio < 0.7 * expectedQuadraticRatio) {
      console.log('🎯 Detected linearithmic complexity: value grows faster than linear');

      alternatives.push({ complexity: 'linear', confidence: 0.4, reason: 'Could be linear with high constant factor' });
      alternatives.push({ complexity: 'quadratic', confidence: 0.3, reason: 'Could be quadratic with small inputs' });

      return {
        complexity: 'linearithmic',
        confidence: 0.75,
        error: Math.abs(valueRatio - expectedLinearithmicRatio) / expectedLinearithmicRatio,
        alternatives,
      };
    }

    // Fallback classification with more conservative thresholds
    console.log('🔄 Using fallback classification based on growth rate');

    if (valueRatio <= 1.5) {
      return { complexity: 'constant', confidence: 0.6, error: 0.3, alternatives: [] };
    } else if (valueRatio <= sizeRatio * 2.5) {
      return { complexity: 'linear', confidence: 0.65, error: 0.25, alternatives: [] };
    } else if (valueRatio <= expectedQuadraticRatio * 0.8) {
      return { complexity: 'linearithmic', confidence: 0.6, error: 0.3, alternatives: [] };
    } else if (valueRatio <= expectedQuadraticRatio * 2.0) {
      return { complexity: 'quadratic', confidence: 0.6, error: 0.3, alternatives: [] };
    } else {
      return { complexity: 'cubic', confidence: 0.4, error: 0.5, alternatives: [] };
    }
  }

  /**
   * Calculates how consistent the growth pattern is across measurements
   */
  private calculateGrowthConsistency(
    measurements: Array<{ inputSize: number; executionTime?: number; memoryUsage?: number }>,
    type: 'time' | 'space'
  ): number {
    if (measurements.length < 3) return 0.5;

    const ratios: number[] = [];

    for (let i = 1; i < measurements.length; i++) {
      const prev = measurements[i - 1];
      const curr = measurements[i];

      const sizeRatio = curr.inputSize / Math.max(prev.inputSize, 1);
      let valueRatio: number;

      if (type === 'time') {
        valueRatio = (curr.executionTime || 0) / Math.max(prev.executionTime || 1, 0.001);
      } else {
        valueRatio = (curr.memoryUsage || 0) / Math.max(prev.memoryUsage || 1, 1);
      }

      // Normalize by size ratio to get growth rate
      const normalizedRatio = valueRatio / Math.max(sizeRatio, 1);
      ratios.push(normalizedRatio);
    }

    // Calculate coefficient of variation (lower = more consistent)
    const mean = ratios.reduce((sum, r) => sum + r, 0) / ratios.length;
    const variance = ratios.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / ratios.length;
    const stdDev = Math.sqrt(variance);
    const coefficientOfVariation = mean > 0 ? stdDev / mean : 1;

    // Convert to consistency score (0-1, higher = more consistent)
    return Math.max(0, 1 - Math.min(coefficientOfVariation, 1));
  }

  /**
   * Checks if the growth pattern is monotonic (generally increasing)
   */
  private isMonotonicGrowth(
    measurements: Array<{ inputSize: number; executionTime?: number; memoryUsage?: number }>,
    type: 'time' | 'space'
  ): boolean {
    if (measurements.length < 2) return true;

    let decreaseCount = 0;

    for (let i = 1; i < measurements.length; i++) {
      const prev = measurements[i - 1];
      const curr = measurements[i];

      let prevValue: number, currValue: number;

      if (type === 'time') {
        prevValue = prev.executionTime || 0;
        currValue = curr.executionTime || 0;
      } else {
        prevValue = prev.memoryUsage || 0;
        currValue = curr.memoryUsage || 0;
      }

      if (currValue < prevValue * 0.9) { // Allow for 10% measurement noise
        decreaseCount++;
      }
    }

    // Allow up to 20% of measurements to decrease (noise tolerance)
    return decreaseCount <= measurements.length * 0.2;
  }

  /**
   * Legacy method - kept for compatibility but simplified
   */
  private fitAllComplexityCurves(dataPoints: Array<{ x: number; y: number }>): CurveFitResult[] {
    // This method is now deprecated - using pattern recognition instead
    console.warn('⚠️ fitAllComplexityCurves is deprecated, using pattern recognition instead');
    return [];
  }

  /**
   * Legacy method - now using pattern recognition instead
   */
  private fitSpaceComplexityCurves(dataPoints: Array<{ x: number; y: number }>): CurveFitResult[] {
    console.warn('⚠️ fitSpaceComplexityCurves is deprecated, using pattern recognition instead');
    return [];
  }

  /**
   * Legacy method - simplified for compatibility
   */
  private fitCurveToFunction(
    dataPoints: Array<{ x: number; y: number }>,
    func: (x: number) => number
  ): CurveFitResult & { rSquared: number; coefficients: number[]; equation: string; residualError: number } {
    return {
      complexity: 'linear' as ComplexityCategory,
      rSquared: 0.5,
      coefficients: [0, 1],
      equation: 'Simplified fit',
      residualError: 0.5,
    };
  }

  /**
   * Simplified linear regression for legacy compatibility
   */
  private linearRegression(points: Array<{ x: number; y: number }>): {
    slope: number;
    intercept: number;
    rSquared: number;
  } {
    if (points.length < 2) {
      return { slope: 0, intercept: 0, rSquared: 0 };
    }

    const n = points.length;
    const sumX = points.reduce((sum, p) => sum + p.x, 0);
    const sumY = points.reduce((sum, p) => sum + p.y, 0);
    const sumXY = points.reduce((sum, p) => sum + p.x * p.y, 0);
    const sumXX = points.reduce((sum, p) => sum + p.x * p.x, 0);

    const denominator = (n * sumXX - sumX * sumX);
    if (Math.abs(denominator) < 1e-10) {
      return { slope: 0, intercept: sumY / n, rSquared: 0 };
    }

    const slope = (n * sumXY - sumX * sumY) / denominator;
    const intercept = (sumY - slope * sumX) / n;

    return { slope, intercept, rSquared: 0.5 }; // Simplified
  }

  /**
   * Simplified residual error calculation
   */
  private calculateResidualError(
    dataPoints: Array<{ x: number; y: number }>,
    func: (x: number) => number,
    regression: { slope: number; intercept: number }
  ): number {
    return 0.2; // Simplified
  }

  /**
   * Legacy method - simplified for compatibility
   */
  private selectBestFit(results: CurveFitResult[]): CurveFitResult | null {
    if (results.length === 0) return null;

    console.log('🎯 Selecting best fit from results:', results.map(r => ({
      complexity: r.complexity,
      rSquared: r.rSquared.toFixed(3),
      residualError: r.residualError.toFixed(3)
    })));

    // Define preference weights for different complexity classes
    // Heavily favor common algorithm patterns
    const complexityWeights: Record<ComplexityCategory, number> = {
      constant: 0.9,    // Very common for optimized algorithms
      linear: 1.0,      // Most common, especially for hash-based algorithms like twoSum
      logarithmic: 0.8, // Common in tree operations
      linearithmic: 0.95, // Very common in sorting algorithms
      quadratic: 0.7,   // Common but often suboptimal
      cubic: 0.3,       // Less common
      exponential: 0.05, // Extremely rare in practice
      factorial: 0.01,  // Almost never correct
    };

    // Score each result based on R-squared, residual error, and complexity preference
    const scoredResults = results.map(result => {
      const baseScore = result.rSquared * 0.8 - result.residualError * 0.15; // Increased R-squared weight
      const complexityWeight = complexityWeights[result.complexity] || 0.5;
      const weightedScore = baseScore * complexityWeight;

      return {
        ...result,
        baseScore,
        complexityWeight,
        score: weightedScore,
      };
    });

    // Sort by score (higher is better)
    scoredResults.sort((a, b) => b.score - a.score);

    // Enhanced logic for common algorithm patterns
    const linearResult = scoredResults.find(r => r.complexity === 'linear');
    const quadraticResult = scoredResults.find(r => r.complexity === 'quadratic');
    const constantResult = scoredResults.find(r => r.complexity === 'constant');
    const linearithmicResult = scoredResults.find(r => r.complexity === 'linearithmic');

    // For hash-based algorithms like twoSum, strongly prefer linear
    if (linearResult && linearResult.rSquared > 0.15) {
      const bestResult = scoredResults[0];

      // If linear has decent fit and best result is much more complex, prefer linear
      if (bestResult.complexity === 'quadratic' || bestResult.complexity === 'cubic') {
        const scoreDiff = Math.abs(linearResult.baseScore - bestResult.baseScore);
        if (scoreDiff < 0.25) {
          console.log('🔄 Preferring linear over complex polynomial - likely hash-based algorithm');
          return linearResult;
        }
      }
    }

    // Preference for linear over quadratic when scores are close (very common case)
    if (linearResult && quadraticResult) {
      const scoreDiff = Math.abs(linearResult.baseScore - quadraticResult.baseScore);
      if (scoreDiff < 0.2 && linearResult.rSquared > 0.1) {
        console.log('🔄 Preferring linear over quadratic - more common algorithm pattern');
        return linearResult;
      }
    }

    // Preference for linearithmic over quadratic (sorting vs nested loops)
    if (linearithmicResult && quadraticResult) {
      const scoreDiff = Math.abs(linearithmicResult.baseScore - quadraticResult.baseScore);
      if (scoreDiff < 0.15 && linearithmicResult.rSquared > 0.15) {
        console.log('🔄 Preferring linearithmic over quadratic - likely sorting algorithm');
        return linearithmicResult;
      }
    }

    // Preference for constant over linear when they're very close (for optimized algorithms)
    if (constantResult && linearResult) {
      const scoreDiff = Math.abs(constantResult.baseScore - linearResult.baseScore);
      if (scoreDiff < 0.1 && constantResult.rSquared > 0.4) {
        console.log('🔄 Preferring constant over linear due to very close scores');
        return constantResult;
      }
    }

    const bestResult = scoredResults[0];
    console.log('🏆 Best scored result:', {
      complexity: bestResult.complexity,
      rSquared: bestResult.rSquared.toFixed(3),
      residualError: bestResult.residualError.toFixed(3),
      baseScore: bestResult.baseScore.toFixed(3),
      complexityWeight: bestResult.complexityWeight.toFixed(2),
      finalScore: bestResult.score.toFixed(3)
    });

    // More lenient thresholds for noisy timing data, but still filter out obviously bad fits
    if (bestResult.rSquared > 0.01 && bestResult.residualError < 5.0) {
      return bestResult;
    }

    // Special case: if the best result is factorial or exponential with low confidence,
    // try to find a more reasonable alternative
    if ((bestResult.complexity === 'factorial' || bestResult.complexity === 'exponential') &&
        bestResult.rSquared < 0.3) {
      const reasonableAlternative = scoredResults.find(r =>
        ['linear', 'quadratic', 'linearithmic', 'constant'].includes(r.complexity) &&
        r.rSquared > 0.05
      );

      if (reasonableAlternative) {
        console.log('🔄 Using reasonable alternative instead of unlikely complexity');
        return reasonableAlternative;
      }
    }

    // If no result meets the threshold, return the best one anyway with a warning
    console.warn('⚠️ No result meets quality threshold, returning best available');
    return bestResult;
  }

  /**
   * Calculates confidence based on fit quality
   */
  private calculateFitConfidence(
    fitResult: CurveFitResult,
    dataPoints: Array<{ x: number; y: number }>
  ): number {
    // Base confidence on R-squared
    let confidence = fitResult.rSquared;

    // Adjust for residual error
    confidence *= (1 - fitResult.residualError);

    // Adjust for number of data points (more points = higher confidence)
    const dataPointsFactor = Math.min(dataPoints.length / 10, 1);
    confidence *= (0.5 + 0.5 * dataPointsFactor);

    // Adjust for data quality (check for outliers)
    const outlierPenalty = this.calculateOutlierPenalty(dataPoints);
    confidence *= (1 - outlierPenalty);

    return Math.max(0, Math.min(1, confidence));
  }

  /**
   * Calculates penalty for outliers in the data
   */
  private calculateOutlierPenalty(dataPoints: Array<{ x: number; y: number }>): number {
    if (dataPoints.length < 3) return 0;

    const yValues = dataPoints.map(p => p.y);
    const mean = yValues.reduce((sum, y) => sum + y, 0) / yValues.length;
    const stdDev = Math.sqrt(
      yValues.reduce((sum, y) => sum + Math.pow(y - mean, 2), 0) / yValues.length
    );

    if (stdDev === 0) return 0;

    // Count outliers (more than 2 standard deviations from mean)
    const outliers = yValues.filter(y => Math.abs(y - mean) > 2 * stdDev);
    const outlierRatio = outliers.length / yValues.length;

    return Math.min(outlierRatio * 0.5, 0.3); // Cap penalty at 30%
  }

  /**
   * Generates alternative classifications
   */
  private generateAlternativeClassifications(
    allResults: CurveFitResult[],
    bestFit: CurveFitResult
  ): Array<{ complexity: ComplexityClass; confidence: number; reason: string }> {
    const alternatives: Array<{ complexity: ComplexityClass; confidence: number; reason: string }> = [];

    // Sort results by score, excluding the best fit
    const otherResults = allResults
      .filter(r => r.complexity !== bestFit.complexity)
      .sort((a, b) => b.rSquared - a.rSquared)
      .slice(0, 3); // Top 3 alternatives

    for (const result of otherResults) {
      if (result.rSquared > 0.2) { // Only include reasonable alternatives
        const confidence = this.calculateFitConfidence(result, []);
        const reason = this.generateAlternativeReason(result, bestFit);

        alternatives.push({
          complexity: COMPLEXITY_CLASSES[result.complexity],
          confidence,
          reason,
        });
      }
    }

    return alternatives;
  }

  /**
   * Generates explanation for alternative classification
   */
  private generateAlternativeReason(alternative: CurveFitResult, bestFit: CurveFitResult): string {
    const rSquaredDiff = bestFit.rSquared - alternative.rSquared;

    if (rSquaredDiff < 0.1) {
      return `Very close fit (R² = ${alternative.rSquared.toFixed(3)}) - could be ${alternative.complexity}`;
    } else if (rSquaredDiff < 0.2) {
      return `Reasonable alternative (R² = ${alternative.rSquared.toFixed(3)})`;
    } else {
      return `Lower confidence fit (R² = ${alternative.rSquared.toFixed(3)})`;
    }
  }

  /**
   * Gets the mathematical function for a complexity category
   */
  private getComplexityFunction(category: ComplexityCategory): (x: number) => number {
    switch (category) {
      case 'constant': return () => 1;
      case 'logarithmic': return (x) => Math.log(x);
      case 'linear': return (x) => x;
      case 'linearithmic': return (x) => x * Math.log(x);
      case 'quadratic': return (x) => x * x;
      case 'cubic': return (x) => x * x * x;
      case 'exponential': return (x) => Math.pow(2, Math.min(x / 100, 20));
      case 'factorial': return (x) => this.approximateFactorial(Math.min(x, 15));
      default: return (x) => x;
    }
  }

  /**
   * Validates if a curve fit is reasonable for the given complexity category
   */
  private isValidFit(
    fitResult: CurveFitResult & { rSquared: number; coefficients: number[]; equation: string; residualError: number },
    category: ComplexityCategory,
    dataPoints: Array<{ x: number; y: number }>
  ): boolean {
    // More lenient basic quality thresholds for noisy real-world data
    if (fitResult.rSquared < 0.01 || fitResult.residualError > 5.0) {
      return false;
    }

    // Check for reasonable coefficient values
    const [intercept, slope] = fitResult.coefficients;

    // Reject fits with negative slopes for growth functions
    if (slope < 0 && category !== 'constant') {
      return false;
    }

    // Since we removed exponential and factorial from the fitting functions,
    // this validation is no longer needed, but keeping for future reference
    if (category === 'exponential' || category === 'factorial') {
      // These categories should not appear since we removed them from complexityFunctions
      console.log(`❌ ${category} rejected: not supported in current implementation`);
      return false;
    }

    // Enhanced validation for linear functions (common in hash-based algorithms)
    if (category === 'linear') {
      const sortedPoints = [...dataPoints].sort((a, b) => a.x - b.x);
      if (sortedPoints.length >= 2) {
        const sizeRatio = sortedPoints[sortedPoints.length - 1].x / Math.max(sortedPoints[0].x, 1);
        const timeRatio = sortedPoints[sortedPoints.length - 1].y / Math.max(sortedPoints[0].y, 1);

        // For linear complexity, time ratio should be roughly proportional to size ratio
        // Allow some variance for noisy measurements
        if (timeRatio > sizeRatio * 5) {
          // Time grows much faster than linear - probably not linear
          console.log(`❌ Linear rejected: time ratio ${timeRatio.toFixed(2)} > size ratio ${sizeRatio.toFixed(2)} * 5`);
          return false;
        }

        // Also check that it's not too slow (constant-like)
        if (timeRatio < 1.1 && sizeRatio > 2) {
          // Time doesn't grow at all - probably constant, not linear
          console.log(`❌ Linear rejected: time ratio ${timeRatio.toFixed(2)} too small for size ratio ${sizeRatio.toFixed(2)}`);
          return false;
        }
      }
    }

    // Validation for quadratic functions
    if (category === 'quadratic') {
      const sortedPoints = [...dataPoints].sort((a, b) => a.x - b.x);
      if (sortedPoints.length >= 2) {
        const sizeRatio = sortedPoints[sortedPoints.length - 1].x / Math.max(sortedPoints[0].x, 1);
        const timeRatio = sortedPoints[sortedPoints.length - 1].y / Math.max(sortedPoints[0].y, 1);

        // For quadratic complexity, time should grow roughly as square of size
        const expectedQuadraticRatio = Math.pow(sizeRatio, 2);

        // Allow significant variance but reject if growth is too linear
        if (timeRatio < expectedQuadraticRatio * 0.1) {
          console.log(`❌ Quadratic rejected: time ratio ${timeRatio.toFixed(2)} too small for quadratic growth`);
          return false;
        }
      }
    }

    // Validation for constant functions
    if (category === 'constant') {
      const sortedPoints = [...dataPoints].sort((a, b) => a.x - b.x);
      if (sortedPoints.length >= 2) {
        const sizeRatio = sortedPoints[sortedPoints.length - 1].x / Math.max(sortedPoints[0].x, 1);
        const timeRatio = sortedPoints[sortedPoints.length - 1].y / Math.max(sortedPoints[0].y, 1);

        // For constant complexity, time should not grow significantly with size
        if (timeRatio > 2 && sizeRatio > 2) {
          console.log(`❌ Constant rejected: time ratio ${timeRatio.toFixed(2)} too large for constant complexity`);
          return false;
        }
      }
    }

    return true;
  }

  /**
   * Approximates factorial for large numbers to prevent overflow
   */
  private approximateFactorial(n: number): number {
    if (n <= 1) return 1;
    if (n > 10) return Infinity; // More conservative to prevent overflow

    let result = 1;
    for (let i = 2; i <= n; i++) {
      result *= i;
      if (result > 1e10) return Infinity; // Prevent excessive values
    }
    return result;
  }
}

/**
 * Advanced complexity analysis utilities
 */
export class ComplexityAnalysisUtils {
  /**
   * Validates measurement data quality for classification
   */
  static validateMeasurementQuality(measurements: PerformanceMeasurement[]): {
    isValid: boolean;
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];

    if (measurements.length < 3) {
      issues.push('Insufficient measurements for reliable classification');
      recommendations.push('Collect at least 5-8 measurements across different input sizes');
    }

    // Check for monotonic growth (expected for most algorithms)
    const sortedMeasurements = [...measurements].sort((a, b) => a.inputSize - b.inputSize);
    let nonMonotonicCount = 0;

    for (let i = 1; i < sortedMeasurements.length; i++) {
      if (sortedMeasurements[i].executionTime < sortedMeasurements[i - 1].executionTime) {
        nonMonotonicCount++;
      }
    }

    if (nonMonotonicCount > measurements.length * 0.3) {
      issues.push('Execution times do not show expected growth pattern');
      recommendations.push('Check for measurement noise or inconsistent test conditions');
    }

    // Check for extreme outliers
    const executionTimes = measurements.map(m => m.executionTime);
    const mean = executionTimes.reduce((sum, time) => sum + time, 0) / executionTimes.length;
    const stdDev = Math.sqrt(
      executionTimes.reduce((sum, time) => sum + Math.pow(time - mean, 2), 0) / executionTimes.length
    );

    const outliers = executionTimes.filter(time => Math.abs(time - mean) > 3 * stdDev);
    if (outliers.length > 0) {
      issues.push(`${outliers.length} extreme outliers detected`);
      recommendations.push('Consider removing outliers or increasing measurement iterations');
    }

    // Check input size distribution
    const inputSizes = measurements.map(m => m.inputSize).sort((a, b) => a - b);
    const sizeRange = inputSizes[inputSizes.length - 1] / inputSizes[0];

    if (sizeRange < 10) {
      issues.push('Limited input size range may affect classification accuracy');
      recommendations.push('Test with a wider range of input sizes (at least 10x difference)');
    }

    return {
      isValid: issues.length === 0,
      issues,
      recommendations,
    };
  }

  /**
   * Suggests optimal input sizes for complexity analysis
   */
  static suggestOptimalInputSizes(
    baseSize: number,
    maxSize: number,
    expectedComplexity?: ComplexityCategory
  ): number[] {
    const sizes: number[] = [];

    if (expectedComplexity === 'exponential' || expectedComplexity === 'factorial') {
      // For exponential/factorial, use small sizes with linear progression
      const step = Math.max(1, Math.floor((Math.min(maxSize, 20) - baseSize) / 6));
      for (let size = baseSize; size <= Math.min(maxSize, 20) && sizes.length < 8; size += step) {
        sizes.push(size);
      }
    } else if (expectedComplexity === 'quadratic' || expectedComplexity === 'cubic') {
      // For polynomial, use moderate sizes with careful progression
      const maxSafeSize = Math.min(maxSize, 1000);
      const ratio = Math.pow(maxSafeSize / baseSize, 1 / 7);

      for (let i = 0; i < 8; i++) {
        sizes.push(Math.round(baseSize * Math.pow(ratio, i)));
      }
    } else {
      // For linear and better complexities, use exponential progression
      const ratio = Math.pow(maxSize / baseSize, 1 / 9);

      for (let i = 0; i < 10; i++) {
        sizes.push(Math.round(baseSize * Math.pow(ratio, i)));
      }
    }

    // Remove duplicates and sort
    return [...new Set(sizes)].sort((a, b) => a - b);
  }

  /**
   * Compares two complexity classifications
   */
  static compareComplexities(
    complexity1: ComplexityClass,
    complexity2: ComplexityClass
  ): {
    better: ComplexityClass;
    comparison: string;
    significantDifference: boolean;
  } {
    const growth1 = complexity1.growthRate;
    const growth2 = complexity2.growthRate;

    const better = growth1 < growth2 ? complexity1 : complexity2;
    const significantDifference = Math.abs(growth1 - growth2) > 1;

    let comparison: string;
    if (growth1 === growth2) {
      comparison = `Both have ${complexity1.notation} complexity`;
    } else if (growth1 < growth2) {
      comparison = `${complexity1.notation} is more efficient than ${complexity2.notation}`;
    } else {
      comparison = `${complexity2.notation} is more efficient than ${complexity1.notation}`;
    }

    return {
      better,
      comparison,
      significantDifference,
    };
  }
}

/**
 * Factory function to create complexity classifier
 */
export function createComplexityClassifier(): ComplexityClassifier {
  return new ComplexityClassifierImpl();
}

/**
 * Default classifier instance
 */
export const defaultClassifier = new ComplexityClassifierImpl();
