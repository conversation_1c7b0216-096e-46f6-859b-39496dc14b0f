/**
 * Advanced curve fitting algorithms for complexity analysis
 * Implements sophisticated mathematical techniques for determining Big O complexity
 */

import { ComplexityCategory } from './types';

/**
 * Data point for curve fitting
 */
interface DataPoint {
  x: number;
  y: number;
  weight?: number; // Optional weighting for robust fitting
}

/**
 * Curve fitting result with statistical metrics
 */
interface CurveFitResult {
  complexity: ComplexityCategory;
  coefficients: number[];
  rSquared: number;
  adjustedRSquared: number;
  residualStandardError: number;
  fStatistic: number;
  pValue: number;
  confidenceInterval: [number, number];
  equation: string;
  predictions: number[];
}

/**
 * Advanced curve fitting engine
 */
export class CurveFittingEngine {
  private readonly CONVERGENCE_THRESHOLD = 1e-8;
  private readonly MAX_ITERATIONS = 1000;

  /**
   * Fits multiple complexity curves and returns ranked results
   */
  fitComplexityCurves(dataPoints: DataPoint[]): CurveFitResult[] {
    if (dataPoints.length < 3) {
      throw new Error('Need at least 3 data points for curve fitting');
    }

    const results: CurveFitResult[] = [];

    // Define complexity functions with their derivatives for optimization
    const complexityFunctions = this.getComplexityFunctions();

    for (const [category, funcDef] of Object.entries(complexityFunctions)) {
      try {
        const result = this.fitSingleCurve(
          dataPoints,
          funcDef.func,
          funcDef.derivative,
          category as ComplexityCategory
        );

        if (this.isValidFit(result)) {
          results.push(result);
        }
      } catch (error) {
        console.warn(`Failed to fit ${category} curve:`, error);
      }
    }

    // Sort by adjusted R-squared (better metric for model comparison)
    return results.sort((a, b) => b.adjustedRSquared - a.adjustedRSquared);
  }

  /**
   * Fits a single curve using non-linear least squares
   */
  private fitSingleCurve(
    dataPoints: DataPoint[],
    func: (x: number, params: number[]) => number,
    derivative: (x: number, params: number[]) => number[],
    complexity: ComplexityCategory
  ): CurveFitResult {
    // Initial parameter estimation
    let params = this.estimateInitialParameters(dataPoints, complexity);

    // Levenberg-Marquardt optimization
    params = this.levenbergMarquardt(dataPoints, func, derivative, params);

    // Calculate statistical metrics
    const predictions = dataPoints.map(p => func(p.x, params));
    const residuals = dataPoints.map((p, i) => p.y - predictions[i]);

    const rSquared = this.calculateRSquared(dataPoints, predictions);
    const adjustedRSquared = this.calculateAdjustedRSquared(rSquared, dataPoints.length, params.length);
    const residualStandardError = this.calculateResidualStandardError(residuals, params.length);
    const fStatistic = this.calculateFStatistic(rSquared, dataPoints.length, params.length);
    const pValue = this.calculatePValue(fStatistic, params.length - 1, dataPoints.length - params.length);
    const confidenceInterval = this.calculateConfidenceInterval(residuals, 0.95);

    return {
      complexity,
      coefficients: params,
      rSquared,
      adjustedRSquared,
      residualStandardError,
      fStatistic,
      pValue,
      confidenceInterval,
      equation: this.generateEquation(complexity, params),
      predictions,
    };
  }

  /**
   * Levenberg-Marquardt algorithm for non-linear optimization
   */
  private levenbergMarquardt(
    dataPoints: DataPoint[],
    func: (x: number, params: number[]) => number,
    derivative: (x: number, params: number[]) => number[],
    initialParams: number[]
  ): number[] {
    let params = [...initialParams];
    let lambda = 0.01;
    let prevError = Infinity;

    for (let iteration = 0; iteration < this.MAX_ITERATIONS; iteration++) {
      const { jacobian, residuals } = this.calculateJacobianAndResiduals(
        dataPoints, func, derivative, params
      );

      const currentError = residuals.reduce((sum, r) => sum + r * r, 0);

      if (Math.abs(prevError - currentError) < this.CONVERGENCE_THRESHOLD) {
        break;
      }

      // Calculate Hessian approximation (J^T * J)
      const hessian = this.multiplyMatrices(this.transposeMatrix(jacobian), jacobian);

      // Add damping factor to diagonal
      for (let i = 0; i < hessian.length; i++) {
        hessian[i][i] += lambda;
      }

      // Calculate gradient (J^T * residuals)
      const gradient = this.multiplyMatrixVector(this.transposeMatrix(jacobian), residuals);

      try {
        // Solve for parameter update
        const deltaParams = this.solveLinearSystem(hessian, gradient);
        const newParams = params.map((p, i) => p - deltaParams[i]);

        // Calculate new error
        const newResiduals = dataPoints.map(point =>
          point.y - func(point.x, newParams)
        );
        const newError = newResiduals.reduce((sum, r) => sum + r * r, 0);

        if (newError < currentError) {
          // Accept the update
          params = newParams;
          lambda *= 0.1;
          prevError = currentError;
        } else {
          // Reject the update, increase damping
          lambda *= 10;
        }
      } catch (error) {
        // If matrix is singular, increase damping and continue
        lambda *= 10;
      }
    }

    return params;
  }

  /**
   * Calculates Jacobian matrix and residuals
   */
  private calculateJacobianAndResiduals(
    dataPoints: DataPoint[],
    func: (x: number, params: number[]) => number,
    derivative: (x: number, params: number[]) => number[],
    params: number[]
  ): { jacobian: number[][]; residuals: number[] } {
    const jacobian: number[][] = [];
    const residuals: number[] = [];

    for (const point of dataPoints) {
      const predicted = func(point.x, params);
      residuals.push(point.y - predicted);
      jacobian.push(derivative(point.x, params));
    }

    return { jacobian, residuals };
  }

  /**
   * Gets complexity functions with their derivatives
   */
  private getComplexityFunctions(): Record<string, {
    func: (x: number, params: number[]) => number;
    derivative: (x: number, params: number[]) => number[];
  }> {
    return {
      constant: {
        func: (x: number, params: number[]) => params[0],
        derivative: (x: number, params: number[]) => [1],
      },

      logarithmic: {
        func: (x: number, params: number[]) => params[0] * Math.log(x) + params[1],
        derivative: (x: number, params: number[]) => [Math.log(x), 1],
      },

      linear: {
        func: (x: number, params: number[]) => params[0] * x + params[1],
        derivative: (x: number, params: number[]) => [x, 1],
      },

      linearithmic: {
        func: (x: number, params: number[]) => params[0] * x * Math.log(x) + params[1],
        derivative: (x: number, params: number[]) => [
          x * Math.log(x),
          1
        ],
      },

      quadratic: {
        func: (x: number, params: number[]) => params[0] * x * x + params[1] * x + params[2],
        derivative: (x: number, params: number[]) => [x * x, x, 1],
      },

      cubic: {
        func: (x: number, params: number[]) => params[0] * x * x * x + params[1] * x * x + params[2],
        derivative: (x: number, params: number[]) => [x * x * x, x * x, 1],
      },

      exponential: {
        func: (x: number, params: number[]) => params[0] * Math.exp(params[1] * x) + params[2],
        derivative: (x: number, params: number[]) => [
          Math.exp(params[1] * x),
          params[0] * x * Math.exp(params[1] * x),
          1
        ],
      },
    };
  }

  /**
   * Estimates initial parameters for optimization
   */
  private estimateInitialParameters(dataPoints: DataPoint[], complexity: ComplexityCategory): number[] {
    const n = dataPoints.length;
    const xValues = dataPoints.map(p => p.x);
    const yValues = dataPoints.map(p => p.y);

    const xMean = xValues.reduce((sum, x) => sum + x, 0) / n;
    const yMean = yValues.reduce((sum, y) => sum + y, 0) / n;

    switch (complexity) {
      case 'constant':
        return [yMean];

      case 'linear':
        // Simple linear regression for initial estimate
        const slope = this.calculateSlope(xValues, yValues);
        const intercept = yMean - slope * xMean;
        return [slope, intercept];

      case 'quadratic':
        // Use method of least squares for quadratic
        return this.estimateQuadraticParameters(dataPoints);

      case 'logarithmic':
        // Transform to linear in log(x)
        const logX = xValues.map(x => Math.log(Math.max(x, 1)));
        const logSlope = this.calculateSlope(logX, yValues);
        const logIntercept = yMean - logSlope * logX.reduce((sum, x) => sum + x, 0) / n;
        return [logSlope, logIntercept];

      case 'exponential':
        // Transform to linear in log(y)
        const logY = yValues.map(y => Math.log(Math.max(y, 1)));
        const expSlope = this.calculateSlope(xValues, logY);
        return [1, expSlope, 0];

      default:
        return [1, 0]; // Default parameters
    }
  }

  /**
   * Estimates quadratic parameters using least squares
   */
  private estimateQuadraticParameters(dataPoints: DataPoint[]): number[] {
    const n = dataPoints.length;

    // Set up normal equations for quadratic fit
    let sumX = 0, sumX2 = 0, sumX3 = 0, sumX4 = 0;
    let sumY = 0, sumXY = 0, sumX2Y = 0;

    for (const point of dataPoints) {
      const x = point.x;
      const y = point.y;
      const x2 = x * x;
      const x3 = x2 * x;
      const x4 = x2 * x2;

      sumX += x;
      sumX2 += x2;
      sumX3 += x3;
      sumX4 += x4;
      sumY += y;
      sumXY += x * y;
      sumX2Y += x2 * y;
    }

    // Solve 3x3 system for quadratic coefficients
    const matrix = [
      [sumX4, sumX3, sumX2],
      [sumX3, sumX2, sumX],
      [sumX2, sumX, n]
    ];

    const vector = [sumX2Y, sumXY, sumY];

    try {
      return this.solveLinearSystem(matrix, vector);
    } catch {
      // Fallback to simpler estimation
      return [0.001, 0.1, 1];
    }
  }

  /**
   * Calculates slope for simple linear regression
   */
  private calculateSlope(xValues: number[], yValues: number[]): number {
    const n = xValues.length;
    const xMean = xValues.reduce((sum, x) => sum + x, 0) / n;
    const yMean = yValues.reduce((sum, y) => sum + y, 0) / n;

    let numerator = 0;
    let denominator = 0;

    for (let i = 0; i < n; i++) {
      numerator += (xValues[i] - xMean) * (yValues[i] - yMean);
      denominator += (xValues[i] - xMean) * (xValues[i] - xMean);
    }

    return denominator !== 0 ? numerator / denominator : 0;
  }

  /**
   * Calculates R-squared coefficient of determination
   */
  private calculateRSquared(dataPoints: DataPoint[], predictions: number[]): number {
    const yMean = dataPoints.reduce((sum, p) => sum + p.y, 0) / dataPoints.length;

    let totalSumSquares = 0;
    let residualSumSquares = 0;

    for (let i = 0; i < dataPoints.length; i++) {
      totalSumSquares += Math.pow(dataPoints[i].y - yMean, 2);
      residualSumSquares += Math.pow(dataPoints[i].y - predictions[i], 2);
    }

    return totalSumSquares > 0 ? 1 - (residualSumSquares / totalSumSquares) : 0;
  }

  /**
   * Calculates adjusted R-squared
   */
  private calculateAdjustedRSquared(rSquared: number, n: number, p: number): number {
    if (n <= p) return 0;
    return 1 - ((1 - rSquared) * (n - 1)) / (n - p - 1);
  }

  /**
   * Calculates residual standard error
   */
  private calculateResidualStandardError(residuals: number[], numParams: number): number {
    const sumSquaredResiduals = residuals.reduce((sum, r) => sum + r * r, 0);
    const degreesOfFreedom = residuals.length - numParams;
    return degreesOfFreedom > 0 ? Math.sqrt(sumSquaredResiduals / degreesOfFreedom) : Infinity;
  }

  /**
   * Calculates F-statistic for model significance
   */
  private calculateFStatistic(rSquared: number, n: number, p: number): number {
    if (rSquared >= 1 || n <= p) return 0;
    return (rSquared / (p - 1)) / ((1 - rSquared) / (n - p));
  }

  /**
   * Calculates p-value using F-distribution approximation
   */
  private calculatePValue(fStatistic: number, df1: number, df2: number): number {
    // Simplified p-value calculation (would use proper F-distribution in production)
    if (fStatistic < 1) return 1;

    // Rough approximation - in practice, would use proper statistical library
    const criticalValue = 3.84; // Approximate F(1, ∞) at α = 0.05
    return fStatistic > criticalValue ? 0.01 : 0.1;
  }

  /**
   * Calculates confidence interval for predictions
   */
  private calculateConfidenceInterval(residuals: number[], confidence: number): [number, number] {
    const mean = residuals.reduce((sum, r) => sum + r, 0) / residuals.length;
    const stdDev = Math.sqrt(
      residuals.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / (residuals.length - 1)
    );

    // Use t-distribution critical value (approximated)
    const tValue = 2.0; // Approximate t-value for 95% confidence
    const margin = tValue * stdDev / Math.sqrt(residuals.length);

    return [mean - margin, mean + margin];
  }

  /**
   * Generates human-readable equation string
   */
  private generateEquation(complexity: ComplexityCategory, params: number[]): string {
    const formatCoeff = (coeff: number) => coeff.toFixed(4);

    switch (complexity) {
      case 'constant':
        return `y = ${formatCoeff(params[0])}`;

      case 'linear':
        return `y = ${formatCoeff(params[0])} * x + ${formatCoeff(params[1])}`;

      case 'quadratic':
        return `y = ${formatCoeff(params[0])} * x² + ${formatCoeff(params[1])} * x + ${formatCoeff(params[2])}`;

      case 'cubic':
        return `y = ${formatCoeff(params[0])} * x³ + ${formatCoeff(params[1])} * x² + ${formatCoeff(params[2])}`;

      case 'logarithmic':
        return `y = ${formatCoeff(params[0])} * log(x) + ${formatCoeff(params[1])}`;

      case 'linearithmic':
        return `y = ${formatCoeff(params[0])} * x * log(x) + ${formatCoeff(params[1])}`;

      case 'exponential':
        return `y = ${formatCoeff(params[0])} * exp(${formatCoeff(params[1])} * x) + ${formatCoeff(params[2])}`;

      default:
        return 'Unknown equation';
    }
  }

  /**
   * Validates if a curve fit is reasonable
   */
  private isValidFit(result: CurveFitResult): boolean {
    return (
      result.rSquared > 0.1 &&
      result.adjustedRSquared > 0.05 &&
      !result.coefficients.some(coeff => !isFinite(coeff)) &&
      result.residualStandardError < 1000
    );
  }

  // Matrix operations for linear algebra

  private multiplyMatrices(a: number[][], b: number[][]): number[][] {
    const result: number[][] = [];
    for (let i = 0; i < a.length; i++) {
      result[i] = [];
      for (let j = 0; j < b[0].length; j++) {
        let sum = 0;
        for (let k = 0; k < b.length; k++) {
          sum += a[i][k] * b[k][j];
        }
        result[i][j] = sum;
      }
    }
    return result;
  }

  private multiplyMatrixVector(matrix: number[][], vector: number[]): number[] {
    return matrix.map(row =>
      row.reduce((sum, val, i) => sum + val * vector[i], 0)
    );
  }

  private transposeMatrix(matrix: number[][]): number[][] {
    return matrix[0].map((_, colIndex) => matrix.map(row => row[colIndex]));
  }

  private solveLinearSystem(matrix: number[][], vector: number[]): number[] {
    // Gaussian elimination with partial pivoting
    const n = matrix.length;
    const augmented = matrix.map((row, i) => [...row, vector[i]]);

    // Forward elimination
    for (let i = 0; i < n; i++) {
      // Find pivot
      let maxRow = i;
      for (let k = i + 1; k < n; k++) {
        if (Math.abs(augmented[k][i]) > Math.abs(augmented[maxRow][i])) {
          maxRow = k;
        }
      }

      // Swap rows
      [augmented[i], augmented[maxRow]] = [augmented[maxRow], augmented[i]];

      // Check for singular matrix
      if (Math.abs(augmented[i][i]) < 1e-10) {
        throw new Error('Matrix is singular');
      }

      // Eliminate column
      for (let k = i + 1; k < n; k++) {
        const factor = augmented[k][i] / augmented[i][i];
        for (let j = i; j <= n; j++) {
          augmented[k][j] -= factor * augmented[i][j];
        }
      }
    }

    // Back substitution
    const solution = new Array(n);
    for (let i = n - 1; i >= 0; i--) {
      solution[i] = augmented[i][n];
      for (let j = i + 1; j < n; j++) {
        solution[i] -= augmented[i][j] * solution[j];
      }
      solution[i] /= augmented[i][i];
    }

    return solution;
  }
}

/**
 * Robust curve fitting with outlier detection
 */
export class RobustCurveFitting extends CurveFittingEngine {
  /**
   * Fits curves with automatic outlier detection and removal
   */
  fitRobustCurves(dataPoints: DataPoint[]): CurveFitResult[] {
    // First pass - identify potential outliers
    const cleanedPoints = this.removeOutliers(dataPoints);

    // Second pass - fit curves on cleaned data
    const results = this.fitComplexityCurves(cleanedPoints);

    // Third pass - validate results against original data
    return this.validateAgainstOriginalData(results, dataPoints);
  }

  /**
   * Removes statistical outliers from data points
   */
  private removeOutliers(dataPoints: DataPoint[]): DataPoint[] {
    if (dataPoints.length < 5) return dataPoints; // Need enough points

    const yValues = dataPoints.map(p => p.y);
    const q1 = this.quantile(yValues, 0.25);
    const q3 = this.quantile(yValues, 0.75);
    const iqr = q3 - q1;

    const lowerBound = q1 - 1.5 * iqr;
    const upperBound = q3 + 1.5 * iqr;

    return dataPoints.filter(p => p.y >= lowerBound && p.y <= upperBound);
  }

  /**
   * Calculates quantile of an array
   */
  private quantile(arr: number[], q: number): number {
    const sorted = [...arr].sort((a, b) => a - b);
    const index = q * (sorted.length - 1);
    const lower = Math.floor(index);
    const upper = Math.ceil(index);

    if (lower === upper) {
      return sorted[lower];
    }

    const weight = index - lower;
    return sorted[lower] * (1 - weight) + sorted[upper] * weight;
  }

  /**
   * Validates curve fit results against original data
   */
  private validateAgainstOriginalData(
    results: CurveFitResult[],
    originalData: DataPoint[]
  ): CurveFitResult[] {
    return results.map(result => {
      // Recalculate metrics using original data
      const functions = this.getComplexityFunctions();
      const func = functions[result.complexity]?.func;

      if (func) {
        const predictions = originalData.map(p => func(p.x, result.coefficients));
        const rSquared = this.calculateRSquared(originalData, predictions);

        return {
          ...result,
          rSquared,
          adjustedRSquared: this.calculateAdjustedRSquared(
            rSquared,
            originalData.length,
            result.coefficients.length
          ),
        };
      }

      return result;
    });
  }
}

/**
 * Export curve fitting utilities
 */
export const curveFitting = {
  engine: new CurveFittingEngine(),
  robust: new RobustCurveFitting(),
};
