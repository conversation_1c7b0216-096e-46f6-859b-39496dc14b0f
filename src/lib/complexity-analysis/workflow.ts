/**
 * Complexity analysis workflow orchestrator
 * Manages the complete analysis process with error handling and resource management
 */

import {
  ComplexityResult,
  AnalysisOptions,
  PerformanceMeasurement,
  DEFAULT_ANALYSIS_OPTIONS,
  AnalysisError,
} from './types';

import { ComplexityAnalysisControllerImpl } from './controller';
import { ErrorUtils, PerformanceUtils } from './utils';
import { InputGenerationUtils } from './input-generator';

/**
 * Analysis workflow state
 */
interface WorkflowState {
  phase: 'initializing' | 'generating-inputs' | 'profiling' | 'classifying' | 'completed' | 'error';
  progress: number; // 0-100
  currentStep: string;
  startTime: number;
  estimatedTimeRemaining?: number;
}

/**
 * Workflow result with detailed metrics
 */
interface WorkflowResult {
  result: ComplexityResult;
  state: WorkflowState;
  metrics: {
    totalTime: number;
    inputGenerationTime: number;
    profilingTime: number;
    classificationTime: number;
    measurementCount: number;
    successRate: number;
  };
  warnings: string[];
  errors: AnalysisError[];
}

/**
 * Advanced complexity analysis workflow
 */
export class ComplexityAnalysisWorkflow {
  private controller = new ComplexityAnalysisControllerImpl();
  private currentState: WorkflowState = {
    phase: 'initializing',
    progress: 0,
    currentStep: 'Initializing analysis',
    startTime: 0,
  };

  private onProgressUpdate?: (state: WorkflowState) => void;
  private onWarning?: (warning: string) => void;
  private onError?: (error: AnalysisError) => void;

  /**
   * Sets up workflow event handlers
   */
  setEventHandlers(handlers: {
    onProgress?: (state: WorkflowState) => void;
    onWarning?: (warning: string) => void;
    onError?: (error: AnalysisError) => void;
  }): void {
    this.onProgressUpdate = handlers.onProgress;
    this.onWarning = handlers.onWarning;
    this.onError = handlers.onError;
  }

  /**
   * Runs the complete complexity analysis workflow
   */
  async runCompleteAnalysis(
    code: string,
    language: string,
    baseInput?: any,
    options: Partial<AnalysisOptions> = {}
  ): Promise<WorkflowResult> {
    const startTime = performance.now();
    const analysisOptions: AnalysisOptions = { ...DEFAULT_ANALYSIS_OPTIONS, ...options };

    const warnings: string[] = [];
    const errors: AnalysisError[] = [];
    const metrics = {
      totalTime: 0,
      inputGenerationTime: 0,
      profilingTime: 0,
      classificationTime: 0,
      measurementCount: 0,
      successRate: 0,
    };

    // Initialize workflow state
    this.updateState({
      phase: 'initializing',
      progress: 0,
      currentStep: 'Validating inputs and preparing analysis',
      startTime,
    });

    try {
      // Phase 1: Input validation and preparation
      await this.validateAndPrepare(code, language, analysisOptions);

      // Phase 2: Generate test inputs
      const inputGenStart = performance.now();
      this.updateState({
        phase: 'generating-inputs',
        progress: 20,
        currentStep: 'Generating test inputs of different sizes',
      });

      const testInputs = await this.generateOptimalTestInputs(baseInput, analysisOptions);
      metrics.inputGenerationTime = performance.now() - inputGenStart;

      if (testInputs.length === 0) {
        throw ErrorUtils.createAnalysisError(
          'analysis-error',
          'Failed to generate test inputs',
          { baseInput, options: analysisOptions },
          false
        );
      }

      // Phase 3: Profile code execution
      const profilingStart = performance.now();
      this.updateState({
        phase: 'profiling',
        progress: 40,
        currentStep: `Profiling code execution with ${testInputs.length} different input sizes`,
      });

      const measurements = await this.profileWithRetries(
        code,
        language,
        testInputs,
        analysisOptions
      );

      metrics.profilingTime = performance.now() - profilingStart;
      metrics.measurementCount = measurements.length;
      metrics.successRate = measurements.length / testInputs.length;

      if (measurements.length === 0) {
        throw ErrorUtils.createAnalysisError(
          'execution-error',
          'No successful measurements obtained',
          { inputCount: testInputs.length },
          false
        );
      }

      // Phase 4: Classify complexity
      const classificationStart = performance.now();
      this.updateState({
        phase: 'classifying',
        progress: 80,
        currentStep: 'Analyzing performance patterns and classifying complexity',
      });

      const result = await this.performComplexityClassification(
        code,
        language,
        measurements,
        analysisOptions
      );

      metrics.classificationTime = performance.now() - classificationStart;

      // Phase 5: Complete analysis
      this.updateState({
        phase: 'completed',
        progress: 100,
        currentStep: 'Analysis completed successfully',
      });

      metrics.totalTime = performance.now() - startTime;

      // Generate warnings based on results
      this.generateAnalysisWarnings(result, metrics, warnings);

      return {
        result,
        state: this.currentState,
        metrics,
        warnings,
        errors,
      };

    } catch (error) {
      const analysisError = error instanceof Error
        ? ErrorUtils.createAnalysisError('analysis-error', error.message, { error }, false)
        : error as AnalysisError;

      errors.push(analysisError);

      this.updateState({
        phase: 'error',
        progress: 0,
        currentStep: `Analysis failed: ${analysisError.message}`,
      });

      if (this.onError) {
        this.onError(analysisError);
      }

      // Return partial result
      return {
        result: {
          id: `error_${Date.now()}`,
          timestamp: new Date(),
          code,
          language,
          timeComplexity: null,
          spaceComplexity: null,
          measurements: [],
          confidence: { time: 0, space: 0 },
          suggestions: [],
          analysisOptions,
          executionSuccess: false,
          errorMessage: analysisError.message,
        },
        state: this.currentState,
        metrics,
        warnings,
        errors,
      };
    }
  }

  /**
   * Runs a quick analysis with minimal overhead
   */
  async runQuickAnalysis(
    code: string,
    language: string,
    baseInput?: any
  ): Promise<ComplexityResult> {
    const quickOptions: AnalysisOptions = {
      enableTimeAnalysis: true,
      enableSpaceAnalysis: false, // Skip space analysis for speed
      inputSizes: [100, 500, 1000, 2000, 5000], // Use larger input sizes for better accuracy
      maxExecutionTime: 15000, // 15 second timeout
      iterations: 1, // Single iteration
    };

    const workflowResult = await this.runCompleteAnalysis(
      code,
      language,
      baseInput,
      quickOptions
    );

    return workflowResult.result;
  }

  /**
   * Runs analysis - alias for runQuickAnalysis for compatibility
   */
  async runAnalysis(
    code: string,
    language: string,
    baseInput?: any
  ): Promise<{ result: ComplexityResult }> {
    const result = await this.runQuickAnalysis(code, language, baseInput);
    return { result };
  }

  /**
   * Validates inputs and prepares for analysis
   */
  private async validateAndPrepare(
    code: string,
    language: string,
    options: AnalysisOptions
  ): Promise<void> {
    // Validate code
    if (!code || code.trim().length === 0) {
      throw ErrorUtils.createAnalysisError(
        'analysis-error',
        'Code cannot be empty',
        { code },
        false
      );
    }

    // Validate language
    const supportedLanguages = ['javascript', 'typescript', 'python'];
    if (!supportedLanguages.includes(language.toLowerCase())) {
      throw ErrorUtils.createAnalysisError(
        'analysis-error',
        `Unsupported language: ${language}`,
        { language, supported: supportedLanguages },
        false
      );
    }

    // Validate options
    if (options.inputSizes.length < 3) {
      throw ErrorUtils.createAnalysisError(
        'analysis-error',
        'Need at least 3 input sizes for reliable analysis',
        { inputSizes: options.inputSizes },
        true
      );
    }

    // Check for potential infinite loops or problematic patterns
    await this.performStaticAnalysis(code, language);
  }

  /**
   * Performs basic static analysis to detect potential issues
   */
  private async performStaticAnalysis(code: string, language: string): Promise<void> {
    const warnings: string[] = [];

    // Check for infinite loop patterns
    const infiniteLoopPatterns = [
      /while\s*\(\s*true\s*\)/g, // while(true)
      /for\s*\(\s*;\s*;\s*\)/g,  // for(;;)
    ];

    for (const pattern of infiniteLoopPatterns) {
      if (pattern.test(code)) {
        warnings.push('Potential infinite loop detected - analysis may timeout');
      }
    }

    // Check for recursive patterns without base cases
    if (language === 'javascript' || language === 'typescript') {
      const functionName = code.match(/function\s+(\w+)/)?.[1];
      if (functionName && code.includes(functionName) && !code.includes('return')) {
        warnings.push('Recursive function without clear base case detected');
      }
    }

    // Emit warnings
    warnings.forEach(warning => {
      if (this.onWarning) {
        this.onWarning(warning);
      }
    });
  }

  /**
   * Generates optimal test inputs for analysis
   */
  private async generateOptimalTestInputs(
    baseInput: any,
    options: AnalysisOptions
  ): Promise<any[]> {
    try {
      // Use smart input generation if base input is provided
      if (baseInput !== undefined) {
        const { inputs } = await import('./pattern-detection').then(module =>
          module.patternDetection.generateSmart(baseInput, options.inputSizes)
        );
        return inputs;
      }

      // Generate default inputs for common patterns
      const defaultInputs = options.inputSizes.map(size => {
        // Generate array inputs by default
        return Array.from({ length: size }, (_, i) => i + 1);
      });

      return defaultInputs;

    } catch (error) {
      // Fallback to simple input generation
      return options.inputSizes.map(size =>
        Array.from({ length: size }, (_, i) => i + 1)
      );
    }
  }

  /**
   * Profiles code execution with retry logic
   */
  private async profileWithRetries(
    code: string,
    language: string,
    testInputs: any[],
    options: AnalysisOptions
  ): Promise<PerformanceMeasurement[]> {
    const measurements: PerformanceMeasurement[] = [];
    const maxRetries = 2;

    for (let i = 0; i < testInputs.length; i++) {
      const input = testInputs[i];
      let attempts = 0;
      let success = false;

      // Update progress
      const progress = 40 + (40 * i / testInputs.length);
      this.updateState({
        phase: 'profiling',
        progress,
        currentStep: `Profiling input ${i + 1}/${testInputs.length} (size: ${this.getInputSize(input)})`,
      });

      while (attempts < maxRetries && !success) {
        try {
          // Profile execution with timeout
          const measurement = await PerformanceUtils.withTimeout(
            () => this.profileSingleInput(code, language, input, options),
            options.maxExecutionTime
          );

          measurements.push(measurement);
          success = true;

        } catch (error) {
          attempts++;

          if (attempts >= maxRetries) {
            const warning = `Failed to profile input size ${this.getInputSize(input)} after ${maxRetries} attempts`;
            if (this.onWarning) {
              this.onWarning(warning);
            }
          }
        }
      }
    }

    return measurements;
  }

  /**
   * Profiles a single input
   */
  private async profileSingleInput(
    code: string,
    language: string,
    input: any,
    options: AnalysisOptions
  ): Promise<PerformanceMeasurement> {
    // This would integrate with the profiler
    const profiler = await import('./profiler').then(module => module.createComplexityProfiler());

    const results = await profiler.profileExecution(code, [input], language);

    if (results.length === 0) {
      throw new Error('No profiling results obtained');
    }

    return results[0];
  }

  /**
   * Performs complexity classification
   */
  private async performComplexityClassification(
    code: string,
    language: string,
    measurements: PerformanceMeasurement[],
    options: AnalysisOptions
  ): Promise<ComplexityResult> {
    // Use the controller to perform classification
    const result = await this.controller.analyzeCode(code, language, options);

    // Override measurements with our profiled data
    result.measurements = measurements;

    // Reclassify based on actual measurements
    const { time, space, confidence } = this.controller.classifyComplexity(measurements);
    result.timeComplexity = time;
    result.spaceComplexity = space;
    result.confidence = confidence;

    return result;
  }

  /**
   * Generates analysis warnings based on results
   */
  private generateAnalysisWarnings(
    result: ComplexityResult,
    metrics: any,
    warnings: string[]
  ): void {
    // Low confidence warnings
    if (result.confidence.time < 0.6) {
      warnings.push('Time complexity analysis has low confidence - results may be unreliable');
    }

    if (result.confidence.space < 0.6) {
      warnings.push('Space complexity analysis has low confidence - results may be unreliable');
    }

    // Performance warnings
    if (metrics.successRate < 0.7) {
      warnings.push(`Only ${Math.round(metrics.successRate * 100)}% of measurements succeeded - consider simplifying the code or reducing input sizes`);
    }

    // Complexity warnings
    if (result.timeComplexity?.efficiency === 'terrible') {
      warnings.push(`${result.timeComplexity.notation} time complexity is very inefficient - strongly consider optimization`);
    }

    // Measurement quality warnings
    if (result.measurements.length < 5) {
      warnings.push('Few successful measurements - analysis reliability may be reduced');
    }
  }

  /**
   * Updates workflow state and notifies listeners
   */
  private updateState(updates: Partial<WorkflowState>): void {
    this.currentState = { ...this.currentState, ...updates };

    // Estimate time remaining
    if (this.currentState.progress > 0 && this.currentState.progress < 100) {
      const elapsed = performance.now() - this.currentState.startTime;
      const estimatedTotal = elapsed / (this.currentState.progress / 100);
      this.currentState.estimatedTimeRemaining = estimatedTotal - elapsed;
    }

    if (this.onProgressUpdate) {
      this.onProgressUpdate(this.currentState);
    }
  }

  /**
   * Gets the size of an input for display purposes
   */
  private getInputSize(input: any): number {
    if (Array.isArray(input)) {
      return input.length;
    } else if (typeof input === 'string') {
      return input.length;
    } else if (typeof input === 'number') {
      return input;
    } else if (typeof input === 'object' && input !== null) {
      return Object.keys(input).length;
    }
    return 1;
  }

  /**
   * Gets current workflow state
   */
  getCurrentState(): WorkflowState {
    return { ...this.currentState };
  }

  /**
   * Cancels the current analysis
   */
  cancel(): void {
    this.updateState({
      phase: 'error',
      currentStep: 'Analysis cancelled by user',
    });
  }
}

/**
 * Batch analysis workflow for multiple algorithms
 */
export class BatchComplexityAnalysis {
  private workflow = new ComplexityAnalysisWorkflow();

  /**
   * Analyzes multiple code implementations
   */
  async analyzeBatch(
    implementations: Array<{
      name: string;
      code: string;
      language: string;
    }>,
    baseInput?: any,
    options?: Partial<AnalysisOptions>
  ): Promise<{
    results: Array<ComplexityResult & { name: string }>;
    comparison: any;
    summary: string;
  }> {
    const results: Array<ComplexityResult & { name: string }> = [];

    // Analyze each implementation
    for (const impl of implementations) {
      try {
        const result = await this.workflow.runQuickAnalysis(
          impl.code,
          impl.language,
          baseInput
        );

        results.push({
          ...result,
          name: impl.name,
        });
      } catch (error) {
        console.warn(`Failed to analyze ${impl.name}:`, error);
      }
    }

    // Compare results
    const comparison = results.length > 1
      ? this.compareImplementations(results)
      : null;

    // Generate summary
    const summary = this.generateBatchSummary(results, comparison);

    return {
      results,
      comparison,
      summary,
    };
  }

  /**
   * Compares multiple implementations
   */
  private compareImplementations(results: Array<ComplexityResult & { name: string }>): any {
    // Find best time and space complexity
    const timeWinner = results.reduce((best, current) => {
      if (!best.timeComplexity || !current.timeComplexity) return best;
      return current.timeComplexity.growthRate < best.timeComplexity.growthRate ? current : best;
    });

    const spaceWinner = results.reduce((best, current) => {
      if (!best.spaceComplexity || !current.spaceComplexity) return best;
      return current.spaceComplexity.growthRate < best.spaceComplexity.growthRate ? current : best;
    });

    return {
      timeWinner: timeWinner.name,
      spaceWinner: spaceWinner.name,
      results: results.map(r => ({
        name: r.name,
        timeComplexity: r.timeComplexity?.notation || 'Unknown',
        spaceComplexity: r.spaceComplexity?.notation || 'Unknown',
        timeConfidence: r.confidence.time,
        spaceConfidence: r.confidence.space,
      })),
    };
  }

  /**
   * Generates batch analysis summary
   */
  private generateBatchSummary(
    results: Array<ComplexityResult & { name: string }>,
    comparison: any
  ): string {
    if (results.length === 0) {
      return 'No successful analyses completed.';
    }

    if (results.length === 1) {
      const result = results[0];
      return `Analysis of ${result.name}: ${result.timeComplexity?.notation || 'Unknown'} time, ${result.spaceComplexity?.notation || 'Unknown'} space complexity.`;
    }

    let summary = `Analyzed ${results.length} implementations. `;

    if (comparison) {
      summary += `Best time complexity: ${comparison.timeWinner}. `;
      summary += `Best space complexity: ${comparison.spaceWinner}.`;
    }

    return summary;
  }
}

/**
 * Export workflow utilities
 */
export const complexityWorkflow = {
  single: new ComplexityAnalysisWorkflow(),
  batch: new BatchComplexityAnalysis(),
};
