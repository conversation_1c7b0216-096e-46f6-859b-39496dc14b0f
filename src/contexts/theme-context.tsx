'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

type ThemeType = 'light' | 'dark' | 'system';
type EditorThemeType = 'light' | 'vs-dark';

interface ThemeContextType {
  theme: ThemeType;
  editorTheme: EditorThemeType;
  setTheme: (theme: ThemeType) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setThemeState] = useState<ThemeType>(() => {
    // Get theme from localStorage or default to system
    if (typeof window !== 'undefined') {
      return (localStorage.getItem('theme') as ThemeType) || 'system';
    }
    return 'system';
  });

  const [editorTheme, setEditorTheme] = useState<EditorThemeType>('vs-dark');

  // Apply theme on mount and when theme changes
  useEffect(() => {
    applyTheme(theme);
  }, [theme]);

  // Listen for system theme changes
  useEffect(() => {
    if (theme === 'system') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleChange = () => applyTheme('system');

      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  }, [theme]);

  const applyTheme = (themeId: ThemeType) => {
    const root = document.documentElement;

    if (themeId === 'dark') {
      root.classList.add('dark');
      root.style.colorScheme = 'dark';
      setEditorTheme('vs-dark');
    } else if (themeId === 'light') {
      root.classList.remove('dark');
      root.style.colorScheme = 'light';
      setEditorTheme('light');
    } else {
      // System theme
      const prefersDark = window.matchMedia(
        '(prefers-color-scheme: dark)'
      ).matches;
      if (prefersDark) {
        root.classList.add('dark');
        root.style.colorScheme = 'dark';
        setEditorTheme('vs-dark');
      } else {
        root.classList.remove('dark');
        root.style.colorScheme = 'light';
        setEditorTheme('light');
      }
    }
  };

  const setTheme = (newTheme: ThemeType) => {
    setThemeState(newTheme);
    localStorage.setItem('theme', newTheme);
  };

  return (
    <ThemeContext.Provider value={{ theme, editorTheme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}
