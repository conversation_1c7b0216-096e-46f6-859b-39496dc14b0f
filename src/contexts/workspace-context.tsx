'use client';

import {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react';
import { usePathname } from 'next/navigation';
import { useAuth } from './auth-context';
import { WorkspaceService } from '@/lib/services/workspace';
import { FileService } from '@/lib/services/file';
import type { Workspace, File } from '@/types';

interface WorkspaceContextType {
  currentWorkspace: Workspace | null;
  currentFiles: File[];
  activeFile: File | null;
  setActiveFile: (file: File | null) => void;
  refreshFiles: () => Promise<void>;
  isLoading: boolean;
  onCreateFileRequest: (() => void) | null;
  setOnCreateFileRequest: (callback: (() => void) | null) => void;
}

const WorkspaceContext = createContext<WorkspaceContextType | undefined>(
  undefined
);

export function WorkspaceProvider({ children }: { children: ReactNode }) {
  const [currentWorkspace, setCurrentWorkspace] = useState<Workspace | null>(
    null
  );
  const [currentFiles, setCurrentFiles] = useState<File[]>([]);
  const [activeFile, setActiveFile] = useState<File | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [onCreateFileRequest, setOnCreateFileRequest] = useState<
    (() => void) | null
  >(null);
  const { user } = useAuth();
  const pathname = usePathname();

  // Extract workspace ID from pathname
  const workspaceId = pathname.match(/\/dashboard\/workspace\/([^\/]+)/)?.[1];

  useEffect(() => {
    if (workspaceId && user) {
      loadWorkspaceData(workspaceId);
    } else {
      setCurrentWorkspace(null);
      setCurrentFiles([]);
      setActiveFile(null);
    }
  }, [workspaceId, user]);

  const loadWorkspaceData = async (id: string) => {
    setIsLoading(true);
    try {
      const [workspace, files] = await Promise.all([
        WorkspaceService.getWorkspace(id),
        FileService.getFiles(id),
      ]);

      setCurrentWorkspace(workspace);
      setCurrentFiles(files);

      // If there's an active file, update it with fresh data from database
      if (activeFile) {
        const updatedActiveFile = files.find((f) => f.$id === activeFile.$id);
        if (updatedActiveFile) {
          // Update the active file with fresh database content
          // This ensures we get the latest saved version when returning to the page
          setActiveFile(updatedActiveFile);
        } else {
          setActiveFile(null);
        }
      }
    } catch (error) {
      console.error('Failed to load workspace data:', error);
      setCurrentWorkspace(null);
      setCurrentFiles([]);
      setActiveFile(null);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshFiles = async () => {
    if (workspaceId) {
      try {
        const files = await FileService.getFiles(workspaceId);
        setCurrentFiles(files);

        // Update active file with fresh database data
        if (activeFile) {
          const updatedActiveFile = files.find((f) => f.$id === activeFile.$id);
          if (updatedActiveFile) {
            setActiveFile(updatedActiveFile);
          } else {
            setActiveFile(null);
          }
        }
      } catch (error) {
        console.error('Failed to refresh files:', error);
      }
    }
  };

  return (
    <WorkspaceContext.Provider
      value={{
        currentWorkspace,
        currentFiles,
        activeFile,
        setActiveFile,
        refreshFiles,
        isLoading,
        onCreateFileRequest,
        setOnCreateFileRequest,
      }}
    >
      {children}
    </WorkspaceContext.Provider>
  );
}

export function useWorkspace() {
  const context = useContext(WorkspaceContext);
  if (context === undefined) {
    throw new Error('useWorkspace must be used within a WorkspaceProvider');
  }
  return context;
}
