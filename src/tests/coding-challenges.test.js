// Tests for the coding challenges component
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { CodingChallenges } from '../components/workspace/coding-challenges';

// Mock the onSelectChallenge function
const mockOnSelectChallenge = jest.fn();

describe('CodingChallenges Component', () => {
  beforeEach(() => {
    // Reset the mock before each test
    mockOnSelectChallenge.mockReset();

    // Render the component
    render(<CodingChallenges onSelectChallenge={mockOnSelectChallenge} />);
  });

  test('renders the component with title and filters', () => {
    // Check for the title
    expect(screen.getByText('Coding Challenges')).toBeInTheDocument();

    // Check for difficulty filters
    expect(screen.getByText('Difficulty:')).toBeInTheDocument();

    // Use getAllByRole to find buttons with specific text
    const difficultyButtons = screen.getAllByRole('button');
    expect(difficultyButtons.some(button => button.textContent === 'All')).toBe(true);
    expect(difficultyButtons.some(button => button.textContent === 'Easy')).toBe(true);
    expect(difficultyButtons.some(button => button.textContent === 'Medium')).toBe(true);
    expect(difficultyButtons.some(button => button.textContent === 'Hard')).toBe(true);

    // Check for category filters
    expect(screen.getByText('Category:')).toBeInTheDocument();
    expect(difficultyButtons.some(button => button.textContent === 'Array')).toBe(true);
    expect(difficultyButtons.some(button => button.textContent === 'String')).toBe(true);
    expect(difficultyButtons.some(button => button.textContent === 'Logic')).toBe(true);
    expect(difficultyButtons.some(button => button.textContent === 'Search')).toBe(true);
  });

  test('displays coding challenges', () => {
    // Check for challenge titles
    expect(screen.getByText('Two Sum')).toBeInTheDocument();
    expect(screen.getByText('Valid Palindrome')).toBeInTheDocument();
    expect(screen.getByText('FizzBuzz')).toBeInTheDocument();
    expect(screen.getByText('Reverse String')).toBeInTheDocument();
    expect(screen.getByText('Binary Search')).toBeInTheDocument();
    expect(screen.getByText('Merge Sorted Arrays')).toBeInTheDocument();
  });

  test('filters challenges by difficulty', () => {
    // Find all difficulty buttons
    const difficultyButtons = screen.getAllByRole('button', { name: /^(All|Easy|Medium|Hard)$/ });

    // Click on the "Easy" difficulty filter (should be the second button after "All")
    const easyButton = difficultyButtons.find(button => button.textContent === 'Easy');
    fireEvent.click(easyButton);

    // Should show Easy challenges
    expect(screen.getByText('Two Sum')).toBeInTheDocument();
    expect(screen.getByText('Valid Palindrome')).toBeInTheDocument();
    expect(screen.getByText('FizzBuzz')).toBeInTheDocument();
    expect(screen.getByText('Reverse String')).toBeInTheDocument();

    // Should not show Medium challenges
    expect(screen.queryByText('Binary Search')).not.toBeInTheDocument();
    expect(screen.queryByText('Merge Sorted Arrays')).not.toBeInTheDocument();

    // Click on the "Medium" difficulty filter
    const mediumButton = difficultyButtons.find(button => button.textContent === 'Medium');
    fireEvent.click(mediumButton);

    // Should show Medium challenges
    expect(screen.getByText('Binary Search')).toBeInTheDocument();
    expect(screen.getByText('Merge Sorted Arrays')).toBeInTheDocument();

    // Should not show Easy challenges
    expect(screen.queryByText('Two Sum')).not.toBeInTheDocument();
    expect(screen.queryByText('Valid Palindrome')).not.toBeInTheDocument();
  });

  test('filters challenges by category', () => {
    // Click on the "Array" category filter - use role and name to find the button
    const arrayButton = screen.getAllByRole('button').find(button =>
      button.textContent === 'Array' &&
      !button.textContent.includes('Merge Sorted Arrays')
    );
    fireEvent.click(arrayButton);

    // Should show Array challenges
    expect(screen.getByText('Two Sum')).toBeInTheDocument();
    expect(screen.getByText('Merge Sorted Arrays')).toBeInTheDocument();

    // Should not show other categories
    expect(screen.queryByText('Valid Palindrome')).not.toBeInTheDocument();
    expect(screen.queryByText('FizzBuzz')).not.toBeInTheDocument();

    // Click on the "String" category filter - use role and name to find the button
    const stringButton = screen.getAllByRole('button').find(button =>
      button.textContent === 'String' &&
      !button.textContent.includes('Reverse String')
    );
    fireEvent.click(stringButton);

    // Should show String challenges
    expect(screen.getByText('Valid Palindrome')).toBeInTheDocument();
    expect(screen.getByText('Reverse String')).toBeInTheDocument();

    // Should not show other categories
    expect(screen.queryByText('Two Sum')).not.toBeInTheDocument();
    expect(screen.queryByText('Binary Search')).not.toBeInTheDocument();
  });

  test('calls onSelectChallenge when "Start Challenge" button is clicked', () => {
    // Find all "Start Challenge" buttons using role
    const startButtons = screen.getAllByRole('button', { name: /Start Challenge/i });

    // Click the first button (Two Sum challenge)
    fireEvent.click(startButtons[0]);

    // Check if onSelectChallenge was called with the correct parameters
    expect(mockOnSelectChallenge).toHaveBeenCalledTimes(1);
    expect(mockOnSelectChallenge).toHaveBeenCalledWith(
      expect.stringContaining('function twoSum'),
      'Two Sum'
    );
  });
});
