// Integration test for the challenge execution flow
import { BrowserCodeExecutor } from '../../lib/code-execution/browser-executor';

// Mock the coding challenges
const CODING_CHALLENGES = [
  {
    id: 'two-sum',
    title: 'Two Sum',
    difficulty: 'Easy',
    category: 'Array',
    description: 'Given an array of integers nums and an integer target, return indices of the two numbers such that they add up to target.',
    starterCode: 'function twoSum(nums, target) { /* Your solution here */ }',
    testCases: ['[2, 7, 11, 15], 9 → [0, 1]']
  },
  {
    id: 'palindrome',
    title: 'Valid Palindrome',
    difficulty: 'Easy',
    category: 'String',
    description: 'A phrase is a palindrome if, after converting all uppercase letters into lowercase letters and removing all non-alphanumeric characters, it reads the same forward and backward.',
    starterCode: 'function isPalindrome(s) { /* Your solution here */ }',
    testCases: ['"A man, a plan, a canal: Panama" → true']
  },
  {
    id: 'fizzbuzz',
    title: 'FizzBuzz',
    difficulty: 'Easy',
    category: 'Logic',
    description: 'Write a program that outputs the string representation of numbers from 1 to n.',
    starterCode: 'function fizzBuzz(n) { /* Your solution here */ }',
    testCases: ['n=15 → ["1","2","Fizz","4","Buzz","Fizz","7","8","Fizz","Buzz","11","Fizz","13","14","FizzBuzz"]']
  },
  {
    id: 'binary-search',
    title: 'Binary Search',
    difficulty: 'Medium',
    category: 'Search',
    description: 'Given an array of integers nums which is sorted in ascending order, and an integer target, write a function to search target in nums.',
    starterCode: 'function search(nums, target) { /* Your solution here */ }',
    testCases: ['[-1,0,3,5,9,12], 9 → 4']
  }
];

// Mock the BrowserCodeExecutor
jest.mock('../../lib/code-execution/browser-executor', () => ({
  BrowserCodeExecutor: {
    executeJavaScript: jest.fn(),
  },
}));

describe('Challenge Execution Flow', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should execute Two Sum challenge with correct solution', async () => {
    // Find the Two Sum challenge
    const twoSumChallenge = CODING_CHALLENGES.find(c => c.id === 'two-sum');
    expect(twoSumChallenge).toBeDefined();

    // Replace the starter code with a working solution
    const solutionCode = `function twoSum(nums, target) {
      const map = new Map();

      for (let i = 0; i < nums.length; i++) {
        const complement = target - nums[i];

        if (map.has(complement)) {
          return [map.get(complement), i];
        }

        map.set(nums[i], i);
      }

      return [];
    }

    // Test cases
    console.log('Test 1:', twoSum([2, 7, 11, 15], 9)); // Expected: [0, 1]
    console.log('Test 2:', twoSum([3, 2, 4], 6)); // Expected: [1, 2]
    console.log('Test 3:', twoSum([3, 3], 6)); // Expected: [0, 1]`;

    // Mock successful execution with correct output
    BrowserCodeExecutor.executeJavaScript.mockResolvedValue({
      output: 'Test 1: 0,1\nTest 2: 1,2\nTest 3: 0,1',
      executionTime: 100,
    });

    // Execute the solution
    const result = await BrowserCodeExecutor.executeJavaScript(solutionCode);

    // Verify the execution was called with the solution code
    expect(BrowserCodeExecutor.executeJavaScript).toHaveBeenCalledWith(solutionCode);

    // Verify the output matches expected results
    expect(result.output).toContain('Test 1: 0,1');
    expect(result.output).toContain('Test 2: 1,2');
    expect(result.output).toContain('Test 3: 0,1');
  });

  test('should execute FizzBuzz challenge with correct solution', async () => {
    // Find the FizzBuzz challenge
    const fizzBuzzChallenge = CODING_CHALLENGES.find(c => c.id === 'fizzbuzz');
    expect(fizzBuzzChallenge).toBeDefined();

    // Replace the starter code with a working solution
    const solutionCode = `function fizzBuzz(n) {
      const result = [];

      for (let i = 1; i <= n; i++) {
        if (i % 15 === 0) {
          result.push("FizzBuzz");
        } else if (i % 3 === 0) {
          result.push("Fizz");
        } else if (i % 5 === 0) {
          result.push("Buzz");
        } else {
          result.push(String(i));
        }
      }

      return result;
    }

    // Test case
    console.log('FizzBuzz(15):', fizzBuzz(15));`;

    // Mock successful execution with correct output
    BrowserCodeExecutor.executeJavaScript.mockResolvedValue({
      output: 'FizzBuzz(15): 1,2,Fizz,4,Buzz,Fizz,7,8,Fizz,Buzz,11,Fizz,13,14,FizzBuzz',
      executionTime: 100,
    });

    // Execute the solution
    const result = await BrowserCodeExecutor.executeJavaScript(solutionCode);

    // Verify the execution was called with the solution code
    expect(BrowserCodeExecutor.executeJavaScript).toHaveBeenCalledWith(solutionCode);

    // Verify the output matches expected results
    expect(result.output).toContain('FizzBuzz(15): 1,2,Fizz,4,Buzz,Fizz,7,8,Fizz,Buzz,11,Fizz,13,14,FizzBuzz');
  });

  test('should execute Valid Palindrome challenge with correct solution', async () => {
    // Find the Valid Palindrome challenge
    const palindromeChallenge = CODING_CHALLENGES.find(c => c.id === 'palindrome');
    expect(palindromeChallenge).toBeDefined();

    // Replace the starter code with a working solution
    const solutionCode = `function isPalindrome(s) {
      // Remove non-alphanumeric characters and convert to lowercase
      const cleaned = s.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();

      // Compare with its reverse
      return cleaned === cleaned.split('').reverse().join('');
    }

    // Test cases
    console.log('Test 1:', isPalindrome("A man, a plan, a canal: Panama")); // true
    console.log('Test 2:', isPalindrome("race a car")); // false
    console.log('Test 3:', isPalindrome(" ")); // true`;

    // Mock successful execution with correct output
    BrowserCodeExecutor.executeJavaScript.mockResolvedValue({
      output: 'Test 1: true\nTest 2: false\nTest 3: true',
      executionTime: 100,
    });

    // Execute the solution
    const result = await BrowserCodeExecutor.executeJavaScript(solutionCode);

    // Verify the execution was called with the solution code
    expect(BrowserCodeExecutor.executeJavaScript).toHaveBeenCalledWith(solutionCode);

    // Verify the output matches expected results
    expect(result.output).toContain('Test 1: true');
    expect(result.output).toContain('Test 2: false');
    expect(result.output).toContain('Test 3: true');
  });

  test('should handle incorrect solution with appropriate error output', async () => {
    // Find the Binary Search challenge
    const binarySearchChallenge = CODING_CHALLENGES.find(c => c.id === 'binary-search');
    expect(binarySearchChallenge).toBeDefined();

    // Create an incorrect solution with a bug
    const incorrectSolutionCode = `function search(nums, target) {
      // This solution has a bug - it doesn't handle the case when target is not found
      let left = 0;
      let right = nums.length - 1;

      while (left <= right) {
        let mid = Math.floor((left + right) / 2);

        if (nums[mid] === target) {
          return mid;
        } else if (nums[mid] < target) {
          left = mid + 1;
        } else {
          right = mid - 1;
        }
      }

      // Missing return -1 here!
    }

    // Test cases
    console.log('Test 1:', search([-1,0,3,5,9,12], 9)); // 4
    console.log('Test 2:', search([-1,0,3,5,9,12], 2)); // -1
    console.log('Test 3:', search([5], 5)); // 0`;

    // Mock execution with undefined result for the second test case
    BrowserCodeExecutor.executeJavaScript.mockResolvedValue({
      output: 'Test 1: 4\nTest 2: undefined\nTest 3: 0',
      executionTime: 100,
    });

    // Execute the solution
    const result = await BrowserCodeExecutor.executeJavaScript(incorrectSolutionCode);

    // Verify the execution was called with the incorrect solution code
    expect(BrowserCodeExecutor.executeJavaScript).toHaveBeenCalledWith(incorrectSolutionCode);

    // Verify the output shows the incorrect result
    expect(result.output).toContain('Test 1: 4');
    expect(result.output).toContain('Test 2: undefined'); // Should be -1
    expect(result.output).toContain('Test 3: 0');
  });
});
