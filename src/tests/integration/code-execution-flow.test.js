// Integration test for the code execution flow
import { <PERSON>rowserCodeExecutor } from '../../lib/code-execution/browser-executor';
import { getTemplate } from '../../lib/code-templates';

// Mock the BrowserCodeExecutor
jest.mock('../../lib/code-execution/browser-executor', () => ({
  BrowserCodeExecutor: {
    executeJavaScript: jest.fn(),
    executeTypeScript: jest.fn(),
    executePython: jest.fn(),
  },
}));

describe('Code Execution Flow Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Default mock implementations
    BrowserCodeExecutor.executeJavaScript.mockResolvedValue({
      output: 'Hello, World!\nWelcome to Codeable!\nOriginal: 1,2,3,4,5\nDoubled: 2,4,6,8,10\nFibonacci sequence:',
      executionTime: 100,
      error: undefined
    });

    BrowserCodeExecutor.executeTypeScript.mockResolvedValue({
      output: 'Person: [object Object]\nString result: Hello TypeScript!\nNumber result: 42',
      executionTime: 120,
      error: undefined
    });

    BrowserCodeExecutor.executePython.mockResolvedValue({
      output: '🐍 Hello from Python!\nWelcome to Codeable!',
      executionTime: 150,
      error: undefined
    });
  });
  test('should execute JavaScript code template successfully', async () => {
    // Get a JavaScript template
    const jsTemplate = getTemplate('javascript', 'Hello World');

    // Execute the template code
    const result = await BrowserCodeExecutor.executeJavaScript(jsTemplate);

    // Verify execution was successful
    expect(result.output).toContain('Hello, World!');
    expect(result.output).toContain('Welcome to Codeable!');
    expect(result.error).toBeUndefined();
    expect(result.executionTime).toBeGreaterThan(0);
  });

  test('should execute TypeScript code template successfully', async () => {
    // Get a TypeScript template
    const tsTemplate = getTemplate('typescript', 'Hello World');

    // Execute the template code
    const result = await BrowserCodeExecutor.executeTypeScript(tsTemplate);

    // Verify execution was successful
    expect(result.output).toContain('Person:');
    expect(result.output).toContain('String result:');
    expect(result.output).toContain('Number result:');
    expect(result.error).toBeUndefined();
    expect(result.executionTime).toBeGreaterThan(0);
  });

  test('should execute coding challenge solution', async () => {
    // Two Sum solution code
    const solutionCode = `
      function twoSum(nums, target) {
        const map = new Map();

        for (let i = 0; i < nums.length; i++) {
          const complement = target - nums[i];

          if (map.has(complement)) {
            return [map.get(complement), i];
          }

          map.set(nums[i], i);
        }

        return [];
      }

      // Test cases
      console.log('Test 1:', twoSum([2, 7, 11, 15], 9)); // Expected: [0, 1]
      console.log('Test 2:', twoSum([3, 2, 4], 6)); // Expected: [1, 2]
      console.log('Test 3:', twoSum([3, 3], 6)); // Expected: [0, 1]
    `;

    // Mock successful execution with correct output
    BrowserCodeExecutor.executeJavaScript.mockResolvedValue({
      output: 'Test 1: 0,1\nTest 2: 1,2\nTest 3: 0,1',
      executionTime: 100,
      error: undefined
    });

    // Execute the solution code
    const result = await BrowserCodeExecutor.executeJavaScript(solutionCode);

    // Verify execution was successful
    expect(result.output).toContain('Test 1: 0,1');
    expect(result.output).toContain('Test 2: 1,2');
    expect(result.output).toContain('Test 3: 0,1');
    expect(result.error).toBeUndefined();
  });

  test('should handle errors in code execution', async () => {
    // Code with syntax error
    const errorCode = `
      function brokenFunction() {
        console.log("This has a syntax error"
        return "Oops";
      }

      brokenFunction();
    `;

    // Mock error response
    BrowserCodeExecutor.executeJavaScript.mockResolvedValue({
      output: '',
      error: 'SyntaxError: Unexpected token',
      executionTime: 50
    });

    // Execute the code with error
    const result = await BrowserCodeExecutor.executeJavaScript(errorCode);

    // Verify error was captured
    expect(result.error).toBeTruthy();
    expect(result.output).toBe('');
  });

  test('should handle infinite loops with timeout', async () => {
    // Code with infinite loop
    const infiniteLoopCode = `
      function infiniteLoop() {
        while(true) {
          // This will run forever
        }
      }

      infiniteLoop();
    `;

    // Mock timeout response
    BrowserCodeExecutor.executeJavaScript.mockResolvedValue({
      output: '',
      error: 'Code execution timeout (5 seconds)',
      executionTime: 5000
    });

    // Execute the code with infinite loop
    const result = await BrowserCodeExecutor.executeJavaScript(infiniteLoopCode);

    // Verify timeout error was captured
    expect(result.error).toContain('timeout');
  });
});
