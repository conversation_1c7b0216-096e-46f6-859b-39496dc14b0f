// Tests for the workspace context
import React from 'react';
import { render, act } from '@testing-library/react';
import { WorkspaceProvider, useWorkspace } from '../contexts/workspace-context';
import { useAuth } from '../contexts/auth-context';
import { usePathname } from 'next/navigation';
import { WorkspaceService } from '@/lib/services/workspace';
import { FileService } from '@/lib/services/file';

// Mock the hooks and services
jest.mock('next/navigation', () => ({
  usePathname: jest.fn(),
}));

jest.mock('../contexts/auth-context', () => ({
  useAuth: jest.fn(),
}));

jest.mock('@/lib/services/workspace', () => ({
  WorkspaceService: {
    getWorkspace: jest.fn(),
  },
}));

jest.mock('@/lib/services/file', () => ({
  FileService: {
    getFiles: jest.fn(),
  },
}));

// Test component that uses the workspace context
const TestComponent = () => {
  const {
    currentWorkspace,
    currentFiles,
    activeFile,
    setActiveFile,
    refreshFiles,
    isLoading,
  } = useWorkspace();

  return (
    <div>
      <div data-testid="workspace-id">
        {currentWorkspace ? currentWorkspace.$id : 'no-workspace'}
      </div>
      <div data-testid="files-count">{currentFiles.length}</div>
      <div data-testid="active-file">
        {activeFile ? activeFile.name : 'no-active-file'}
      </div>
      <div data-testid="loading">{isLoading.toString()}</div>
      <button
        data-testid="set-active"
        onClick={() => setActiveFile(currentFiles[0] || null)}
      >
        Set Active
      </button>
      <button data-testid="refresh" onClick={refreshFiles}>
        Refresh
      </button>
    </div>
  );
};

describe('WorkspaceContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Default mocks
    usePathname.mockReturnValue('/dashboard/workspace/test-workspace-id');
    useAuth.mockReturnValue({ user: { $id: 'test-user-id' }, loading: false });

    WorkspaceService.getWorkspace.mockResolvedValue({
      $id: 'test-workspace-id',
      name: 'Test Workspace',
    });

    FileService.getFiles.mockResolvedValue([
      { $id: 'file1', name: 'file1.js' },
      { $id: 'file2', name: 'file2.js' },
    ]);
  });

  test('should load workspace data when path contains workspace ID', async () => {
    let component;

    await act(async () => {
      component = render(
        <WorkspaceProvider>
          <TestComponent />
        </WorkspaceProvider>
      );
    });

    const { getByTestId } = component;

    expect(WorkspaceService.getWorkspace).toHaveBeenCalledWith('test-workspace-id');
    expect(FileService.getFiles).toHaveBeenCalledWith('test-workspace-id');

    expect(getByTestId('workspace-id').textContent).toBe('test-workspace-id');
    expect(getByTestId('files-count').textContent).toBe('2');
    expect(getByTestId('active-file').textContent).toBe('no-active-file');
    expect(getByTestId('loading').textContent).toBe('false');
  });

  test('should not load workspace data when path does not contain workspace ID', async () => {
    usePathname.mockReturnValue('/dashboard');

    let component;

    await act(async () => {
      component = render(
        <WorkspaceProvider>
          <TestComponent />
        </WorkspaceProvider>
      );
    });

    const { getByTestId } = component;

    expect(WorkspaceService.getWorkspace).not.toHaveBeenCalled();
    expect(FileService.getFiles).not.toHaveBeenCalled();

    expect(getByTestId('workspace-id').textContent).toBe('no-workspace');
    expect(getByTestId('files-count').textContent).toBe('0');
  });

  test('should set active file', async () => {
    let component;

    await act(async () => {
      component = render(
        <WorkspaceProvider>
          <TestComponent />
        </WorkspaceProvider>
      );
    });

    const { getByTestId } = component;

    await act(async () => {
      getByTestId('set-active').click();
    });

    expect(getByTestId('active-file').textContent).toBe('file1.js');
  });

  test('should refresh files', async () => {
    let component;

    await act(async () => {
      component = render(
        <WorkspaceProvider>
          <TestComponent />
        </WorkspaceProvider>
      );
    });

    const { getByTestId } = component;

    // Clear previous calls
    FileService.getFiles.mockClear();

    // Update mock to return different files on refresh
    FileService.getFiles.mockResolvedValue([
      { $id: 'file1', name: 'file1.js' },
      { $id: 'file2', name: 'file2.js' },
      { $id: 'file3', name: 'file3.js' },
    ]);

    await act(async () => {
      getByTestId('refresh').click();
    });

    expect(FileService.getFiles).toHaveBeenCalledWith('test-workspace-id');
    expect(getByTestId('files-count').textContent).toBe('3');
  });

  test('should handle loading state', async () => {
    // Make the API calls take some time
    let resolveWorkspace, resolveFiles;

    WorkspaceService.getWorkspace.mockImplementation(
      () => new Promise(resolve => { resolveWorkspace = resolve; })
    );

    FileService.getFiles.mockImplementation(
      () => new Promise(resolve => { resolveFiles = resolve; })
    );

    let component;

    await act(async () => {
      component = render(
        <WorkspaceProvider>
          <TestComponent />
        </WorkspaceProvider>
      );
    });

    const { getByTestId } = component;

    // Should be in loading state
    expect(getByTestId('loading').textContent).toBe('true');

    // Resolve the promises
    await act(async () => {
      resolveWorkspace({ $id: 'test-workspace-id', name: 'Test Workspace' });
      resolveFiles([
        { $id: 'file1', name: 'file1.js' },
        { $id: 'file2', name: 'file2.js' },
      ]);
    });

    // Should no longer be in loading state
    expect(getByTestId('loading').textContent).toBe('false');
  });
});
