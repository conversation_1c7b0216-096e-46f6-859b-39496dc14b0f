// Tests for the code templates functionality
import { getTemplate, getTemplateNames, getDefaultTemplate } from '../lib/code-templates';

describe('Code Templates', () => {
  test('getTemplate should return the correct template for a language and template name', () => {
    const jsTemplate = getTemplate('javascript', 'Hello World');
    expect(jsTemplate).toContain('JavaScript Hello World');
    expect(jsTemplate).toContain('console.log');

    const tsTemplate = getTemplate('typescript', 'Hello World');
    expect(tsTemplate).toContain('TypeScript Hello World with types');
    expect(tsTemplate).toContain('interface Person');
  });

  test('getTemplate should return default template for invalid template name', () => {
    const invalidTemplate = getTemplate('javascript', 'NonExistentTemplate');
    expect(invalidTemplate).toContain('// JavaScript solution');
    expect(invalidTemplate).toContain('function solution()');
  });

  test('getTemplate should return default template for invalid language', () => {
    const invalidLangTemplate = getTemplate('invalidLanguage', 'Hello World');
    expect(invalidLangTemplate).toContain('// Your code here');
  });

  test('getTemplateNames should return all template names for a language', () => {
    const jsTemplateNames = getTemplateNames('javascript');
    expect(jsTemplateNames).toContain('Hello World');
    expect(jsTemplateNames).toContain('Two Sum Problem');
    expect(jsTemplateNames).toContain('FizzBuzz Challenge');

    const tsTemplateNames = getTemplateNames('typescript');
    expect(tsTemplateNames).toContain('Hello World');
    expect(tsTemplateNames).toContain('Linked List Implementation');
  });

  test('getTemplateNames should return empty array for invalid language', () => {
    const invalidLangTemplateNames = getTemplateNames('invalidLanguage');
    expect(invalidLangTemplateNames).toEqual([]);
  });

  test('getDefaultTemplate should return appropriate default template for each language', () => {
    const jsDefault = getDefaultTemplate('javascript');
    expect(jsDefault).toContain('// JavaScript solution');

    const tsDefault = getDefaultTemplate('typescript');
    expect(tsDefault).toContain('// TypeScript solution');

    const pyDefault = getDefaultTemplate('python');
    expect(pyDefault).toContain('# Python solution');

    const javaDefault = getDefaultTemplate('java');
    expect(javaDefault).toContain('// Java solution');
  });
});
