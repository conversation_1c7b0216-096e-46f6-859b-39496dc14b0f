# Test Suite for Codeable

This directory contains tests for the Codeable application. The tests are organized into different categories to ensure comprehensive coverage of the application's functionality.

## Test Structure

- **Unit Tests**: Test individual components and functions in isolation
- **Integration Tests**: Test how different parts of the application work together
- **Setup**: Configuration for the test environment

## Running Tests

You can run the tests using the following npm scripts:

```bash
# Run all tests
npm test

# Run tests in watch mode (useful during development)
npm run test:watch

# Run tests with coverage report
npm run test:coverage
```

## Test Files

### Unit Tests

- `browser-executor.test.js`: Tests for the browser-based code execution functionality
- `code-templates.test.js`: Tests for the code template management
- `coding-challenges.test.js`: Tests for the coding challenges component
- `api-execute.test.js`: Tests for the code execution API
- `workspace-context.test.js`: Tests for the workspace context provider

### Integration Tests

- `integration/code-execution-flow.test.js`: Tests the complete flow of code execution

### Setup

- `setup.js`: Configuration for the Jest test environment, including mocks for browser APIs

## Adding New Tests

When adding new tests:

1. Follow the naming convention: `[feature].test.js`
2. Import the necessary components and utilities
3. Use descriptive test names that explain what is being tested
4. Group related tests using `describe` blocks
5. Keep tests focused on specific functionality

## Mocks

The test suite uses various mocks to simulate browser APIs and external dependencies:

- `localStorage`
- `fetch`
- `ResizeObserver`
- `IntersectionObserver`
- `matchMedia`
- Pyodide (Python in browser)

These mocks are defined in the `setup.js` file and are automatically applied to all tests.
