# Testing Guide for Codeable

This document provides guidance on how to run and extend the test suite for the Codeable application.

## Getting Started

The test suite uses <PERSON><PERSON> as the testing framework along with React Testing Library for component testing. To run the tests:

```bash
# Run all tests
npm test

# Run tests in watch mode (useful during development)
npm run test:watch

# Run tests with coverage report
npm run test:coverage
```

## Test Structure

The tests are organized into the following categories:

### Unit Tests

Located in `src/tests/`, these test individual components and functions:

- `browser-executor.test.js`: Tests for the browser-based code execution functionality
- `code-templates.test.js`: Tests for the code template management
- `coding-challenges.test.js`: Tests for the coding challenges component
- `api-execute.test.js`: Tests for the code execution API
- `workspace-context.test.js`: Tests for the workspace context provider
- `enhanced-code-editor.test.js`: Tests for the enhanced code editor component

### Integration Tests

Located in `src/tests/integration/`, these test how different parts of the application work together:

- `code-execution-flow.test.js`: Tests the complete flow of code execution
- `challenge-execution.test.js`: Tests executing coding challenge solutions

## Writing New Tests

### Unit Tests

When writing unit tests for components:

1. Import the component and any necessary dependencies
2. Mock external dependencies using <PERSON><PERSON>'s mocking capabilities
3. Render the component using React Testing Library
4. Use queries to find elements in the rendered component
5. Simulate user interactions using fireEvent
6. Assert that the component behaves as expected

Example:

```javascript
import { render, screen, fireEvent } from '@testing-library/react';
import { MyComponent } from '../components/my-component';

test('should handle button click', () => {
  const handleClick = jest.fn();
  render(<MyComponent onClick={handleClick} />);

  fireEvent.click(screen.getByText('Click me'));

  expect(handleClick).toHaveBeenCalled();
});
```

### Integration Tests

For integration tests:

1. Focus on testing how multiple components or functions work together
2. Mock external services or APIs that are not part of the test
3. Test complete user flows rather than individual actions

## Mocking

The test suite uses various mocks to simulate browser APIs and external dependencies:

- `localStorage`
- `fetch`
- `ResizeObserver`
- `IntersectionObserver`
- `matchMedia`
- Pyodide (Python in browser)

These mocks are defined in the `setup.js` file and are automatically applied to all tests.

To mock a specific module:

```javascript
// Mock a module
jest.mock('../path/to/module', () => ({
  functionName: jest.fn(),
  ClassName: jest.fn().mockImplementation(() => ({
    methodName: jest.fn(),
  })),
}));
```

## Testing Async Code

For testing asynchronous code:

1. Use `async/await` with the test function
2. Use `waitFor` to wait for asynchronous operations to complete
3. Assert on the expected outcome after the operation completes

Example:

```javascript
test('should load data asynchronously', async () => {
  render(<DataLoader />);

  // Wait for the loading state to complete
  await waitFor(() => {
    expect(screen.getByText('Data loaded')).toBeInTheDocument();
  });

  // Assert on the loaded data
  expect(screen.getByText('Item 1')).toBeInTheDocument();
});
```

## Code Coverage

The test suite is configured to generate code coverage reports. To view the coverage report:

1. Run `npm run test:coverage`
2. Open the generated report in `coverage/lcov-report/index.html`

The coverage report shows:

- Statement coverage: percentage of statements executed
- Branch coverage: percentage of branches executed
- Function coverage: percentage of functions called
- Line coverage: percentage of lines executed

## Continuous Integration

The test suite is designed to run in a CI environment. The tests are automatically run on:

- Pull requests to the main branch
- Pushes to the main branch

If any tests fail, the CI build will fail, preventing the integration of broken code.

## Troubleshooting

If you encounter issues with the tests:

1. Check that all dependencies are installed: `npm install`
2. Ensure that the test environment is properly set up in `setup.js`
3. Check for any console errors during test execution
4. Try running a specific test file: `npm test -- path/to/test.js`
5. Try running tests with the `--verbose` flag: `npm test -- --verbose`

## Best Practices

- Keep tests focused on a single behavior or feature
- Use descriptive test names that explain what is being tested
- Avoid testing implementation details; focus on behavior
- Use setup and teardown functions to avoid repetition
- Mock external dependencies to isolate the code being tested
- Use snapshot testing sparingly and only for stable components
