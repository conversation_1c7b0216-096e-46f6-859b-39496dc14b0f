// Tests for the enhanced code editor component
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { EnhancedCodeEditor } from '../components/editor/enhanced-code-editor';
import { BrowserCodeExecutor } from '../lib/code-execution/browser-executor';

// Mock the BrowserCodeExecutor
jest.mock('../lib/code-execution/browser-executor', () => ({
  BrowserCodeExecutor: {
    executeJavaScript: jest.fn(),
    executeTypeScript: jest.fn(),
    executePython: jest.fn(),
  },
}));

// Mock the CodeEditor component
jest.mock('../components/editor/code-editor', () => ({
  CodeEditor: ({ value, language, onChange, onSave, readOnly, className, onKeyDown }) => (
    <div data-testid="code-editor" className={className}>
      <textarea
        data-testid="code-editor-textarea"
        value={value}
        onChange={(e) => onChange && onChange(e.target.value)}
        readOnly={readOnly}
        onKeyDown={(e) => onKeyDown && onKeyDown(e)}
      />
      <div data-testid="code-editor-language">{language}</div>
      <button data-testid="code-editor-save" onClick={onSave}>
        Save
      </button>
    </div>
  ),
}));

// Mock the ResizablePanel component
jest.mock('../components/ui/resizable-panel', () => ({
  ResizablePanel: ({ children, className }) => (
    <div data-testid="resizable-panel" className={className}>
      {children}
    </div>
  ),
}));

// Mock the InputVariablesPanel component
jest.mock('../components/editor/input-variables-panel', () => ({
  InputVariablesPanel: ({ language, onVariablesChange, onRunWithVariables, className }) => (
    <div data-testid="input-variables-panel" className={className}>
      <div data-testid="input-variables-language">{language}</div>
      <textarea
        data-testid="input-variables-textarea"
        onChange={(e) => onVariablesChange(e.target.value)}
      />
      <button data-testid="input-variables-run" onClick={onRunWithVariables}>
        Run with Variables
      </button>
    </div>
  ),
}));

describe('EnhancedCodeEditor Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Default mock implementations
    BrowserCodeExecutor.executeJavaScript.mockResolvedValue({
      output: 'JavaScript output',
      executionTime: 100,
    });

    BrowserCodeExecutor.executeTypeScript.mockResolvedValue({
      output: 'TypeScript output',
      executionTime: 120,
    });

    BrowserCodeExecutor.executePython.mockResolvedValue({
      output: 'Python output',
      executionTime: 150,
    });
  });

  test('renders with JavaScript code', () => {
    render(
      <EnhancedCodeEditor
        value="console.log('Hello');"
        language="javascript"
        onChange={() => {}}
      />
    );

    expect(screen.getByTestId('code-editor-language').textContent).toBe('javascript');
    expect(screen.getByRole('button', { name: /Run JavaScript/i })).toBeInTheDocument();
  });

  test('renders with TypeScript code', () => {
    render(
      <EnhancedCodeEditor
        value="const greeting: string = 'Hello';"
        language="typescript"
        onChange={() => {}}
      />
    );

    expect(screen.getByTestId('code-editor-language').textContent).toBe('typescript');
    expect(screen.getByRole('button', { name: /Run TypeScript/i })).toBeInTheDocument();
  });

  test('renders with Python code', () => {
    render(
      <EnhancedCodeEditor
        value="print('Hello')"
        language="python"
        onChange={() => {}}
      />
    );

    expect(screen.getByTestId('code-editor-language').textContent).toBe('python');
    expect(screen.getByRole('button', { name: /Run Python/i })).toBeInTheDocument();
  });

  test('renders with non-executable language', () => {
    render(
      <EnhancedCodeEditor
        value="public class Main {}"
        language="java"
        onChange={() => {}}
      />
    );

    expect(screen.getByTestId('code-editor-language').textContent).toBe('java');
    expect(screen.getByRole('button', { name: /Run Code/i })).toBeInTheDocument();
  });

  test('executes JavaScript code when run button is clicked', async () => {
    render(
      <EnhancedCodeEditor
        value="console.log('Hello');"
        language="javascript"
        onChange={() => {}}
      />
    );

    // Click the run button
    fireEvent.click(screen.getByRole('button', { name: /Run JavaScript/i }));

    // Wait for execution to complete
    await waitFor(() => {
      expect(BrowserCodeExecutor.executeJavaScript).toHaveBeenCalled();
      expect(screen.getByText(/JavaScript output/i)).toBeInTheDocument();
    });
  });

  test('executes TypeScript code when run button is clicked', async () => {
    render(
      <EnhancedCodeEditor
        value="const greeting: string = 'Hello';"
        language="typescript"
        onChange={() => {}}
      />
    );

    // Click the run button
    fireEvent.click(screen.getByRole('button', { name: /Run TypeScript/i }));

    // Wait for execution to complete
    await waitFor(() => {
      expect(BrowserCodeExecutor.executeTypeScript).toHaveBeenCalled();
      expect(screen.getByText(/TypeScript output/i)).toBeInTheDocument();
    });
  });

  test('executes Python code when run button is clicked', async () => {
    render(
      <EnhancedCodeEditor
        value="print('Hello')"
        language="python"
        onChange={() => {}}
      />
    );

    // Click the run button
    fireEvent.click(screen.getByRole('button', { name: /Run Python/i }));

    // Wait for execution to complete
    await waitFor(() => {
      expect(BrowserCodeExecutor.executePython).toHaveBeenCalled();
      expect(screen.getByText(/Python output/i)).toBeInTheDocument();
    });
  });

  test('handles execution errors', async () => {
    // Mock execution error
    BrowserCodeExecutor.executeJavaScript.mockResolvedValue({
      output: '',
      error: 'Syntax error',
      executionTime: 50,
    });

    render(
      <EnhancedCodeEditor
        value="console.log('Hello');"
        language="javascript"
        onChange={() => {}}
      />
    );

    // Click the run button
    fireEvent.click(screen.getByRole('button', { name: /Run JavaScript/i }));

    // Wait for execution to complete
    await waitFor(() => {
      expect(screen.getByText(/Syntax error/i)).toBeInTheDocument();
      // Look for error badge by its class rather than text
      expect(screen.getByText('✗ Error')).toBeInTheDocument();
    });
  });

  test('clears output when clear button is clicked', async () => {
    render(
      <EnhancedCodeEditor
        value="console.log('Hello');"
        language="javascript"
        onChange={() => {}}
      />
    );

    // Click the run button
    fireEvent.click(screen.getByRole('button', { name: /Run JavaScript/i }));

    // Wait for execution to complete
    await waitFor(() => {
      expect(screen.getByText(/JavaScript output/i)).toBeInTheDocument();
    });

    // Click the clear button (use the first one with title attribute)
    const clearButtons = screen.getAllByTitle('Clear output');
    fireEvent.click(clearButtons[0]);

    // Output should be cleared
    expect(screen.queryByText(/JavaScript output/i)).not.toBeInTheDocument();
  });

  test('toggles output panel visibility', () => {
    render(
      <EnhancedCodeEditor
        value="console.log('Hello');"
        language="javascript"
        onChange={() => {}}
      />
    );

    // Initially, output panel should be expanded
    expect(screen.getByRole('button', { name: /Hide Console/i })).toBeInTheDocument();

    // Click to hide console
    fireEvent.click(screen.getByRole('button', { name: /Hide Console/i }));

    // Now it should show "Show Console"
    expect(screen.getByRole('button', { name: /Show Console/i })).toBeInTheDocument();

    // Click to show console again
    fireEvent.click(screen.getByRole('button', { name: /Show Console/i }));

    // Back to "Hide Console"
    expect(screen.getByRole('button', { name: /Hide Console/i })).toBeInTheDocument();
  });

  test('calls onChange when code is modified', () => {
    const handleChange = jest.fn();

    render(
      <EnhancedCodeEditor
        value="console.log('Hello');"
        language="javascript"
        onChange={handleChange}
      />
    );

    // Change the code
    fireEvent.change(screen.getByTestId('code-editor-textarea'), {
      target: { value: "console.log('Updated');" },
    });

    expect(handleChange).toHaveBeenCalledWith("console.log('Updated');");
  });

  test('calls onSave when save is triggered', () => {
    const handleSave = jest.fn();

    render(
      <EnhancedCodeEditor
        value="console.log('Hello');"
        language="javascript"
        onChange={() => {}}
        onSave={handleSave}
      />
    );

    // Trigger save
    fireEvent.click(screen.getByTestId('code-editor-save'));

    expect(handleSave).toHaveBeenCalled();
  });

  test('executes code with keyboard shortcut (Ctrl+Enter)', async () => {
    render(
      <EnhancedCodeEditor
        value="console.log('Hello');"
        language="javascript"
        onChange={() => {}}
      />
    );

    // Trigger Ctrl+Enter keyboard shortcut
    fireEvent.keyDown(screen.getByTestId('code-editor-textarea'), {
      key: 'Enter',
      ctrlKey: true,
    });

    // Wait for execution to complete
    await waitFor(() => {
      expect(BrowserCodeExecutor.executeJavaScript).toHaveBeenCalled();
      expect(screen.getByText('JavaScript output')).toBeInTheDocument();
    });
  });

  test('shows input dialog for Python code', () => {
    render(
      <EnhancedCodeEditor
        value="input('Enter your name:')"
        language="python"
        onChange={() => {}}
      />
    );

    // Click the Add Input button
    fireEvent.click(screen.getByRole('button', { name: /Add Input \(0\)/i }));

    // Input dialog should be visible
    expect(screen.getByText('Python Input')).toBeInTheDocument();

    // Add an input
    fireEvent.change(screen.getByPlaceholderText('Enter your input...'), {
      target: { value: 'Test Input' },
    });

    // Submit the input
    fireEvent.click(screen.getByRole('button', { name: /Add Input$/i }));

    // Input count should be updated
    expect(screen.getByRole('button', { name: /Add Input \(1\)/i })).toBeInTheDocument();
  });
});
