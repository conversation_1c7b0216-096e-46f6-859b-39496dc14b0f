// Tests for the browser-based code executor
import { BrowserCodeExecutor } from '../lib/code-execution/browser-executor';

// Mock the BrowserCodeExecutor
jest.mock('../lib/code-execution/browser-executor', () => ({
  BrowserCodeExecutor: {
    executeJavaScript: jest.fn(),
    executeTypeScript: jest.fn(),
    executePython: jest.fn(),
  },
}));

describe('BrowserCodeExecutor', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  describe('executeJavaScript', () => {
    test('should execute simple JavaScript code', async () => {
      const code = 'console.log("Hello, World!");';

      // Mock the implementation for this test
      BrowserCodeExecutor.executeJavaScript.mockResolvedValue({
        output: 'Hello, World!',
        error: undefined,
        executionTime: 100
      });

      const result = await BrowserCodeExecutor.executeJavaScript(code);

      expect(result.output).toBe('Hello, World!');
      expect(result.error).toBeUndefined();
      expect(result.executionTime).toBe(100);
    });

    test('should handle errors in JavaScript code', async () => {
      const code = 'throw new Error("Test error");';

      // Mock the implementation for this test
      BrowserCodeExecutor.executeJavaScript.mockResolvedValue({
        output: '',
        error: 'Test error',
        executionTime: 50
      });

      const result = await BrowserCodeExecutor.executeJavaScript(code);

      expect(result.error).toContain('Test error');
      expect(result.executionTime).toBe(50);
    });

    test('should capture console output', async () => {
      const code = `
        console.log("Line 1");
        console.log("Line 2");
        console.log(1 + 2);
      `;

      // Mock the implementation for this test
      BrowserCodeExecutor.executeJavaScript.mockResolvedValue({
        output: 'Line 1\nLine 2\n3',
        error: undefined,
        executionTime: 75
      });

      const result = await BrowserCodeExecutor.executeJavaScript(code);

      expect(result.output).toBe('Line 1\nLine 2\n3');
      expect(result.error).toBeUndefined();
    });

    test('should handle object logging', async () => {
      const code = `
        const obj = { name: 'Test', value: 42 };
        console.log(obj);
      `;

      // Mock the implementation for this test
      BrowserCodeExecutor.executeJavaScript.mockResolvedValue({
        output: '{\n  "name": "Test",\n  "value": 42\n}',
        error: undefined,
        executionTime: 80
      });

      const result = await BrowserCodeExecutor.executeJavaScript(code);

      expect(result.output).toContain('"name": "Test"');
      expect(result.output).toContain('"value": 42');
    });
  });

  describe('executeTypeScript', () => {
    test('should execute TypeScript code by converting to JavaScript', async () => {
      const code = `
        const greeting: string = "Hello, TypeScript!";
        console.log(greeting);

        function add(a: number, b: number): number {
          return a + b;
        }

        console.log(add(5, 7));
      `;

      // Mock the implementation for this test
      BrowserCodeExecutor.executeTypeScript.mockResolvedValue({
        output: 'Hello, TypeScript!\n12',
        error: undefined,
        executionTime: 120
      });

      const result = await BrowserCodeExecutor.executeTypeScript(code);

      expect(result.output).toBe('Hello, TypeScript!\n12');
      expect(result.error).toBeUndefined();
    });
  });
});
