// Database Models
export interface User {
  $id: string;
  email: string;
  name: string;
  emailVerification: boolean;
  $createdAt: string;
  $updatedAt: string;
}

export interface Workspace {
  $id: string;
  name: string;
  description?: string;
  category: 'interview-prep' | 'leetcode' | 'algorithms' | 'system-design' | 'general';
  userId: string;
  fileCount?: number; // Computed field
  lastAccessed?: string; // Computed field
  $createdAt: string;
  $updatedAt: string;
}

export interface File {
  $id: string;
  name: string;
  content: string;
  language:
    | 'javascript'
    | 'typescript'
    | 'python'
    | 'java'
    | 'cpp'
    | 'go'
    | 'rust'
    | 'markdown'
    | 'text';
  workspaceId: string;
  userId: string;
  size?: number; // Computed field in bytes
  lastModified?: string; // Computed field
  lastExecuted?: string; // When file was last run
  executionCount?: number; // Total times file was executed
  isModified?: boolean; // Has unsaved changes
  $createdAt: string;
  $updatedAt: string;
}

export interface PracticeSession {
  $id: string;
  fileId: string;
  userId: string;
  duration: number; // in seconds
  sessionDate: string;
  fileName?: string; // Computed field
  workspaceName?: string; // Computed field
}

// Form Data Types
export interface CreateWorkspaceData {
  name: string;
  description?: string;
  category: Workspace['category'];
}

export interface UpdateWorkspaceData {
  name?: string;
  description?: string;
  category?: Workspace['category'];
}

export interface CreateFileData {
  name: string;
  language: File['language'];
  workspaceId: string;
  content?: string;
}

// UI State Types
export interface SaveStatus {
  status: 'saved' | 'saving' | 'error';
  message?: string;
}

export interface EditorState {
  activeFileId?: string;
  openFiles: string[];
  savedStates: Record<string, boolean>;
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: Record<string, unknown>;
}
