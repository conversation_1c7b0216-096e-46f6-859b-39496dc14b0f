'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { FileService } from '@/lib/services/file';

interface UseAutoSaveOptions {
  delay?: number; // Delay in milliseconds (default: 2000)
  enabled?: boolean;
}

export function useAutoSave(
  fileId: string | null,
  content: string,
  options: UseAutoSaveOptions = {}
) {
  const { delay = 2000, enabled = true } = options;
  const [saveStatus, setSaveStatus] = useState<'saved' | 'saving' | 'error' | 'unsaved'>('saved');
  const [lastSavedContent, setLastSavedContent] = useState(content);
  const timeoutRef = useRef<NodeJS.Timeout>();
  const lastSaveRef = useRef<Promise<any>>();

  const save = useCallback(async (contentToSave: string) => {
    if (!fileId || !enabled) return;

    setSaveStatus('saving');
    
    try {
      const savePromise = FileService.updateFile(fileId, contentToSave);
      lastSaveRef.current = savePromise;
      
      await savePromise;
      
      // Only update status if this is still the latest save operation
      if (lastSaveRef.current === savePromise) {
        setLastSavedContent(contentToSave);
        setSaveStatus('saved');
      }
    } catch (error) {
      console.error('Auto-save failed:', error);
      
      // Only update status if this is still the latest save operation
      if (lastSaveRef.current === FileService.updateFile(fileId, contentToSave)) {
        setSaveStatus('error');
        
        // Retry after 5 seconds
        setTimeout(() => {
          if (fileId && enabled) {
            save(contentToSave);
          }
        }, 5000);
      }
    }
  }, [fileId, enabled]);

  const debouncedSave = useCallback((contentToSave: string) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      save(contentToSave);
    }, delay);
  }, [save, delay]);

  useEffect(() => {
    if (!enabled || !fileId) {
      setSaveStatus('saved');
      return;
    }

    // Check if content has changed
    if (content !== lastSavedContent) {
      setSaveStatus('unsaved');
      debouncedSave(content);
    } else {
      setSaveStatus('saved');
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [content, lastSavedContent, debouncedSave, enabled, fileId]);

  // Manual save function
  const forceSave = useCallback(async () => {
    if (!fileId || !enabled) return;

    // Cancel any pending auto-save
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    await save(content);
  }, [fileId, enabled, save, content]);

  // Update last saved content when file changes
  useEffect(() => {
    if (fileId) {
      setLastSavedContent(content);
      setSaveStatus('saved');
    }
  }, [fileId]);

  return {
    saveStatus,
    forceSave,
    hasUnsavedChanges: content !== lastSavedContent,
  };
}