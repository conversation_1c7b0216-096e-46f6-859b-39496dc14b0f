#!/usr/bin/env node

/**
 * Appwrite Database Setup Script for Codeable Platform
 * 
 * This script creates the necessary database collections for the Codeable platform.
 * Run this after setting up your Appwrite project.
 * 
 * Prerequisites:
 * 1. Install Appwrite CLI: npm install -g appwrite-cli
 * 2. Login to Appwrite: appwrite login
 * 3. Set your project: appwrite client setProject <PROJECT_ID>
 * 
 * Usage: node scripts/setup-appwrite.js
 */

const { Client, Databases, Permission, Role } = require('node-appwrite');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const client = new Client()
    .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT)
    .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID)
    .setKey(process.env.APPWRITE_API_KEY); // You'll need to add this to your .env.local

const databases = new Databases(client);

async function setupDatabase() {
    console.log('🚀 Setting up Codeable database...');
    
    try {
        // Create database
        console.log('📂 Creating database...');
        const database = await databases.create(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            'Codeable Database'
        );
        console.log('✅ Database created:', database.name);
    } catch (error) {
        if (error.code === 409) {
            console.log('📂 Database already exists');
        } else {
            console.error('❌ Error creating database:', error.message);
            return;
        }
    }

    // Create Workspaces Collection
    try {
        console.log('📋 Creating Workspaces collection...');
        const workspacesCollection = await databases.createCollection(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_WORKSPACES_COLLECTION_ID,
            'Workspaces',
            [
                Permission.read(Role.user('user')),
                Permission.create(Role.user('user')),
                Permission.update(Role.user('user')),
                Permission.delete(Role.user('user'))
            ]
        );

        // Add attributes
        await databases.createStringAttribute(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_WORKSPACES_COLLECTION_ID,
            'name',
            255,
            true
        );

        await databases.createStringAttribute(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_WORKSPACES_COLLECTION_ID,
            'description',
            1000,
            false
        );

        await databases.createEnumAttribute(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_WORKSPACES_COLLECTION_ID,
            'category',
            ['interview-prep', 'leetcode', 'algorithms', 'system-design', 'general'],
            true
        );

        await databases.createStringAttribute(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_WORKSPACES_COLLECTION_ID,
            'userId',
            255,
            true
        );

        console.log('✅ Workspaces collection created');
    } catch (error) {
        if (error.code === 409) {
            console.log('📋 Workspaces collection already exists');
        } else {
            console.error('❌ Error creating Workspaces collection:', error.message);
        }
    }

    // Create Files Collection
    try {
        console.log('📄 Creating Files collection...');
        const filesCollection = await databases.createCollection(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_FILES_COLLECTION_ID,
            'Files',
            [
                Permission.read(Role.user('user')),
                Permission.create(Role.user('user')),
                Permission.update(Role.user('user')),
                Permission.delete(Role.user('user'))
            ]
        );

        // Add attributes
        await databases.createStringAttribute(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_FILES_COLLECTION_ID,
            'name',
            255,
            true
        );

        await databases.createStringAttribute(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_FILES_COLLECTION_ID,
            'content',
            1000000, // 1MB limit
            false
        );

        await databases.createEnumAttribute(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_FILES_COLLECTION_ID,
            'language',
            ['javascript', 'typescript', 'python', 'java', 'cpp', 'go', 'rust', 'markdown', 'text'],
            true
        );

        await databases.createStringAttribute(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_FILES_COLLECTION_ID,
            'workspaceId',
            255,
            true
        );

        await databases.createStringAttribute(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_FILES_COLLECTION_ID,
            'userId',
            255,
            true
        );

        console.log('✅ Files collection created');
    } catch (error) {
        if (error.code === 409) {
            console.log('📄 Files collection already exists');
        } else {
            console.error('❌ Error creating Files collection:', error.message);
        }
    }

    // Create Sessions Collection
    try {
        console.log('⏱️ Creating Sessions collection...');
        const sessionsCollection = await databases.createCollection(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_SESSIONS_COLLECTION_ID,
            'Practice Sessions',
            [
                Permission.read(Role.user('user')),
                Permission.create(Role.user('user')),
                Permission.update(Role.user('user')),
                Permission.delete(Role.user('user'))
            ]
        );

        // Add attributes
        await databases.createStringAttribute(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_SESSIONS_COLLECTION_ID,
            'fileId',
            255,
            true
        );

        await databases.createStringAttribute(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_SESSIONS_COLLECTION_ID,
            'userId',
            255,
            true
        );

        await databases.createIntegerAttribute(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_SESSIONS_COLLECTION_ID,
            'duration',
            true
        );

        await databases.createStringAttribute(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_SESSIONS_COLLECTION_ID,
            'sessionDate',
            255,
            true
        );

        console.log('✅ Sessions collection created');
    } catch (error) {
        if (error.code === 409) {
            console.log('⏱️ Sessions collection already exists');
        } else {
            console.error('❌ Error creating Sessions collection:', error.message);
        }
    }

    console.log('🎉 Database setup complete!');
    console.log('\n📝 Next steps:');
    console.log('1. Enable Authentication in your Appwrite console');
    console.log('2. Configure OAuth providers if needed');
    console.log('3. Run: npm run dev');
    console.log('4. Test user registration and workspace creation');
}

// Run setup
setupDatabase().catch(console.error);