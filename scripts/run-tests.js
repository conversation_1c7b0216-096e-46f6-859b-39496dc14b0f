#!/usr/bin/env node

/**
 * Test runner script for Codeable
 *
 * This script runs the Jest test suite with various options
 * based on command line arguments.
 */

const { spawn } = require('child_process');
const path = require('path');

// Parse command line arguments
const args = process.argv.slice(2);
let command = 'test';
let testPath = '';
let coverage = false;
let watch = false;
let verbose = false;

// Process arguments
args.forEach(arg => {
  if (arg === '--coverage' || arg === '-c') {
    coverage = true;
  } else if (arg === '--watch' || arg === '-w') {
    watch = true;
  } else if (arg === '--verbose' || arg === '-v') {
    verbose = true;
  } else if (arg.startsWith('--path=')) {
    testPath = arg.split('=')[1];
  } else if (arg === '--help' || arg === '-h') {
    showHelp();
    process.exit(0);
  }
});

// Build the Jest command
const jestArgs = ['test'];

if (coverage) {
  jestArgs.push('--coverage');
}

if (watch) {
  jestArgs.push('--watch');
}

if (verbose) {
  jestArgs.push('--verbose');
}

if (testPath) {
  jestArgs.push(testPath);
}

console.log(`\n🧪 Running tests with options: ${jestArgs.join(' ')}\n`);

// Run Jest
const jest = spawn('npm', jestArgs, {
  stdio: 'inherit',
  shell: true
});

jest.on('close', code => {
  if (code === 0) {
    console.log('\n✅ Tests completed successfully!\n');
  } else {
    console.error(`\n❌ Tests failed with exit code ${code}\n`);
    process.exit(code);
  }
});

// Help function
function showHelp() {
  console.log(`
🧪 Codeable Test Runner

Usage:
  node scripts/run-tests.js [options]

Options:
  --coverage, -c     Generate code coverage report
  --watch, -w        Run tests in watch mode
  --verbose, -v      Run tests with verbose output
  --path=<path>      Run tests in specific path
  --help, -h         Show this help message

Examples:
  node scripts/run-tests.js                         Run all tests
  node scripts/run-tests.js --coverage              Run tests with coverage
  node scripts/run-tests.js --watch                 Run tests in watch mode
  node scripts/run-tests.js --path=src/tests/api    Run tests in specific directory
  `);
}
