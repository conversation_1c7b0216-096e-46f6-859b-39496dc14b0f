#!/usr/bin/env node

/**
 * <PERSON>ript to run a specific test file or test pattern
 *
 * Usage:
 * node scripts/run-specific-test.js <test-file-pattern>
 *
 * Example:
 * node scripts/run-specific-test.js browser-executor
 * node scripts/run-specific-test.js integration
 */

const { spawn } = require('child_process');
const path = require('path');

// Get the test pattern from command line arguments
const args = process.argv.slice(2);
const testPattern = args[0] || '';
const watchMode = args.includes('--watch') || args.includes('-w');
const verbose = args.includes('--verbose') || args.includes('-v');

if (!testPattern) {
  console.error('❌ Error: Please provide a test file pattern');
  console.log('Usage: node scripts/run-specific-test.js <test-file-pattern> [--watch] [--verbose]');
  process.exit(1);
}

// Build the Jest command
const jestArgs = ['test', testPattern];

if (watchMode) {
  jestArgs.push('--watch');
}

if (verbose) {
  jestArgs.push('--verbose');
}

console.log(`\n🧪 Running tests matching pattern: "${testPattern}"\n`);

// Run Jest
const jest = spawn('npm', jestArgs, {
  stdio: 'inherit',
  shell: true
});

jest.on('close', code => {
  if (code === 0) {
    console.log('\n✅ Tests completed successfully!\n');
  } else {
    console.error(`\n❌ Tests failed with exit code ${code}\n`);
    process.exit(code);
  }
});
