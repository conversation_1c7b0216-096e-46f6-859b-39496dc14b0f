#!/usr/bin/env node

/**
 * Automated Appwrite Database Setup for Codeable Platform
 * 
 * This script creates the database and collections programmatically.
 * You'll need an API key with proper permissions.
 * 
 * Usage: 
 * 1. Get your API key from Appwrite Console > Settings > API Keys
 * 2. Add APPWRITE_API_KEY to your .env.local
 * 3. Run: node scripts/setup-appwrite-db.js
 */

const sdk = require('node-appwrite');
require('dotenv').config({ path: '.env.local' });

// Initialize the client
const client = new sdk.Client();

client
    .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT)
    .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID)
    .setKey(process.env.APPWRITE_API_KEY); // You need to add this to .env.local

const databases = new sdk.Databases(client);

async function setupDatabase() {
    console.log('🚀 Setting up Codeable database and collections...\n');

    try {
        // Create database
        console.log('📂 Creating database...');
        await databases.create(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            'Codeable Database'
        );
        console.log('✅ Database created successfully\n');
    } catch (error) {
        if (error.code === 409) {
            console.log('📂 Database already exists\n');
        } else {
            console.error('❌ Error creating database:', error.message);
            return;
        }
    }

    // Create Workspaces Collection
    try {
        console.log('📋 Creating Workspaces collection...');
        
        const workspacesCollection = await databases.createCollection(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_WORKSPACES_COLLECTION_ID,
            'Workspaces',
            [
                sdk.Permission.read(sdk.Role.users()),
                sdk.Permission.create(sdk.Role.users()),
                sdk.Permission.update(sdk.Role.users()),
                sdk.Permission.delete(sdk.Role.users())
            ]
        );

        // Add attributes to Workspaces
        console.log('  Adding name attribute...');
        await databases.createStringAttribute(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_WORKSPACES_COLLECTION_ID,
            'name',
            255,
            true
        );

        console.log('  Adding description attribute...');
        await databases.createStringAttribute(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_WORKSPACES_COLLECTION_ID,
            'description',
            1000,
            false
        );

        console.log('  Adding category attribute...');
        await databases.createEnumAttribute(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_WORKSPACES_COLLECTION_ID,
            'category',
            ['interview-prep', 'leetcode', 'algorithms', 'system-design', 'general'],
            true
        );

        console.log('  Adding userId attribute...');
        await databases.createStringAttribute(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_WORKSPACES_COLLECTION_ID,
            'userId',
            255,
            true
        );

        console.log('✅ Workspaces collection created successfully\n');
    } catch (error) {
        if (error.code === 409) {
            console.log('📋 Workspaces collection already exists\n');
        } else {
            console.error('❌ Error creating Workspaces collection:', error.message, '\n');
        }
    }

    // Create Files Collection
    try {
        console.log('📄 Creating Files collection...');
        
        const filesCollection = await databases.createCollection(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_FILES_COLLECTION_ID,
            'Files',
            [
                sdk.Permission.read(sdk.Role.users()),
                sdk.Permission.create(sdk.Role.users()),
                sdk.Permission.update(sdk.Role.users()),
                sdk.Permission.delete(sdk.Role.users())
            ]
        );

        // Add attributes to Files
        console.log('  Adding name attribute...');
        await databases.createStringAttribute(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_FILES_COLLECTION_ID,
            'name',
            255,
            true
        );

        console.log('  Adding content attribute...');
        await databases.createStringAttribute(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_FILES_COLLECTION_ID,
            'content',
            1000000, // 1MB limit
            false
        );

        console.log('  Adding language attribute...');
        await databases.createEnumAttribute(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_FILES_COLLECTION_ID,
            'language',
            ['javascript', 'typescript', 'python', 'java', 'cpp', 'go', 'rust', 'markdown', 'text'],
            true
        );

        console.log('  Adding workspaceId attribute...');
        await databases.createStringAttribute(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_FILES_COLLECTION_ID,
            'workspaceId',
            255,
            true
        );

        console.log('  Adding userId attribute...');
        await databases.createStringAttribute(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_FILES_COLLECTION_ID,
            'userId',
            255,
            true
        );

        console.log('✅ Files collection created successfully\n');
    } catch (error) {
        if (error.code === 409) {
            console.log('📄 Files collection already exists\n');
        } else {
            console.error('❌ Error creating Files collection:', error.message, '\n');
        }
    }

    // Create Sessions Collection
    try {
        console.log('⏱️ Creating Sessions collection...');
        
        const sessionsCollection = await databases.createCollection(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_SESSIONS_COLLECTION_ID,
            'Practice Sessions',
            [
                sdk.Permission.read(sdk.Role.users()),
                sdk.Permission.create(sdk.Role.users()),
                sdk.Permission.update(sdk.Role.users()),
                sdk.Permission.delete(sdk.Role.users())
            ]
        );

        // Add attributes to Sessions
        console.log('  Adding fileId attribute...');
        await databases.createStringAttribute(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_SESSIONS_COLLECTION_ID,
            'fileId',
            255,
            true
        );

        console.log('  Adding userId attribute...');
        await databases.createStringAttribute(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_SESSIONS_COLLECTION_ID,
            'userId',
            255,
            true
        );

        console.log('  Adding duration attribute...');
        await databases.createIntegerAttribute(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_SESSIONS_COLLECTION_ID,
            'duration',
            true
        );

        console.log('  Adding sessionDate attribute...');
        await databases.createStringAttribute(
            process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
            process.env.NEXT_PUBLIC_SESSIONS_COLLECTION_ID,
            'sessionDate',
            255,
            true
        );

        console.log('✅ Sessions collection created successfully\n');
    } catch (error) {
        if (error.code === 409) {
            console.log('⏱️ Sessions collection already exists\n');
        } else {
            console.error('❌ Error creating Sessions collection:', error.message, '\n');
        }
    }

    console.log('🎉 Database setup complete!');
    console.log('\n📝 Next steps:');
    console.log('1. Enable Authentication in your Appwrite console');
    console.log('2. Configure OAuth providers if needed');
    console.log('3. Run: npm run dev');
    console.log('4. Test user registration and workspace creation');
}

// Check if API key is provided
if (!process.env.APPWRITE_API_KEY) {
    console.error('❌ APPWRITE_API_KEY is required in .env.local');
    console.log('\n📝 To get your API key:');
    console.log('1. Go to Appwrite Console > Settings > API Keys');
    console.log('2. Create a new API key with Database permissions');
    console.log('3. Add APPWRITE_API_KEY=your_key_here to .env.local');
    process.exit(1);
}

// Run setup
setupDatabase().catch(console.error);