// Web Worker for isolated code execution
self.onmessage = function(e) {
  const { code, language, timeout = 5000 } = e.data;

  const startTime = Date.now();
  let output = '';
  let error = '';

  // Set up timeout
  const timeoutId = setTimeout(() => {
    self.postMessage({
      output: '',
      error: 'Execution timeout exceeded',
      executionTime: Date.now() - startTime,
    });
  }, timeout);

  try {
    // Capture console output
    const logs = [];
    const console = {
      log: (...args) => logs.push(args.join(' ')),
      error: (...args) => logs.push('ERROR: ' + args.join(' ')),
      warn: (...args) => logs.push('WARN: ' + args.join(' ')),
    };

    if (language === 'javascript') {
      // Execute JavaScript
      const func = new Function('console', code);
      func(console);
      output = logs.join('\n');
    } else if (language === 'python') {
      // For Python, you'd need Pyodide or similar
      output = 'Python execution not implemented in this example';
    }

  } catch (err) {
    error = err.message;
  } finally {
    clearTimeout(timeoutId);
  }

  self.postMessage({
    output,
    error,
    executionTime: Date.now() - startTime,
  });
};
