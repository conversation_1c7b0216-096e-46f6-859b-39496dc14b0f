{"name": "codeable", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "setup:database": "node scripts/setup-appwrite-db.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:run": "node scripts/run-tests.js"}, "dependencies": {"@monaco-editor/react": "^4.7.0", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-tabs": "^1.1.12", "@types/uuid": "^10.0.0", "appwrite": "^18.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "next": "15.4.2", "ogl": "^1.0.11", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "dotenv": "^16.6.1", "eslint": "^9", "eslint-config-next": "15.4.2", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "node-appwrite": "^15.0.1", "tailwindcss": "^3.4.17", "typescript": "^5"}}