# Development files
*.md
!README.md

# Test files
src/tests/
jest.config.js
**/*.test.js
**/*.test.ts
**/*.test.tsx
**/*.spec.js
**/*.spec.ts
**/*.spec.tsx

# Scripts (except necessary ones)
scripts/run-tests.js
scripts/run-specific-test.js

# Build artifacts
tsconfig.tsbuildinfo
.next/
dist/
build/

# Development dependencies
node_modules/

# Environment files (will be set in Vercel dashboard)
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Coverage reports
coverage/
*.lcov

# Temporary files
tmp/
temp/
